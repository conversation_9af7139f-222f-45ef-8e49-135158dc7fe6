package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.controlcenter.beans.ADKnownCompAttrBean;
import com.appnomic.appsone.controlcenter.beans.ComponentFilesDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Host;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.*;
import com.appnomic.appsone.controlcenter.common.autodiscovery.Entity;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AutoDiscoveryIgnoreBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.autodiscovery.DiscoveredAttributes;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.autodiscovery.HostWiseConnections;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.AutoDiscoveryDao;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.AgentDetails;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.InstanceComponentMappingDetails;
import com.appnomic.appsone.controlcenter.pojo.InstanceServiceApplicationDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.AutoDiscoComponentMapping;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.HostProcessCountPojo;
import lombok.extern.slf4j.Slf4j;
import org.skife.jdbi.v2.Handle;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class AutoDiscoveryDataService extends AbstractDaoService<AutoDiscoveryDao>{

    public List<Host> getHostList(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getHostList();
        } catch (Exception e) {
            log.error("Exception while getting host list from 'autodisco_host' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<Host> getNotIgnoredHostList(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getNotIgnoredHostList();
        } catch (Exception e) {
            log.error("Exception while getting not ignored host list from 'autodisco_host' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<InstanceServiceApplicationDetailsPojo> getHostInstanceServiceAppDetails(Handle handle) {

        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getHostInstanceServiceAppDetails();
        } catch (Exception e) {
            log.error("Exception while getting host, service, app details from 'autodisco_host' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<InstanceServiceApplicationDetailsPojo> getComponentInstanceServiceAppDetails(Handle handle) {

        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getComponentInstanceServiceAppDetails();
        } catch (Exception e) {
            log.error("Exception while getting host, service, app details from 'autodisco_host' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<HostProcessCountPojo> getNewProcessCount(Handle handle) {

        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getNewProcessCount();
        } catch (Exception e) {
            log.error("Exception while getting new discovered process count for the hosts : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<NetworkInterface> getNetworkInterfacesList(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getNetworkInterfacesList();
        } catch (Exception e) {
            log.error("Exception while getting network interfaces list from 'autodisco_network_interfaces' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<HostWiseConnections> getNumberOfConnectionsForHost(int accountId, int healHealthAccountId, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getNumberOfConnectionsForHostIpAddress(accountId, healHealthAccountId);
        } catch (Exception e) {
            log.error("Exception while getting network interfaces list from 'autodisco_network_interfaces' table for all the host IP addresses", e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<HostWiseConnections> getNumberOfConnectionsForHostIdentifier(int accountId, int healHealthAccountId, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getNumberOfConnectionsForHostIdentifier(accountId, healHealthAccountId);
        } catch (Exception e) {
            log.error("Exception while getting network interfaces list from 'autodisco_network_interfaces' table for all the host identifiers", e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<Process> getProcessList(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getProcessList();
        } catch (Exception e) {
            log.error("Exception while getting process list from 'autodisco_process' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<AutoDiscoveryServiceMapping> getServiceMappingList(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getServiceMappingList();
        } catch (Exception e) {
            log.error("Exception while getting service mapping list from 'autodisco_service_mapping' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return null;
    }

    public List<AutoDiscoveryDiscoveredAttributes> getDiscoveredAttributesList(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getDiscoveredAttributesList();
        } catch (Exception e) {
            log.error("Exception while getting attributes list from 'autodisco_discovered_attributes' table : {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<InstanceComponentMappingDetails> getInstCompAttributeMapping(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getInstCompAttributeMapping();
        } catch (Exception e) {
            log.error("Exception while getting component instance and attributes list for host: {}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<AutoDiscoveryDiscoveredConnections> getDiscoveredConnectionsList(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getDiscoveredConnectionsList();
        } catch (Exception e) {
            log.error("Error occurred while getting the connection details from 'discovered_connections' table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<AutoDiscoveryDiscoveredConnections> getUsefulDiscoveredConnections(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getUsefulDiscoveredConnections();
        } catch (Exception e) {
            log.error("Error occurred while getting the useful connection details from 'discovered_connections' table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return new ArrayList<>();
    }

    public List<AutoDiscoveryDiscoveredConnections> getDiscoveredConnectionsListByHost(String hostIdentifier, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getDiscoveredConnectionsListByHost(hostIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting the connection details from 'discovered_connections' table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
        return Collections.emptyList();
    }

    public List<Connection> getNetworkConnections(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getNetworkConnections();
        } catch (Exception e) {
            log.error("Exception encountered while fetching network connections. err :{}", e.getMessage());
            return Collections.emptyList();
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public List<Endpoint> getEndpoints(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getEndpoints();
        } catch (Exception e) {
            log.error("Exception encountered while fetching endpoints. err :{}", e.getMessage());
            return Collections.emptyList();
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public void addDiscoveredConnection(List<AutoDiscoveryDiscoveredConnections> discoveredConnectionsPojo, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            dao.addDiscoveredConnections(discoveredConnectionsPojo);
        } catch (Exception e) {
            log.error("Exception while adding discovered connections to table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void addServiceMapping(List<AutoDiscoveryServiceMapping> autoDiscoveryEntityBeanList, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            dao.addServiceMapping(autoDiscoveryEntityBeanList);
        } catch (Exception e) {
            log.error("Exception while adding entity/entities to autodisco_service_mapping table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void setLastUpdatedTime(List<AutoDiscoveryServiceMapping> mappings, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try{
            if (mappings.get(0).getEntityType().equals(Entity.Host)) {
                dao.setHostLastUpdatedTime(mappings);
                dao.setConnLastUpdatedTime(mappings);
            } else if(mappings.get(0).getEntityType().equals(Entity.CompInstance)) {
                dao.setCompInstanceLastUpdateTime(mappings);
            }
        } catch (Exception e) {
            log.error("Exception while modifying entity(s) last updated time in table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void setHostIDIsIgnore(List<AutoDiscoveryIgnoreBean> hostIDsList, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            dao.setHostIsIgnore(hostIDsList);
        } catch (Exception e) {
            log.error("Exception while modifying host(s) is_ignored status in table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void setProcessIDIsIgnore(List<AutoDiscoveryIgnoreBean> processIDsList, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            dao.setProcessIsIgnore(processIDsList);
        } catch (Exception e) {
            log.error("Exception while modifying process(s) is_ignored status in table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<Controller> validateServiceIdentifier(int[] id, Integer accountId) throws DataProcessingException {
        List<Controller> controllerList = MasterDataService.getControllerList(accountId);
        List<Controller> clist = new ArrayList<>();
        for (int sid : id) {
            Controller controller = controllerList.stream()
                    .filter(control -> Integer.parseInt(control.getAppId()) == sid)
                    .findFirst().orElse(null);
            if (controller == null) {
                String msg = "Service id not found in controller table. id: " + sid;
                log.error(msg);
                throw new DataProcessingException(msg);
            }
            clist.add(controller);
        }
        return clist;
    }

    public void validateIdentifiers(Entity entityType, List<String> validIDs, Map<String, String> messages) throws DataProcessingException {
        // checks if host or component instance identifiers are present in the DB
        AutoDiscoveryDataService adDataService = new AutoDiscoveryDataService();
        Host hostIdentifiers;
        Process processIdentifiers;
        if (entityType.equals(Entity.Host)) {
            List<Host> identifiersBeanList = adDataService.getHostList(null);
            if(identifiersBeanList == null) throw new DataProcessingException("Error while fetching known host(s) data.");
            int count = 0;

            for (String id : validIDs) {
                hostIdentifiers = identifiersBeanList.stream()
                        .filter(identifier -> identifier.getHostIdentifier().equals(id))
                        .findAny().orElse(null);
                if (hostIdentifiers == null) {
                    String msg = "Host identifier not found in autodisco_host table." + id;
                    log.error(msg);
                    messages.put(id, msg);
                    count++;
                }
            }
            /**
            if (count == validIDs.length) {
                String msg = "None of the host identifiers are valid.";
                log.error(msg);
                throw new DataProcessingException(msg);
            }
             */

        } else if (entityType.equals(Entity.CompInstance)) {
            List<Process> identifiersBeanList = adDataService.getProcessList(null);
            if(identifiersBeanList == null) throw new DataProcessingException("Error while fetching known process(s) data.");
            int count = 0;

            for (String id : validIDs) {
                processIdentifiers = identifiersBeanList.stream()
                        .filter(identifier -> identifier.getProcessIdentifier().equals(id))
                        .findAny().orElse(null);
                if (processIdentifiers == null) {
                    String msg = "Process identifier not found in autodisco_process table." + id;
                    log.error(msg);
                    messages.put(id, msg);
                }
            }
            /**
            if (count == validIDs.length) {
                String msg = "None of the process identifiers are valid.";
                log.error(msg);
                throw new DataProcessingException(msg);
            }
             */
        }
    }

    public List<AutoDiscoveryIgnoredEntities> getIgnoredHosts() {
        AutoDiscoveryDao dao = getDaoConnection(null, AutoDiscoveryDao.class);
        try {
            return dao.getIgnoredHosts();
        } catch (Exception e) {
            log.error("Exception while fetching ignored hosts. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(null, dao);
        }
        return new ArrayList<>();
    }

    public List<AutoDiscoveryIgnoredEntities> getIgnoredProcesses() {
        AutoDiscoveryDao dao = getDaoConnection(null, AutoDiscoveryDao.class);
        try {
            return dao.getIgnoredProcesses();
        } catch (Exception e) {
            log.error("Exception while fetching ignored processes. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(null, dao);
        }
        return new ArrayList<>();
    }
    // If hosts service is modified
    public void deleteExistingMappingsAndConnections(String validEntity, List<String> existingCompMappings, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            if (!existingCompMappings.isEmpty()) {
                dao.deleteHostMapKeepCompMappings(validEntity, existingCompMappings);
            } else {
                dao.deleteEntityService(validEntity);
            }
            dao.deleteDiscoveredConnections(validEntity);
        } catch (Exception e) {
            log.error("Exception while deleting existing mappings from the table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
    }
    // If comp_instance service is modified
    public void deleteExistingMappingsAndConnections(String validEntity, List<String> mappings, String hostIdentifier, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            dao.deleteEntityService(validEntity);
            for (String map : mappings) {
                dao.deleteHostMapToCompService(hostIdentifier, map);
            }
            dao.deleteDiscoveredConnections(hostIdentifier);
        } catch (Exception e) {
            log.error("Exception while deleting existing mappings from the table. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    /** Add To System */
    public List<Host> getHostListByIdentifier(List<String> identifiers, Handle handle) throws DataProcessingException {

        AutoDiscoveryDao autoDiscoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            //TODO: Move this method to BindInDao class.
            return autoDiscoveryDao.getHostListByIdentifier(identifiers);
        } catch (Exception e) {
            log.error("Error while fetching the host with identifiers: [{}]", identifiers.toString(), e);
            throw new DataProcessingException("Error while fetching the host.");
        } finally {
            closeDaoConnection(handle, autoDiscoveryDao);
        }
    }

    public List<Endpoint> getHostEndpointByIdentifier(String identifier, Handle handle) throws DataProcessingException {
        AutoDiscoveryDao autoDiscoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return autoDiscoveryDao.getHostEndpointByIdentifier(identifier);
        } catch (Exception e) {
            log.error("Error while fetching the endpoint with identifier: [{}]", identifier, e);
            throw new DataProcessingException("Error while fetching the endpoint.");
        } finally {
            closeDaoConnection(handle, autoDiscoveryDao);
        }
    }

    public List<Connection> getHostNetworkConnectionsByIdentifier(String identifier, Handle handle) throws DataProcessingException {
        AutoDiscoveryDao autoDiscoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return autoDiscoveryDao.getHostNetworkConnectionsByIdentifier(identifier);
        } catch (Exception e) {
            log.error("Error while fetching the Connection with identifier: [{}]", identifier, e);
            throw new DataProcessingException("Error while fetching Connection.");
        } finally {
            closeDaoConnection(handle, autoDiscoveryDao);
        }
    }

    public List<Process> getHostProcessByIdentifier(String identifier, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao autoDiscoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return autoDiscoveryDao.getHostProcessByIdentifier(identifier);
        } catch (Exception e) {
            log.error("Error while fetching the process with identifier: [{}]", identifier, e);
            throw new ControlCenterException("Error while fetching process.");
        } finally {
            closeDaoConnection(handle, autoDiscoveryDao);
        }
    }

    public List<NetworkInterface> getHostNetworkInterfacesByIdentifier(String identifier, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao autoDiscoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return autoDiscoveryDao.getHostNetworkInterfacesByIdentifier(identifier);
        } catch (Exception e) {
            log.error("Error while fetching the NetworkInterface with identifier: [{}]", identifier, e);
            throw new ControlCenterException("Error while fetching NetworkInterface.");
        } finally {
            closeDaoConnection(handle, autoDiscoveryDao);
        }
    }

    public List<AutoDiscoveryServiceMapping> getEntityServiceMapping(String mappingIdentifier, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao autoDiscoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return autoDiscoveryDao.getEntityServiceMapping(mappingIdentifier);
        } catch (Exception e) {
            log.error("Error while fetching entity service mapping with mappingIdentifier: [{}]", mappingIdentifier, e);
            throw new ControlCenterException("Error while fetching entity service mapping.");
        } finally {
            closeDaoConnection(handle, autoDiscoveryDao);
        }
    }

    public List<DiscoveredAttributes> getDiscoveredAttributes(String discoveredAttributesIdentifier, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao autoDiscoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return autoDiscoveryDao.getDiscoveredAttributes(discoveredAttributesIdentifier);
        } catch (Exception e) {
            log.error("Error while fetching discovered_attributes_identifier with discoveredAttributesIdentifier: [{}]", discoveredAttributesIdentifier, e);
            throw new ControlCenterException("Error while fetching entity service mapping.");
        } finally {
            closeDaoConnection(handle, autoDiscoveryDao);
        }
    }

    public List<MountPoint> getHostMountPointByIdentifier(String identifier, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao autoDiscoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return autoDiscoveryDao.getHostMountPointByIdentifier(identifier);
        } catch (Exception e) {
            log.error("Error while fetching mount points with identifier: [{}]", identifier, e);
            throw new ControlCenterException("Error while fetching mount points.");
        } finally {
            closeDaoConnection(handle, autoDiscoveryDao);
        }
    }

    /** Add To System End*/


    /**
     * Auto Discovery (AD): Fetches 'discovery_pattern' and 'attributes' data of known components
     */
    public List<ADKnownCompAttrBean> getADComponentAttributeDetails(){
        AutoDiscoveryDao dao = getDaoConnection(null, AutoDiscoveryDao.class);
        try {
            return dao.getADComponentAttributeDetail();
        }catch (Exception e){
            log.error("Error occurred while getting component attribute details" + e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<ComponentFilesDetailsBean> getConfigurationEntities(){
        AutoDiscoveryDao dao = getDaoConnection(null, AutoDiscoveryDao.class);
        try {
            return dao.getConfigurationEntities();
        }catch (Exception e){
            log.error("Error while fetching configuration entities" + e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    /*Push Discovery Data*/

    public void pushDiscoveredHosts(Host host, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Add discovery data in host. {}", host);
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredHosts(host);
        } catch (Exception e) {
            log.error("Error occurred while adding host. {}", host, e);
            throw new ControlCenterException(e, "Error occurred while adding host.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void pushDiscoveredEndpoints(List<Endpoint> endpoints, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Add discovery data in endpoint.");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredEndpoints(endpoints);
        } catch (Exception e) {
            log.error("Error occurred while adding endpoint. {}", endpoints, e);
            throw new ControlCenterException(e, "Error occurred while adding endpoint.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void pushDiscoveredConnections(List<Connection> connections, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Add discovery data in endpoint.");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredConnections(connections);
        } catch (Exception e) {
            log.error("Error occurred while adding connections. {}", connections, e);
            throw new ControlCenterException(e, "Error occurred while adding connections.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void pushDiscoveredProcesses(List<Process> processes, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Add discovery data in endpoint.");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredProcesses(processes);
        } catch (Exception e) {
            log.error("Error occurred while adding processes. {}", processes, e);
            throw new ControlCenterException(e, "Error occurred while connections.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void pushDiscoveredNetworkInterface(List<NetworkInterface> networkInterfaces, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Add discovery data in endpoint.");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredNetworkInterface(networkInterfaces);
        } catch (Exception e) {
            log.error("Error occurred while adding network_interface. {}", networkInterfaces, e);
            throw new ControlCenterException(e, "Error occurred while adding network_interface.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void pushDiscoveredMountPoint(List<MountPoint> mountPoints, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Add discovery data in endpoint.");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredMountPoint(mountPoints);
        } catch (Exception e) {
            log.error("Error occurred while adding mount_points. {}", mountPoints, e);
            throw new ControlCenterException(e, "Error occurred while adding mount_points.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void pushDiscoveredAttributes(List<AutoDiscoveryDiscoveredAttributes> attributes, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Add discovery data in endpoint.");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredAttributes(attributes);
        } catch (Exception e) {
            log.error("Error occurred while adding attributes. {}", attributes, e);
            throw new ControlCenterException(e, "Error occurred while adding attributes.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void pushDiscoveredConfigurationEntities(List<AutoDiscoveredConfigurationEntities> configurationEntities, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Adding auto discovered configuration entities to DB");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredConfigurationEntities(configurationEntities);
        } catch (Exception e) {
            log.error("Error occurred while adding configuration entities {} - {}", configurationEntities, e);
            throw new ControlCenterException(e, "Error occurred while adding configuration entities.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public List<AutoDiscoveredConfigurationEntities> getDiscoveredConfigurationEntities(String hostIdentifier, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Adding auto discovered configuration entities to DB");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            return discoveryDao.getDiscoveredConfigurationEntities(hostIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while fetching configuration entities {} - {}", hostIdentifier, e);
            throw new ControlCenterException(e, "Error occurred while fetching configuration entities");
        }
    }

    public void pushDiscoveredPreRequisites(List<AutoDiscoveredPreRequisites> discoveredPreRequisitesList, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Adding auto discovered prerequisites to DB");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredPreRequisites(discoveredPreRequisitesList);
        } catch (Exception e) {
            log.error("Error occurred while adding prerequisites {} - {}", discoveredPreRequisitesList, e);
            throw new ControlCenterException(e, "Error occurred while adding prerequisites.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void pushDiscoveredPortAvailability(List<AutoDiscoveredPortAvailability> portAvailabilityList, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Adding auto discovered port availability to DB");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredPortAvailability(portAvailabilityList);
        } catch (Exception e) {
            log.error("Error occurred while adding port availability {} - {}", portAvailabilityList, e);
            throw new ControlCenterException(e, "Error occurred while adding port availability.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void deleteDiscoveredAttributes(List<Integer> discoveredAttributesIdentifier, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.deleteDiscoveredAttributes(discoveredAttributesIdentifier);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ControlCenterException("Error while deleting discovered attributes.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    /*Push Discovery Data END*/

    public void setHostDiscoveryStatusTimeAccount(String lastUpdatedTime, String discoveryStatus, String hostIdentifier, int accountId, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.setHostDiscoveryStatusTimeAccount(lastUpdatedTime, discoveryStatus, hostIdentifier, accountId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ControlCenterException("Error while updating status of host.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void setProcessDiscoveryStatusTimeAccount(String lastUpdatedTime, String discoveryStatus, String processIdentifier, int accountId, Handle handle) throws ControlCenterException {
        AutoDiscoveryDao discoveryDao = null;
        try {
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.setProcessDiscoveryStatusTimeAccount(lastUpdatedTime, discoveryStatus, processIdentifier, accountId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ControlCenterException("Error while updating status of processes.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }

    public void setEnvironment(List<String> validEntities, int environmentId) {
        AutoDiscoveryDao dao = getDaoConnection(null, AutoDiscoveryDao.class);
        try {
            dao.setEnvironmentOfHost(validEntities, environmentId);
        } catch (Exception e) {
            log.error("Exception while setting environment of host. err :{}", e.getMessage());
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public ComponentInstanceBean getCompInstForAccountByHostId(String hostAddress, int mstComponentTypeId, int isCluster, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getCompInstForAccountByHostId(hostAddress, mstComponentTypeId, isCluster);
        } catch (Exception e) {
            log.error("Exception while getting host by host address from table comp_instance." + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(dao);
        }
        return null;
    }

    public List<AgentDetails> getAllAgentDetails(Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getAllAgentDetails();
        } catch (Exception e) {
            log.error("Exception while getting agent from table autodisco_agents." +  e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(dao);
        }
        return null;
    }

    public void pushDiscoveryAgentDetails(List<AgentDetails> agents, Handle handle) throws ControlCenterException{
        AutoDiscoveryDao discoveryDao = null;
        try {
            log.debug("Add discovery data in agent.");
            discoveryDao = getDaoConnection(handle, AutoDiscoveryDao.class);
            discoveryDao.pushDiscoveredAgent(agents);
        } catch (Exception e) {
            log.error("Exception while adding agent by host address for the table autodisco_agents." +  e.getMessage(), e);
            throw new ControlCenterException(e, "Error occurred while adding agent.");
        } finally {
            closeDaoConnection(handle, discoveryDao);
        }
    }
    public List<AgentDetails> getDiscoveryAgentForHostIdentifier(String hostIdentifier, Handle handle) {
        AutoDiscoveryDao dao = getDaoConnection(handle, AutoDiscoveryDao.class);
        try {
            return dao.getAgentDetailsForHostIdentifier(hostIdentifier);
        } catch (Exception e) {
            log.error("Exception while getting agent  for host identifier."  + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(dao);
        }
        return Collections.emptyList();
    }
    public List<Host> getHostDetailsByHostAddress(String hostIdentifier) {
        AutoDiscoveryDao dao = getDaoConnection(null, AutoDiscoveryDao.class);
        try {
            return dao.getHostListByHostIdentifier(hostIdentifier);
        } catch (Exception e) {
            log.error("Exception while getting host detail for hostAddress."  + e.getMessage(), e);
        } finally {
            MySQLConnectionManager.getInstance().close(dao);
        }
        return null;
    }

    public List<AutoDiscoComponentMapping> getAutoDiscoComponentMapping() {
        AutoDiscoveryDao dao = getDaoConnection(null, AutoDiscoveryDao.class);
        try {
            return dao.getAutoDiscoComponentMapping();
        }catch (Exception e){
            log.error("Error occurred while getting auto disco component mapping details", e);
        }
        return Collections.emptyList();
    }
}

