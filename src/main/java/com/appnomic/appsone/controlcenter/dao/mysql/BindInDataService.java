package com.appnomic.appsone.controlcenter.dao.mysql;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.*;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.heal.configuration.pojos.TransactionGroup;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.*;

public class BindInDataService extends AbstractDaoService<BindInDao> {

    private final Logger LOGGER = LoggerFactory.getLogger(BindInDataService.class);

    public List<ComponentInstanceAttributesBean> getCompInstBasedInAttributeValues(Handle handle, List<String> attributesVal, int accountId, int mstComponentId) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getCompInstBasedInAttributeValues(attributesVal, accountId, mstComponentId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instance ids -" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }

        return Collections.emptyList();
    }

    public List<ComponentInstanceBean> getComponentInstanceBeans(List<String> identifiers, int accountId, Handle handle) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getComponentInstances(identifiers, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instances-" + identifiers + ":" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return Collections.emptyList();
    }


    public List<ComptInstancePojo> getCompInstanceDetailsForNonHost(List<Integer> instanceIds, Handle handle) {
        BindInDao componentInstanceDao = getDaoConnection(handle, BindInDao.class);
        try {
            return componentInstanceDao.getCompInstanceDetailsForNonHost(instanceIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting group data from view common version and producer kpis table", e);
        } finally {
            closeDaoConnection(handle, componentInstanceDao);
        }
        return new ArrayList<>();
    }

    public List<CompInstAttributesBean> getComponentInstanceAttributeByName(Set<Integer> compInstanceId, String attributeName, Handle handle) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getComponentInstanceAttributeByName(attributeName, compInstanceId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting attribute {} for component instance {} :", attributeName, compInstanceId, e);
        }
        closeDaoConnection(handle, dao);
        return null;
    }

    public List<Controller> getControllerDetailsWithIdentifier(int accountId, List<String> controllerIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getControllerDetailsWithIdentifier(accountId, controllerIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting service list details" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return new ArrayList<>();
    }

    public List<Integer> getControllerDetailsFromTagMapping(int accountId, List<Integer> applicationIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getControllerDetailsFromTagMapping(accountId, applicationIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting service list details" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return new ArrayList<>();
    }

    public List<Integer> getTransactionsFromTagMapping(int accountId, List<Integer> serviceIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getTransactionsFromTagMapping(accountId, serviceIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting service list details" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return new ArrayList<>();
    }

    public List<String> getControllerFromId(List<Integer> controllerIds) {
        if (controllerIds == null || controllerIds.isEmpty()) {
            return Collections.emptyList();
        }
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getControllerIdentifiersFromId(controllerIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting controller list" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return Collections.emptyList();
    }

    public Set<AgentBean> getAgentTypeList(Set<Integer> agentIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getAgentTypeList(agentIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting agent data from table" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return Collections.emptySet();
    }

    public List<AgentCompInstMappingBean> getAgentCompInstMapping(List<Integer> agentIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getAgentCompInstMapping(agentIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting agent comp inst mapping data from table" +
                    " " + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return Collections.emptyList();
    }

    public List<Integer> getInstancesWithClusterIds(int accountId, List<Integer> instanceIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getInstances(accountId, instanceIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting agent data from table" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return Collections.emptyList();
    }

    public void deleteApplicationPercentiles(List<Integer> deletePercentiles, Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            dao.deletePercentilesForApplication(deletePercentiles);
        } catch (Exception e) {
            LOGGER.error("Error while deleting application percentiles from 'application_percentiles' table." + e.getMessage(), e);
            throw new ControlCenterException("Error while deleting application percentiles from 'application_percentiles' table.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<ProcessHostDetailsBean> getHostDetailsForProcesses(List<Integer> batchProcessIds) throws ControlCenterException {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getHostDetailsForProcesses(batchProcessIds);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while fetching host details for batch processes. Reason: ", e);
            throw new ControlCenterException("Error while fetching host details for batch processes.");
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public List<KpiCategoryMapping> getCategoryDetailsForKpis(List<Integer> kpiIds) throws ControlCenterException {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getCategoryDetailsForKpis(kpiIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting Categories." + e.getMessage(), e);
            throw new ControlCenterException("Exception while getting Categories.");
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public List<CountBean> getKpiCountForCategories(List<Integer> categoryIds) throws ControlCenterException {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getKpiCountForCategories(categoryIds);
        } catch (Exception e) {
            LOGGER.error("Exception while getting Categories." + e.getMessage(), e);
            throw new ControlCenterException("Error while fetching kpi count for categories");
        } finally {
            closeDaoConnection(null, dao);
        }
    }

    public void updateInfoForMappedKPIs(List<Integer> KPIs, Integer isInfo, Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            dao.updateInfoForMappedKPIs(KPIs, isInfo, DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new ControlCenterException("Error in updating is_informative for KPIs mapped to the category " +
                    "while updating the category details.");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<Integer> getControllerIdFromIdentifiers(List<String> controllerIdentifiers, Handle handle) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getControllerIdFromIdentifiers(controllerIdentifiers);
        } catch (Exception e) {
            LOGGER.error("Exception while fetching services for account from DB", e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return Collections.emptyList();
    }

    public List<ViewApplicationServiceMappingBean> getServicesForApplications(int accountId, List<String> applicationIdentifiers) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getServicesForApplications(accountId, applicationIdentifiers);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching getApplicationServiceMapping. Details: {}", e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return new ArrayList<>();
    }

    public List<ViewApplicationServiceMappingBean> getServicesMappedToApplication(int accountId, List<Integer> applicationIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getServicesMappedToApplication(accountId, applicationIds);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching getApplicationServiceMapping", e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return new ArrayList<>();
    }

    public List<ViewApplicationServiceMappingBean> getApplicationsMappedToService(int accountId, List<Integer> serviceIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getApplicationsMappedToService(accountId, serviceIds);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while fetching getApplicationsMappedToService", e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return new ArrayList<>();
    }

    public List<DiscoveryTagDetailsBean> getDiscoveryTagsDetails(int accountId, List<Integer> objectIds, String objectRefTable) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            if (Constants.TABLE_TRANSACTION.equals(objectRefTable))
                return dao.getTxnDiscoveryTagsDetails(accountId, objectRefTable, objectIds);
            else
                return dao.getRuleDiscoveryTagsDetails(accountId, objectRefTable, objectIds);
        } catch (Exception e) {
            LOGGER.error("Exception while retrieving the request tags: ", e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return Collections.emptyList();
    }

    public List<TransactionGroupMapBean> getDiscoveryTagIds(int accountId, List<Integer> objectIds, String objectRefTable, Set<String> tagNames) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getDiscoveryTagIds(accountId, objectRefTable, objectIds, tagNames);

        } catch (Exception e) {
            LOGGER.error("Exception while retrieving the request tags ids: ", e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return Collections.emptyList();
    }

    public int checkKpiUsingKpiId(int accountId, Set<Integer> kpiIds, Handle handle) {
        BindInDao kpiDataDao = getDaoConnection(handle, BindInDao.class);
        try {
            return kpiDataDao.checkKpiUsingKpiId(accountId, kpiIds);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while fetching KPI details.", e);
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int getKpisUsingId(List<Integer> kpiIds, int accountId, Handle handle) {
        BindInDao kpiDataDao = getDaoConnection(handle, BindInDao.class);
        try {
            return kpiDataDao.getKpisUsingId(kpiIds, accountId);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while fetching KPI details. Reason: {}", e.getMessage());
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public int checkGroupKpiUsingGroupKpiId(int accountId, Set<Integer> kpiIds, Handle handle) {
        BindInDao kpiDataDao = getDaoConnection(handle, BindInDao.class);
        try {
            return kpiDataDao.checkGroupKpiUsingGroupKpiId(accountId, kpiIds);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while fetching Group KPI details.", e);
            return -1;
        } finally {
            closeDaoConnection(handle, kpiDataDao);
        }
    }

    public List<MaintenanceDetails> getMaintenanceWindowDetailsList(List<Integer> maintenanceId, Timestamp startTime, Timestamp endTime) {
        BindInDao maintenanceWindowDao = getDaoConnection(null, BindInDao.class);
        try {
            return maintenanceWindowDao.getMaintenanceWindowDetailsList(maintenanceId, startTime, endTime);
        } catch (Exception e) {
            LOGGER.error("Exception while retrieving the getMaintenanceWindowDetailsList", e);
        } finally {
            MySQLConnectionManager.getInstance().close(maintenanceWindowDao);
        }
        return null;
    }

    public List<RecurringDetails> getRecurringDetailsList(List<Integer> maintenanceList) {
        BindInDao maintenanceWindowDao = getDaoConnection(null, BindInDao.class);
        try {
            return maintenanceWindowDao.getRecurringDetailsList(maintenanceList);
        } catch (Exception e) {
            LOGGER.error("Exception while retrieving the getMaintenanceWindowDetailsList", e);
        } finally {
            MySQLConnectionManager.getInstance().close(maintenanceWindowDao);
        }
        return null;
    }

    public void removeOldApplicationIdsForUserNotifications(int accountId, String userId, Handle handle, List<Integer> appIds) throws ControlCenterException {
        SignalNotificationPreferenceDao notificationPreferenceDao = MySQLConnectionManager.getInstance().open(SignalNotificationPreferenceDao.class);
        BindInDao bindInDao = getDaoConnection(handle, BindInDao.class);

        try {
            notificationPreferenceDao.removeNotificationDetailsForUser(accountId, userId);
            bindInDao.removeOldApplicationIdsForUserNotif(appIds, accountId, userId);
            bindInDao.removeOldApplicationIdsForForensicNotif(appIds, accountId, userId);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while deleting user notification preferences for user [{}]. Reason: {}", userId, e.getMessage(), e);
            throw new ControlCenterException("Error in deleting user notification preferences");
        } finally {
            if(handle == null) {
                MySQLConnectionManager.getInstance().close(notificationPreferenceDao);
            }
            closeDaoConnection(handle, bindInDao);
        }
    }

    public void removeOldAccountsForUserNotifications(String userId, List<Integer> accountIds, Handle handle) throws ControlCenterException {
        BindInDao notificationPreferenceDao = getDaoConnection(handle, BindInDao.class);
        try {
            notificationPreferenceDao.removeOldAccountsForUserNotificationMapping(accountIds, userId);
        } catch (Exception e) {
            LOGGER.error("Exception encountered while deleting user notification mapping for user [{}]. Reason: {}", userId, e.getMessage(), e);
            throw new ControlCenterException("Error in deleting user notification mapping.");
        } finally {
            closeDaoConnection(handle, notificationPreferenceDao);
        }
    }

    public List<NotificationBean> getApplicationNotificationPreferences(List<Integer> appIds, int accountId, Handle handle) {
        BindInDao userNotificationDao = getDaoConnection(handle, BindInDao.class);
        List<NotificationBean> notificationBeans = new ArrayList<>();
        try {
            notificationBeans = userNotificationDao.getApplicationNotificationPreferences(appIds, accountId);
        } catch (Exception e) {
            LOGGER.error("Error in getting application user details from DB. application ids:{}", appIds, e);
        } finally {
            closeDaoConnection(handle, userNotificationDao);
        }

        return notificationBeans;
    }

    public List<NotificationBean> getUserNotificationDetails(List<Integer> appIds, String user, int accountId, Handle handle) {
        BindInDao userNotificationDao = getDaoConnection(handle, BindInDao.class);
        List<NotificationBean> notificationBeans = new ArrayList<>();
        try {
            notificationBeans = userNotificationDao.getUserNotificationDetails(appIds, user, accountId);
        } catch (Exception e) {
            LOGGER.error("Error in getting application user details from DB. application ids:{}", appIds, e);
        } finally {
            closeDaoConnection(handle, userNotificationDao);
        }

        return notificationBeans;
    }

    public int deleteTagMapping(int tagId, int objId, int[] tagKeys, String refTable, int accountId, Handle handle) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            dao.deleteTagMapping(tagId, objId, tagKeys, refTable, accountId);
            return 1;
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting tag mapping details." + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return -1;
    }

    public int[] updateAgentVersion(List<AgentBean> agentBeans, Handle handle) throws ControlCenterException {
        BindInDao agentDao = getDaoConnection(handle, BindInDao.class);
        try {
            return agentDao.updateAgentVersion(agentBeans);
        } catch (Exception e) {
            LOGGER.error("Exception while updating agent data into table", e);
            throw new ControlCenterException("Error while updating agent information");
        } finally {
            closeDaoConnection(handle, agentDao);
        }
    }

    public void removeCompVersionKpiMapping(int kpiId, List<Integer> removeCompVersionKpiMappingList, Handle handle) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            dao.removeCompVersionKpiMapping(kpiId, removeCompVersionKpiMappingList);
        } catch (Exception e) {
            LOGGER.error("Error occurred while kpi - component mapping." + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void removeCompInstanceGroupKpiMapping(int kpiId, List<Integer> removeCompInstanceGroupKpiMappingList, Handle handle) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            dao.removeCompInstanceGroupKpiMapping(kpiId, removeCompInstanceGroupKpiMappingList);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting group kpi - component instance mapping details." + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void removeCompInstanceNonGroupKpiMapping(int kpiId, List<Integer> removeCompInstanceNonGroupKpiMappingList, Handle handle) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            dao.removeCompInstanceNonGroupKpiMapping(kpiId, removeCompInstanceNonGroupKpiMappingList);
        } catch (Exception e) {
            LOGGER.error("Error occurred while deleting non group kpi - component instance mapping details." + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public int deleteBatchProcessMapping(int id, List<String> batchJobNames, Handle handle) throws ControlCenterException {
        BindInDao agentDao = getDaoConnection(handle, BindInDao.class);
        try {
            return agentDao.deleteBatchProcessMapping(id, batchJobNames);
        } catch (Exception e) {
            LOGGER.error("Exception while deleting batch to process mapping", e);
            throw new ControlCenterException("Error while deleting batch to process mapping");
        } finally {
            closeDaoConnection(handle, agentDao);
        }
    }

    public List<Integer> fetchBatchJobsMappedToProcessId(int processId, List<String> batchJobNames, Handle handle) throws ControlCenterException {
        BindInDao agentDao = getDaoConnection(handle, BindInDao.class);
        try {
            return agentDao.fetchBatchJobsMappedToProcessId(processId, batchJobNames);
        } catch (Exception e) {
            LOGGER.error("Exception while updating agent data into table", e);
            throw new ControlCenterException("Error while updating agent information");
        } finally {
            closeDaoConnection(handle, agentDao);
        }
    }

    public List<HostInstanceDetails> getHostInstanceId(int hostCompTypeId, String hostAddress, int accId, int isDR,
                                                       Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getHostInstanceId(hostCompTypeId, hostAddress, accId, isDR);
        } catch (Exception e) {
            LOGGER.error("Exception while getting host instanceId for hostAddress [{}] and accountId [{}]. Details: ", hostAddress, accId, e);
            throw new ControlCenterException("Exception while getting host instanceId for hostAddress and accountId provided");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public ClusterInstancePojo getClusterInstanceMapping(int instanceId, Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getClusterInstanceMapping(instanceId);
        } catch (Exception e) {
            LOGGER.error("Error while fetching cluster mapping details for instanceId [{}]", instanceId);
            throw new ControlCenterException("Error while fetching cluster mapping details for instanceId [{}]", String.valueOf(instanceId));
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(int accountId, int instanceId, Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getGroupKpiAttributeMapping(accountId, instanceId);
        } catch (Exception e) {
            LOGGER.error("Error while fetching group kpi attribute mapping details for instanceId [{}]", instanceId);
            throw new ControlCenterException("Error while fetching group kpi attribute mapping details for instanceId [{}]", String.valueOf(instanceId));
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public int getMatchingTxnIdCount(List<Integer> txnIds) {
        BindInDao bindInDao = getDaoConnection(null, BindInDao.class);

        try {
            return bindInDao.getMatchingTxnIdCount(txnIds);
        } catch (Exception e) {
            LOGGER.error("Exception while retrieving the serviceId for txn id:{}", txnIds, e);
        } finally {
            closeDaoConnection(null, bindInDao);
        }
        return 0;
    }

    public void deleteTransactionTagMappings(List<Integer> txnIds, String objectRefTable, int accountId, Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            dao.deleteTransactionTagMappings(txnIds, objectRefTable, accountId);
        } catch (Exception e) {
            LOGGER.error("Error while deleting tag mappings for txnIds [{}]", txnIds);
            throw new ControlCenterException("Error while deleting transaction tag mapping");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<Integer> getTxnGroupIdsWithTxnId(List<Integer> txnIds, Handle handle) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getTxnGroupIdsWithTxnId(txnIds);
        } catch (Exception e) {
            LOGGER.error("Error while fetching mapped txn groups for txnIds [{}]", txnIds);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return Collections.emptyList();
    }

    public int getTxnGroupIdsMappedToMultipleTxns(List<Integer> txnIds, List<Integer> txnGroupIds, Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getTxnGroupIdsMappedtoMultipleTxns(txnGroupIds, txnIds);
        } catch (Exception e) {
            LOGGER.error("Error while fetching count of txn groups for txns [{}] and groups [{}]", txnIds, txnGroupIds);
            throw new ControlCenterException("Error while fetching count of groups mapped to multiple transactions");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public void deleteTxnGroups(List<Integer> txnGroupIds, Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            dao.deleteTxnGroups(txnGroupIds);
        } catch (Exception e) {
            LOGGER.error("Error while deleting transaction groups [{}]", txnGroupIds);
            throw new ControlCenterException("Error while deleting transaction groups");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public List<Integer> getHostClusterId(List<Integer> serviceId, int componentTypeId, Handle handle) throws ControlCenterException {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getHostClusterId(serviceId, componentTypeId);
        } catch (Exception e) {
            LOGGER.error("Exception while host cluster id from service ids : {}", e.getMessage());
            throw new ControlCenterException("Exception while getting host cluster id from services provided");
        } finally {
            closeDaoConnection(handle, dao);
        }
    }

    public int getThresholdsCountForInstAndKpi(List<Integer> compInstanceIds, int kpiId, Handle handle) throws ControlCenterException {
        BindInDao bindInDao = getDaoConnection(handle, BindInDao.class);
        try {
            return bindInDao.getThresholdsCountForInstAndKpi(compInstanceIds, kpiId);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while getting discovery status for group KPI", e);
            throw new ControlCenterException("Encountered exception while getting discovery status for group KPI");
        } finally {
            closeDaoConnection(handle, bindInDao);
        }
    }

    public List<KpiMaintenanceStatusBean> getMaintenanceStatusForInstAndKpi(List<Integer> compInstanceIds, int kpiId, Handle handle) throws ControlCenterException {
        BindInDao bindInDao = getDaoConnection(handle, BindInDao.class);
        try {
            return bindInDao.getMaintenanceStatusForInstAndKpi(compInstanceIds, kpiId);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while getting maintenance status for KPIs", e);
            throw new ControlCenterException("Encountered exception while getting maintenance status for KPIs");
        } finally {
            closeDaoConnection(handle, bindInDao);
        }
    }

    public List<AgentBean> getAgentsDataByAgentId(List<Integer> agentIds) {
        BindInDao dao = getDaoConnection(null, BindInDao.class);
        try {
            return dao.getAgentsDataByAgentId(agentIds);
        } catch (Exception e) {
            LOGGER.error("Error occurred while getting physical agent details" + e.getMessage(), e);
        } finally {
            closeDaoConnection(null, dao);
        }
        return Collections.emptyList();
    }

    public int agentCountForGivenIds(List<Integer> agentIds, Handle handle) {
        BindInDao bindInDao = getDaoConnection(handle, BindInDao.class);
        try {
            return bindInDao.agentCountForGivenIds(agentIds);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while getting agent count based on Ids.", e);
        } finally {
            closeDaoConnection(handle, bindInDao);
        }
        return -1;
    }

    public List<TransactionGroup> getTransactionGroupsByNames(Set<String> tagNameList, Handle handle) {
        BindInDao bindInDao = getDaoConnection(handle, BindInDao.class);
        try {
            return bindInDao.getTxnGroupIds(tagNameList);
        } catch (Exception e) {
            LOGGER.error("Error while getting transaction groups", e);
        } finally {
            closeDaoConnection(handle, bindInDao);
        }
        return new ArrayList<>();
    }

    public List<ProducerMapping> getProducerMapping(List<Integer> compVersionIds, int componentId, int accountId) {
        BindInDao bindInDao = getDaoConnection(null, BindInDao.class);
        try {
            return bindInDao.getProducerMappingDetails(compVersionIds, componentId, accountId);
        } catch (Exception e) {
            LOGGER.error("Encountered exception while getting agent count based on Ids.", e);
        } finally {
            closeDaoConnection(null, bindInDao);
        }
        return Collections.emptyList();
    }

    public List<String> getCategoryIdentifiersByIds(List<Integer> categoryIds, Handle handle) throws ControlCenterException {
        BindInDao bindInDao = getDaoConnection(handle, BindInDao.class);
        try {
            return bindInDao.getCategoryIdentifierById(categoryIds);
        } catch (Exception e) {
            LOGGER.error("Error while fetching the category identifiers with IDs [{}]", categoryIds, e);
            throw new ControlCenterException("Error while fetching the category Identifiers");
        } finally {
            closeDaoConnection(handle, bindInDao);
        }
    }

    public List<ComponentInstanceBean> getComponentInstancesByIds(List<Integer> ids, int accountId, Handle handle) {
        BindInDao dao = getDaoConnection(handle, BindInDao.class);
        try {
            return dao.getComponentInstancesByIds(ids, accountId);
        } catch (Exception e) {
            LOGGER.error("Exception while getting component instances for ids -" + ids + ":" + e.getMessage(), e);
        } finally {
            closeDaoConnection(handle, dao);
        }
        return Collections.emptyList();
    }

    public int[] updateCompInstanceDetails(List<ComponentInstanceBean> instances, Handle handle) {
        BindInDao agentDao = getDaoConnection(handle, BindInDao.class);
        try {
            return agentDao.updateInstanceDetails(instances);
        } catch (Exception e) {
            LOGGER.error("Exception while updating comp instances data into table", e);
        } finally {
            closeDaoConnection(handle, agentDao);
        }
        return new int[0];
    }

    public List<CommandDetailArgumentBean> getCommandArguments(List<Integer> commandId, Handle handle) {
        BindInDao bindInDao = getDaoConnection(handle, BindInDao.class);
        try {
            return bindInDao.getAllCommandArguments(commandId);
        } catch (Exception e) {
            LOGGER.error("Error in getting command arguments from DB", e);
        }finally {
            closeDaoConnection(handle, bindInDao);
        }
        return new ArrayList<>();
    }

    public List<CommandTriggerBean> getTriggeredJimForensicCommands(List<Integer> commandId, List<Integer> jimAgentIds, Handle handle) {
        BindInDao bindInDao = getDaoConnection(handle, BindInDao.class);
        try {
            return bindInDao.getJimForensicCommands(commandId, jimAgentIds);
        } catch (Exception e) {
            LOGGER.error("Error in getting command arguments from DB", e);
        }finally {
            closeDaoConnection(handle, bindInDao);
        }
        return new ArrayList<>();
    }

}
