package com.appnomic.appsone.controlcenter.dao.mysql.interfaces;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.*;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.heal.configuration.pojos.TransactionGroup;
import org.skife.jdbi.v2.sqlobject.*;
import org.skife.jdbi.v2.sqlobject.customizers.RegisterMapperFactory;
import org.skife.jdbi.v2.sqlobject.stringtemplate.UseStringTemplate3StatementLocator;
import org.skife.jdbi.v2.tweak.BeanMapperFactory;
import org.skife.jdbi.v2.unstable.BindIn;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

@UseStringTemplate3StatementLocator
public interface BindInDao {

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select c.id componentInstanceId, a.attribute_value attributeValue, a.attribute_name attributeName " +
            "from comp_instance c inner join comp_instance_attribute_values a on c.id = a.comp_instance_id where c.account_id = :accountId " +
            "and a.attribute_value in (<attributesVal>) and c.mst_component_id=:mstComponentId")
    List<ComponentInstanceAttributesBean> getCompInstBasedInAttributeValues(@BindIn("attributesVal") List<String> attributesVal, @Bind("accountId") int accountId, @Bind("mstComponentId") int mstComponentId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status,host_id hostId,is_dr isDR,is_cluster isCluster," +
            "mst_component_version_id mstComponentVersionId,created_time createdTime," +
            "updated_time updatedTime,user_details_id userDetailsId,account_id accountId," +
            "mst_component_id mstComponentId,mst_component_type_id mstComponentTypeId,discovery," +
            "host_address hostAddress,identifier,mst_common_version_id mstCommonVersionId, supervisor_id supervisorId " +
            "from comp_instance where account_id = :accountId and " +
            "identifier in (<identifiers>)")
    List<ComponentInstanceBean> getComponentInstances(@BindIn("identifiers") List<String> identifiers, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ci.id instanceId, ci.name instanceName, " +
            "ci.component_version_name instanceVersion, ci.host_address ipAddress , " +
            "cih.id hostId,cih.name hostName, " +
            "cih.component_name hostType, " +
            "cih.component_version_name hostVersion " +
            "from view_component_instance ci " +
            "join view_component_instance cih on(ci.host_id=cih.id) where ci.id in (<instanceIds>)")
    List<ComptInstancePojo> getCompInstanceDetailsForNonHost(@BindIn("instanceIds") List<Integer> instanceIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id attributeId, attribute_value attributeValue, attribute_name attributeName, comp_instance_id compInstanceId " +
            "from comp_instance_attribute_values attr where attr.comp_instance_id in (<compInstanceIds>) and attr.attribute_name = :attrName")
    List<CompInstAttributesBean> getComponentInstanceAttributeByName(@Bind("attrName") String attrName, @BindIn("compInstanceIds") Set<Integer> compInstanceIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct c.id appId, c.name name, c.controller_type_id controllerTypeId, c.identifier identifier, c.status status, c.user_details_id createdBy," +
            "c.created_time createdOn, c.updated_time updatedTime, c.account_id accountId " +
            "from controller c where c.account_id = :accountId and c.identifier in (<controllerIdentifiers>) and c.status=1")
    List<Controller> getControllerDetailsWithIdentifier(@Bind("accountId") int accountId, @BindIn("controllerIdentifiers") List<String> controllerIdentifiers);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct tm.tag_key from tag_mapping tm join controller c where tm.account_id=:accountId and tm.account_id=c.account_id " +
            "and tm.object_ref_table='controller' and object_id in (<applicationIds>) and tm.tag_key=c.id and c.status=1")
    List<Integer> getControllerDetailsFromTagMapping(@Bind("accountId") int accountId, @BindIn("applicationIds") List<Integer> applicationIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct tm.object_id from tag_mapping tm join transaction t where tm.account_id=:accountId and tm.account_id=t.account_id " +
            "and tm.object_ref_table='transaction' and tm.tag_key in (<serviceIds>) and tm.object_id=t.id and t.status=1")
    List<Integer> getTransactionsFromTagMapping(@Bind("accountId") int accountId, @BindIn("serviceIds") List<Integer> serviceIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select identifier identifier from controller where id in (<controllerIds>)")
    List<String> getControllerIdentifiersFromId(@BindIn("controllerIds") List<Integer> controllerIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,unique_token uniqueToken,name,agent_type_id agentTypeId,created_time " +
            "createdTime,updated_time updatedTime,user_details_id userDetailsId,status," +
            "host_address hostAddress,physical_agent_id physicalAgentId, mode,description from agent where id in (<agentIds>)")
    Set<AgentBean> getAgentTypeList(@BindIn("agentIds") Set<Integer> agentIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id compInstanceId, agent_id agentId from " +
            "agent_comp_instance_mapping where agent_id in (<agentIds>)")
    List<AgentCompInstMappingBean> getAgentCompInstMapping(@BindIn("agentIds") List<Integer> agentIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select r.id, r.name, r.is_enabled monitorEnabled, r.order, r.rule_type_id ruleTypeId, r.is_default defaultRule from rules r where r.id in (<ruleIds>)")
    List<RulesBean> getRulesByIds(@BindIn("ruleIds") List<Integer> ruleIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select comp_instance_id from component_cluster_mapping where account_id =:accountId and cluster_id in (<instanceIds>)")
    List<Integer> getInstances(@Bind("accountId") int accountId,@BindIn("instanceIds") List<Integer> instanceIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("DELETE FROM `application_percentiles` WHERE id IN (<appPercentileIds>)")
    void deletePercentilesForApplication(@BindIn("appPercentileIds") List<Integer> deletePercentiles);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id, host_address hostAddress, directory_path directoryPath, process_details_id processDetailsId, status, " +
            "created_time createdTime, updated_time updatedTime, user_details_id userDetailsId from process_host_details " +
            "where process_details_id in (<batchProcessIds>) and status = 1")
    List<ProcessHostDetailsBean> getHostDetailsForProcesses(@BindIn("batchProcessIds") List<Integer> batchProcessIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select kpi_id kpiId, category_id categoryId, category_identifier categoryIdentifier, name categoryName, is_workload workLoad " +
            "from view_kpi_category_details where kpi_id in (<kpiIds>)")
    List<KpiCategoryMapping> getCategoryDetailsForKpis(@BindIn("kpiIds") List<Integer> kpiIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select category_id id, count(kpi_id) count from view_kpi_category_details where category_id in (<categoryIds>) group by category_id")
    List<CountBean> getKpiCountForCategories(@BindIn("categoryIds") List<Integer> categoryIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("update mst_kpi_details set is_informative = :isInfo, updated_time = :updatedTime where id in (<kpiIds>)")
    void updateInfoForMappedKPIs(@BindIn("kpiIds") List<Integer> kpiIds, @Bind("isInfo") Integer isInfo,
                                 @Bind("updatedTime") String updateTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id id,name name,controller_type_id controllerTypeId,identifier identifier from controller " +
            "where identifier in (<controllerIdentifiers>)")
    List<Integer> getControllerIdFromIdentifiers(@BindIn("controllerIdentifiers") List<String> controllerIdentifiers);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct service_id serviceId, service_name serviceName from view_application_service_mapping " +
            "where account_id = :account_id and application_identifier in (<applicationIdentifiers>)")
    List<ViewApplicationServiceMappingBean> getServicesForApplications(@Bind("account_id") Integer accountId, @BindIn("applicationIdentifiers") List<String> applicationIdentifiers);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select service_id serviceId, service_name serviceName, service_identifier serviceIdentifier, " +
            "application_id applicationId, application_identifier applicationIdentifier, application_name applicationName " +
            "from view_application_service_mapping " +
            "where account_id = :account_id and application_id in (<applicationIds>)")
    List<ViewApplicationServiceMappingBean> getServicesMappedToApplication(@Bind("account_id") Integer accountId, @BindIn("applicationIds") List<Integer> applicationIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select service_id serviceId, service_name serviceName, service_identifier serviceIdentifier, " +
            "application_id applicationId, application_identifier applicationIdentifier, application_name applicationName " +
            "from view_application_service_mapping " +
            "where account_id = :account_id and service_id in (<serviceIds>)")
    List<ViewApplicationServiceMappingBean> getApplicationsMappedToService(@Bind("account_id") Integer accountId, @BindIn("serviceIds") List<Integer> serviceIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select T.id mappingId, GROUP_CONCAT(TG.name) as tagName, T.max_tags maxTags, TG.status status, count(TM.object_id) tagCount from transaction_group_mapping TM left join transaction T on T.id=TM.object_id left join transaction_groups TG on TG.id=TM.txn_group_id where TM.account_id = :accountId and TM.object_ref_table = :objectRefTable and TM.object_id in(<objectId>) group by T.id;")
    List<DiscoveryTagDetailsBean> getTxnDiscoveryTagsDetails(@Bind("accountId") Integer accountId, @Bind("objectRefTable") String objectRefTable, @BindIn("objectId") List<Integer> objectId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select R.id mappingId, GROUP_CONCAT(TG.name) as tagName, R.max_tags maxTags, TG.status status, count(TM.object_id) tagCount from transaction_group_mapping TM left join rules R on R.id=TM.object_id left join transaction_groups TG on TG.id=TM.txn_group_id where TM.account_id= :accountId and TM.object_ref_table = :objectRefTable and R.id in(<objectId>) group by R.id")
    List<DiscoveryTagDetailsBean> getRuleDiscoveryTagsDetails(@Bind("accountId") Integer accountId, @Bind("objectRefTable") String objectRefTable,  @BindIn("objectId") List<Integer> objectId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select TM.id, TM.object_id objectId from transaction_group_mapping TM JOIN transaction_groups TG on TM.txn_group_id=TG.id where TM.object_ref_table = :objectRefTable and TM.account_id = :accountId and TM.object_id in(<objectId>) and TG.name in (<tagNames>)")
    List<TransactionGroupMapBean> getDiscoveryTagIds(@Bind("accountId") Integer accountId, @Bind("objectRefTable") String objectRefTable, @BindIn("objectId") List<Integer> objectId,  @BindIn("tagNames") Set<String> tagNames);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT count(*) from mst_kpi_details where account_id in (1, :accountId) and id in (<kpiIdsList>)")
    int checkKpiUsingKpiId(@Bind("accountId") int accountId, @BindIn("kpiIdsList") Set<Integer> kpiIdsList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT count(*) from mst_kpi_group where account_id in (1, :accountId) and id in (<groupKpiIdsList>)")
    int checkGroupKpiUsingGroupKpiId(@Bind("accountId") int accountId, @BindIn("groupKpiIdsList") Set<Integer> groupKpiIdsList);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from mst_kpi_details where id in (<kpiIds>) and account_id in (1, :accountId)")
    int getKpisUsingId(@BindIn("kpiIds") List<Integer> kpiIds, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id , name, type_id typeId, account_id accountId, status, start_time startTimeString, end_time endTimeString,start_time startTime,end_time endTime, created_time createdTime,"+
            "updated_time updatedTime , user_details_id userDetails  from maintenance_details where id IN (<maintenanceId>) and status = 1 "+
            "and ((start_time \\<= :startTime and end_time >= :startTime) or (start_time \\<= :endTime and end_time >= :endTime) or start_time >= :endTime)")
    List<MaintenanceDetails> getMaintenanceWindowDetailsList(@BindIn("maintenanceId") List<Integer> maintenanceId, @Bind("startTime") Timestamp startTime , @Bind("endTime") Timestamp endTime);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id , maintenance_id maintenanceId, recurring_type_id recurringTypeId, start_hr_min startHrMin, end_hr_min endHrMin, duration,"+
            "recurring_data recurringData, user_details_id userDetails, created_time createdTime, updated_time updatedTime "+
            "from recurring_details where maintenance_id IN (<maintenanceId>)")
    List<RecurringDetails> getRecurringDetailsList(@BindIn("maintenanceId") List<Integer> maintenanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId,notification_type_id notificationTypeId,signal_type_id signalTypeId,signal_severity_id severityTypeId " +
            "from application_notification_mapping where " +
            "application_id in (<appIds>) and account_id=:accountId ")
    List<NotificationBean> getApplicationNotificationPreferences(@BindIn ("appIds")List<Integer> appIds, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select application_id applicationId,notification_type_id notificationTypeId,signal_type_id signalTypeId,signal_severity_id severityTypeId " +
            "from user_notification_mapping where " +
            "application_id in (<appIds>) and applicable_user_id=:applicableUserId and account_id=:accountId ")
    List<NotificationBean> getUserNotificationDetails(@BindIn("appIds") List<Integer> appIds, @Bind("applicableUserId") String applicableUserId, @Bind("accountId") int accountId);

    @SqlUpdate("DELETE FROM user_notification_mapping where applicable_user_id = :applicableUserId and account_id= :accountId and application_id not in (<appIds>)")
    void removeOldApplicationIdsForUserNotif(@BindIn("appIds") List<Integer> appIds, @Bind("accountId") int accountId, @Bind("applicableUserId") String applicableUserId);

    @SqlUpdate("DELETE FROM user_forensic_notification_mapping where applicable_user_id = :applicableUserId and account_id = :accountId and application_id not in (<appIds>)")
    void removeOldApplicationIdsForForensicNotif(@BindIn("appIds") List<Integer> appIds, @Bind("accountId") int accountId, @Bind("applicableUserId") String applicableUserId);

    @SqlUpdate("DELETE FROM user_notification_mapping where applicable_user_id = :applicableUserId and account_id not in (<accountIds>)")
    void removeOldAccountsForUserNotificationMapping(@BindIn("accountIds") List<Integer> accountIds, @Bind("applicableUserId") String applicableUserId);

    @SqlUpdate("DELETE FROM tag_mapping where tag_id = :tagId and object_id = :objId and object_ref_table = :refTable and " +
            "tag_key in (<tagKeys>) and account_id = :accountId")
    void deleteTagMapping(@Bind("tagId") int tagId, @Bind("objId") int objId, @BindIn("tagKeys") int[] tagKeys,
                          @Bind("refTable") String refTable, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update agent set version=ifnull(:version,version), updated_time = :updatedTime, user_details_id = :userDetailsId where unique_token = :uniqueToken")
    int[] updateAgentVersion(@BindBean List<AgentBean> agentBean);

    @SqlUpdate("DELETE FROM mst_component_version_kpi_mapping where mst_kpi_details_id = :kpiId and mst_component_id in (<componentIds>)")
    void removeCompVersionKpiMapping(@Bind("kpiId") int kpiId, @BindIn ("componentIds")List<Integer> componentIds);

    @SqlUpdate("DELETE FROM comp_instance_kpi_group_details where mst_kpi_details_id = :kpiId and comp_instance_id in (<compInstanceIds>)")
    void removeCompInstanceGroupKpiMapping(@Bind("kpiId") int kpiId, @BindIn ("compInstanceIds")List<Integer> compInstanceIds);

    @SqlUpdate("DELETE FROM comp_instance_kpi_details where mst_kpi_details_id = :kpiId and comp_instance_id in (<compInstanceIds>)")
    void removeCompInstanceNonGroupKpiMapping(@Bind("kpiId") int kpiId, @BindIn ("compInstanceIds")List<Integer> compInstanceIds);

    @SqlUpdate("DELETE from `batch_process_mapping` where batch_job_name in (<batchJobNames>) and process_id=:processId")
    int deleteBatchProcessMapping(@Bind("processId") int id, @BindIn("batchJobNames") List<String> batchJobNames);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id from batch_process_mapping where process_id=:processId and batch_job_name in (<batchJobs>)")
    List<Integer> fetchBatchJobsMappedToProcessId(@Bind("processId") int processId, @BindIn("batchJobs") List<String> batchJobs);

    @RegisterMapperFactory(BeanMapperFactory.class)

    @SqlQuery("select distinct ci1.id hostInstanceId ,ci1.name hostInstanceName,  ci1.is_DR isDR from " +
            "component_cluster_mapping ccm, comp_instance ci1, comp_instance ci2  where ci1.status = 1 and " +
            "ci1.mst_component_type_id = 1 and  ccm.comp_instance_id=ci1.id and ccm.cluster_id=ci2.id and " +
            " ci2.is_cluster= 1 and ci1.is_cluster = 0 and ci1.mst_component_type_id = :hostCompTypeId and  " +
            "ci1.host_address=:hostAddress and ci1.account_id =:accId and ci1.is_DR=:isDR")
   List<HostInstanceDetails> getHostInstanceId(@Bind("hostCompTypeId") int hostCompTypeId,
                                               @Bind("hostAddress") String hostAddress, @Bind("accId") int accId,
                                               @Bind("isDR") int isDR);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct id from view_cluster_services where service_id in (<serviceId>) and mst_component_type_id = :componentTypeId")
    List<Integer> getHostClusterId(@BindIn("serviceId") List<Integer> serviceId, @Bind("componentTypeId") int componentTypeId);

    @SqlQuery("select count(*) from comp_instance_kpi_threshold_details where comp_instance_id in (<compInstanceIds>) and kpi_id=:kpiId")
    int getThresholdsCountForInstAndKpi(@BindIn("compInstanceIds") List<Integer> compInstanceIds, @Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select distinct exclude_maintenance isMaintenanceExcluded, sor_persistence persistence, sor_suppression suppression " +
            "from comp_instance_kpi_configuration where comp_instance_id in (<compInstanceIds>) and kpi_id=:kpiId")
    List<KpiMaintenanceStatusBean> getMaintenanceStatusForInstAndKpi(@BindIn("compInstanceIds") List<Integer> compInstanceIds, @Bind("kpiId") int kpiId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select a.id,unique_token uniqueToken,a.name,a.agent_type_id agentTypeId," +
            "a.created_time createdTime,a.updated_time updatedTime,a.user_details_id userDetailsId," +
            "a.status, a.host_address hostAddress, a.mode, a.description, a.physical_agent_id physicalAgentId, " +
            "pa.identifier physicalAgentIdentifier " +
            " from agent a, physical_agent pa where a.id in(<agentIds>) and a.physical_agent_id=pa.id")
    List<AgentBean> getAgentsDataByAgentId(@BindIn("agentIds") List<Integer> agentIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(id) from agent where id in (<agentIds>)")
    int agentCountForGivenIds(@BindIn("agentIds") List<Integer> agentIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id transactionGroupId, name transactionGroupName from transaction_groups where name in (<names>)")
    List<TransactionGroup> getTxnGroupIds(@BindIn("names") Set<String> names);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select mp.id producerId, mp.name producerName, mpkm.is_default isDefault, mpkm.id producerMappingId, mpkm.mst_kpi_details_id kpiId " +
            "from mst_producer mp, mst_producer_kpi_mapping mpkm " +
            "where mpkm.mst_component_id = :componentId and mpkm.mst_component_version_id in (<componentVersionIds>) and " +
            "mp.id = mpkm.producer_id and mp.status = 1 and mp.account_id in (1, :accountId)")
    List<ProducerMapping> getProducerMappingDetails(@BindIn("componentVersionIds") List<Integer> componentVersionIds,
                                                    @Bind("componentId") int componentId, @Bind("accountId") int accountId);
    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select ccm.cluster_id clusterId, identifier clusterIdentifier, c.name clusterName, c.status status, c.host_id hostId, c.is_DR isDR, c.is_cluster isCluster, c.mst_component_version_id componentVersionId, c.created_time createdTime, c.updated_time updatedTime, c.user_details_id lastModifiedBy, c.account_id accountId, c.mst_component_id componentId, c.mst_component_type_id componentTypeId, c.discovery discovery, c.host_address hostAddress, c.mst_common_version_id commonVersionId , c.parent_instance_id parentInstanceId, c.supervisor_id supervisorId from component_cluster_mapping ccm," +
            " comp_instance c where ccm.cluster_id=c.id and comp_instance_id = :instanceId ")
    ClusterInstancePojo getClusterInstanceMapping(@Bind("instanceId") int instanceId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select attribute_value attributeValue, mst_kpi_details_id kpiId ,ifnull(alias_name, attribute_value) aliasName from comp_instance_kpi_group_details ckgd, comp_instance ci where ci.id=comp_instance_id and ci.account_id= :accountId and comp_instance_id = :instanceId")
    List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(@Bind("accountId") int accountId, @Bind("instanceId") int instanceId);

    @SqlUpdate("DELETE FROM tag_mapping where object_id in (<objIds>) and object_ref_table = :refTable and account_id = :accountId")
    void deleteTransactionTagMappings(@BindIn("objIds") List<Integer> objIds, @Bind("refTable") String refTable, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select txn_group_id from transaction_group_mapping where object_id in (<txnIds>) and object_ref_table='transaction'")
    List<Integer> getTxnGroupIdsWithTxnId(@BindIn("txnIds") List<Integer> txnIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from transaction_group_mapping where txn_group_id in (<txnGroupIds>) and object_id not in (<txnIds>)")
    int getTxnGroupIdsMappedtoMultipleTxns(@BindIn("txnGroupIds") List<Integer> txnGroupIds, @BindIn("txnIds") List<Integer> txnIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlUpdate("delete from transaction_groups where id in (<txnGroupIds>)")
    void deleteTxnGroups(@BindIn("txnGroupIds") List<Integer> txnGroupIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select count(*) from transaction where id in (<txnIds>)")
    int getMatchingTxnIdCount(@BindIn("txnIds") List<Integer> txnIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select identifier from mst_category_details where id in (<categoryIds>)")
    List<String> getCategoryIdentifierById(@BindIn("categoryIds") List<Integer> categoryIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select id,name,status, forensic_agent_type_id forensicsAgentTypeId " +
            "from comp_instance where account_id = :accountId and " +
            "id in (<ids>)")
    List<ComponentInstanceBean> getComponentInstancesByIds(@BindIn("ids") List<Integer> ids, @Bind("accountId") int accountId);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlBatch("update comp_instance set forensic_agent_type_id = :forensicsAgentTypeId where id = :id")
    int[] updateInstanceDetails(@BindBean List<ComponentInstanceBean> instances);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("SELECT id id, command_id commandId, argument_key argumentKey, argument_value " +
            "argumentValue, default_value defaultValue, argument_value_type_id argumentValueTypeId, " +
            "argument_type_id argumentTypeId, created_time createdTime, updated_time updatedTime, " +
            "user_details_id userDetails, is_placeholder placeHolder from command_arguments " +
            "where command_id in (<commandIds>)")
    List<CommandDetailArgumentBean> getAllCommandArguments(@BindIn("commandIds") List<Integer> commandIds);

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select  physical_agent_id physicalAgentIdentifier, command_id commandId, max(trigger_time) triggerTime, command_job_id commandJobId, command_status commandStatus, user_details_id userDetailsId, agent_id agentId " +
            "from agent_commands_triggered where agent_id in (<agentIds>) and command_id in (<commandIds>) group by agent_id")
    List<CommandTriggerBean> getJimForensicCommands(@BindIn("commandIds") List<Integer> commandIds, @BindIn("agentIds") List<Integer> jimIds);

}
