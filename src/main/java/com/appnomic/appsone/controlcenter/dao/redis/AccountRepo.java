package com.appnomic.appsone.controlcenter.dao.redis;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.SMSDetails;
import com.heal.configuration.pojos.SMTPDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

@Slf4j
public class AccountRepo {

    public List<Account> getAccounts() {
        String ACCOUNTS_KEY = "/accounts";
        String ACCOUNTS_HASH = "ACCOUNT_DATA";
        try {
            String accountBean = RedisUtilities.getKey(ACCOUNTS_KEY, ACCOUNTS_HASH);
            if (accountBean == null) {
                log.debug("Account details not found");
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(accountBean, new TypeReference<List<Account>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting account Details", e);
            return Collections.emptyList();
        }
    }

    public Account getAccountWithAccountIdentifier(String accountIdentifier) {
        String ACCOUNTS_KEY = "/accounts/" + accountIdentifier;
        String ACCOUNTS_HASH = "ACCOUNT_DATA_" + accountIdentifier;
        try {
            String accountBean = RedisUtilities.getKey(ACCOUNTS_KEY, ACCOUNTS_HASH);
            if (accountBean == null) {
                log.debug("Account details not found");
                return null;
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(accountBean, new TypeReference<Account>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting account Details", e);
            return null;
        }
    }


    public void updateAccount(String accountIdentifier, Account account) {
        String key = "/accounts";
        String hashKey = "ACCOUNT_DATA";
        try {
            RedisUtilities.updateKey(key + "/" + accountIdentifier,
                    hashKey + "_" + accountIdentifier,
                    account);
            log.debug("Updated the account with id {}, name {}", account.getId(), account.getName());
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating account details {} for account [{}]. Details: ", account, accountIdentifier, e);
        }
    }

    public void updateSmsConfiguration(String accIdentifier, SMSDetails smsDetails) {
        String key = "/accounts/" + accIdentifier + "/sms/configuration";
        String hashKey = "ACCOUNTS_" + accIdentifier + "_SMS_CONFIGURATION";
        try {
            RedisUtilities.updateKey(key, hashKey, smsDetails);
            log.debug("Updated the sms Details with for account {}", accIdentifier);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating sms details for account [{}] :", accIdentifier, e);
        }
    }

    public void updateSmtpConfiguration(String accIdentifier, SMTPDetails smsDetails) {
        String key = "/accounts/" + accIdentifier + "/smtp/configuration";
        String hashKey = "ACCOUNTS_" + accIdentifier + "_SMTP_CONFIGURATION";
        try {
            RedisUtilities.updateKey(key, hashKey, smsDetails);
            log.debug("Updated the smtp Details with for account {}", accIdentifier);
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating smtp details for account [{}] :", accIdentifier, e);
        }
    }

    public List<BasicEntity> getAllApplications(String accIdentifier) {
        String key = "/accounts/" + accIdentifier + "/applications";
        String hashKey = "ACCOUNTS_" + accIdentifier + "_APPLICATIONS";
        try {
            String applicationDetails = RedisUtilities.getKey(key, hashKey);

            if(applicationDetails == null){
                log.debug(" application details not found for account: [{}]",accIdentifier);
                return Collections.emptyList();
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(applicationDetails, new TypeReference<List<BasicEntity>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while fetching application details for account [{}] :", accIdentifier, e);
            return Collections.emptyList();
        }
    }
    public void updateAccounts( List<Account> accounts) {
        String key = "/accounts";
        String hashKey = "ACCOUNT_DATA";
        try {
            RedisUtilities.updateKey(key, hashKey , accounts);
            log.debug("Updated the account details");
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while updating the account details  ");
        }
    }
}

