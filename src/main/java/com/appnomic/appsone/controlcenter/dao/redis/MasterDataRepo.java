package com.appnomic.appsone.controlcenter.dao.redis;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.OSIndexZoneDetails;
import com.heal.configuration.pojos.ViewTypes;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class MasterDataRepo {

    private List<ViewTypes> viewTypesList;

    public List<ViewTypes> getTypes() {
        String HEAL_KEY = "/heal";
        String TYPE_KEY = "/types";
        String HEAL_HASH = "HEAL";
        String TYPE_HASH = "_TYPES";
        try {
            String typesObject = RedisUtilities.getKey(HEAL_KEY + TYPE_KEY, HEAL_HASH + TYPE_HASH);
            if (typesObject == null) {
                log.debug("Type Details not found.");
                return Collections.emptyList();
            }
            viewTypesList = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(typesObject, new TypeReference<List<ViewTypes>>() {
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting All Types details.");
        }

        return viewTypesList;
    }

    public ViewTypes getSubType(String typeName, String subTypeName) {
        if(viewTypesList == null || viewTypesList.isEmpty()){
            viewTypesList = getTypes();
        }
        return viewTypesList
                .stream()
                .filter(a -> a.getTypeName().equalsIgnoreCase(typeName))
                .filter(a -> a.getSubTypeName().equalsIgnoreCase(subTypeName))
                .findFirst().orElse(null);
    }

    public List<ViewTypes> getTypesByTypeId(int typeId) {
        if(viewTypesList.isEmpty()){
            viewTypesList = getTypes();
        }
        return viewTypesList
                .stream()
                .filter(a -> a.getTypeId() == (typeId))
                .collect(Collectors.toList());
    }

    public List<ViewTypes> getTypeDetailsByTypeName(String typeName) {
        if(viewTypesList == null || viewTypesList.isEmpty()) {
            viewTypesList = new MasterDataRepo().getTypes();
        }

        return viewTypesList
                .parallelStream()
                .filter(a -> a.getTypeName().equalsIgnoreCase(typeName))
                .collect(Collectors.toList());
    }

    public List<OSIndexZoneDetails> getHealOSIndexToZoneMapping() {
        String key = "/heal/index/zones";
        String hashKey = "HEAL_INDEX_ZONES";
        try {
            String osIndexToZoneMapping = RedisUtilities.getKey(key, hashKey);
            if (osIndexToZoneMapping == null || osIndexToZoneMapping.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }

            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(osIndexToZoneMapping, new TypeReference<List<OSIndexZoneDetails>>() {
            });

        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }
}