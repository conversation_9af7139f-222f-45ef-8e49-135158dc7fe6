package com.appnomic.appsone.controlcenter.dao.redis;

import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.RedisUtilities;
import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.TagDetails;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;


@Slf4j
public class TagDetailsRepo {
    String HEAL_KEY = "/heal";
    String TAG_KEY = "/tag";
    String DETAILS_KEY = "/details";
    String HEAL_HASH = "HEAL";
    String TAG_HASH = "_TAG";
    String DETAILS_HASH = "_DETAILS";
    public List<TagDetails> getTagMappingDetails() {
        try {
            String tagMappingDetailsBean = RedisUtilities.getKey(HEAL_KEY + TAG_KEY + DETAILS_KEY, HEAL_HASH + TAG_HASH + DETAILS_HASH);
            if (tagMappingDetailsBean == null) {
                log.debug("Tag Mapping details not found");
                return Collections.emptyList();
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(tagMappingDetailsBean, new TypeReference<List<TagDetails>>(){
            });
        } catch (Exception e) {
            CCCache.INSTANCE.updateCCErrors(1);
            log.error("Error occurred while getting TagMapping Details", e);
            return Collections.emptyList();
        }
    }
}
