package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class Agent {
    private int id;
    private int status;
    private String uniqueToken;
    private String physicalAgentIdentifier;
    private String name;
    private String subType;
    private String hostAddress;
    private String hostType;
    private String mode;
    private String description;
    private String version;
    private int communicationInterval;
    private int serviceId;
    private String serviceName;
    private ComponentAgent agentMappingDetails;
    private Timestamp updatedTime;
    private String updatedBy;
    private List<AgentAccountMappings> accountMappings;
    private List<AgentCompInstMappingDetails> compInstIdentifiers;
    private List<String> addedDataSources;
    private List<String> deletedDataSources;

    public boolean validateForUpdate() {
        boolean retVal = true;

        if(StringUtils.isEmpty(physicalAgentIdentifier)) {
            log.error("Physical agent identifier is invalid. Reason: It is either NULL or empty");
            retVal = false;
        }

        if(version != null && StringUtils.isEmpty(version)) {
            log.error("Agent version [{}] is invalid", version);
            retVal = false;
        }

        if(agentMappingDetails != null && agentMappingDetails.getDataCommunication() != null) {
            if(agentMappingDetails.getDataCommunication().getId() == 0) {
                log.error("Data Communication Id [{}] is invalid", agentMappingDetails.getDataCommunication().getId());
                retVal = false;
            }
        }
        return retVal;
    }

    public void validate() throws Exception {
        if (this.uniqueToken != null && uniqueToken.trim().isEmpty()) {
            throw new ControlCenterException("uniqueToken can not be empty.");
        }
        if(this.physicalAgentIdentifier != null && physicalAgentIdentifier.trim().isEmpty()) {
            throw new ControlCenterException("physicalAgentIdentifier can not be empty.");
        }
        if (StringUtils.isEmpty(this.name)) {
            throw new ControlCenterException("name can not be null or empty.");
        }
        if (StringUtils.isEmpty(this.subType)) {
            throw new ControlCenterException("sub type can not be null or empty.");
        }
        if (StringUtils.isEmpty(this.hostAddress)) {
            throw new ControlCenterException("host address can not be null or empty.");
        }
        if (StringUtils.isEmpty(this.mode)) {
            throw new ControlCenterException("mode can not be null or empty.");
        }


        if (!this.name.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            throw new ControlCenterException(UIMessages.ALLOWED_NAME_CHARACTERS, "Agent name");
        }

        if (this.uniqueToken != null && !this.uniqueToken.matches(Constants.ALLOWED_IDENTIFIER_CHARACTERS)) {
            throw new ControlCenterException(UIMessages.ALLOWED_IDENTIFIER_CHARACTERS, "Agent uniqueToken");
        }

        if (this.agentMappingDetails != null) {
            this.agentMappingDetails.validate();
        }

        if (this.compInstIdentifiers != null && !this.compInstIdentifiers.isEmpty()) {
            for (AgentCompInstMappingDetails compInstId : this.compInstIdentifiers) compInstId.validate();
        }

        if(this.description != null && !this.description.matches(Constants.ALLOWED_NAME_CHARACTERS)){
            throw new ControlCenterException(UIMessages.ALLOWED_NAME_CHARACTERS, "Description");
        }

        validateAccountMappings();
    }

    private void validateAccountMappings() throws ControlCenterException {
        if (this.accountMappings != null && !this.accountMappings.isEmpty()) {
            for (AgentAccountMappings accountMapping : this.accountMappings) {
                if (StringUtils.isEmpty(accountMapping.getAccountIdentifier()))
                    throw new ControlCenterException("Account Identifier can not be null or empty.");
                List<Tags> tagList = accountMapping.getTags();
                if (!(tagList == null || tagList.isEmpty())) {
                    validateTags(tagList);
                }
            }
        }
    }

    private void validateTags(List<Tags> tagList) throws ControlCenterException {
        for (Tags tag : tagList) {
            if (StringUtils.isEmpty(tag.getIdentifier())) {
                throw new ControlCenterException("Tag identifier can not be null or empty.");
            }
            if (StringUtils.isEmpty(tag.getName())) {
                throw new ControlCenterException("Tag name can not be null or empty.");
            }
            if (tag.getValue() != null && tag.getValue().length() < 1) {
                throw new ControlCenterException("Tag value can not be or empty.");
            }
            if (StringUtils.isEmpty(tag.getName()) || !tag.getName().equals(Constants.CONTROLLER_TAG)) {
                throw new ControlCenterException("For tags in Agent-Account mapping name should be 'Controller'.");
            }
            if (StringUtils.isEmpty(tag.getSubTypeName()) || !tag.getSubTypeName().equals(Constants.SERVICES_CONTROLLER_TYPE)) {
                throw new ControlCenterException("For tags in Agent-Account mapping subTypeName should be 'Services'.");
            }
        }
    }
}
