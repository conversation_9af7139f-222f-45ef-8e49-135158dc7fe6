package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.common.ValueType;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.quartz.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomKpiFromUI {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomKpiFromUI.class);

    private int id;
    private String name;
    private String identifier;
    private String kpiType;
    private String description;
    private String kpiUnit;
    private int collectionInterval;
    private String clusterOperation;
    private String rollupOperation;
    private String clusterAggregation;
    private String instanceAggregation;
    private int componentId;
    private int componentTypeId;
    private int componentCommonVersionId;
    private String componentCommonVersionName;
    private int componentVersionId;
    private int availableForAnalytics;
    private int status;
    private String dataType;
    private ValueType valueType;
    private CategoryDetails kpiCategoryDetails;
    private GroupKpi groupKpiDetails;
    private ComputedKpiDetails computedKpiDetails;
    private List<ComponentMapping> components;
    private String cronExpression;
    private int deltaPerSec=1;
    private int resetDeltaValue=1;
    private String componentName;
    public boolean validateForInsert() {
        boolean retVal = true;

        if (StringUtils.isEmpty(name) || name.trim().length() > 128) {
            LOGGER.error("KpiName validation failure. Reason: kpiName is NULL or empty or its length is greater than 128 characters.");
            retVal = false;
        }

        if (this.name != null && !this.name.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "KPI name");
            retVal = false;
        }

        if (null != identifier) {
            identifier = identifier.trim();
            if (identifier.isEmpty() || identifier.length() > 128) {
                LOGGER.error("Identifier validation failure. Reason: identifier is empty or its length is greater than 128 characters.");
                retVal = false;
            }

            if (identifier.trim().length()> 0 && !identifier.matches(Constants.ALLOWED_IDENTIFIER_CHARACTERS)) {
                LOGGER.error(UIMessages.ALLOWED_IDENTIFIER_CHARACTERS, "KPI identifier");
                retVal = false;
            }
        } else {
            LOGGER.info("kpiIdentifier not provided in the request. It will be auto-generated.");
        }

        if (StringUtils.isEmpty(description) || description.length() > 256) {
            LOGGER.error("Description validation failure. Reason: description is NULL or empty or its length is " +
                    "greater than 256 characters.");
            retVal = false;
        }

        if (StringUtils.isEmpty(kpiUnit) || kpiUnit.trim().length() > 16) {
            LOGGER.error("measurementUnits validation failure. " +
                    "Reason: measurementUnits is either NULL, empty or its length is greater than 16 characters.");
            retVal = false;
        }

        if (availableForAnalytics != 1 && availableForAnalytics != 0) {
            LOGGER.error("enableAnalytics validation failure. Reason: enableAnalytics provided is [{}]. " +
                    "It should be either 0 r 1.", availableForAnalytics);
            retVal = false;
        }

        if (collectionInterval <= 0 || collectionInterval % 60 != 0) {
            LOGGER.error("collectionInterval validation failure. Reason: collectionInterval " +
                    "provided is [{}]. It should be a multiple of 60.", collectionInterval);
            retVal = false;
        }

        if (componentId <= 0) {
            LOGGER.error("componentId validation failure. Reason: componentId provided is [{}]. It should be a positive integer.", componentId);
            retVal = false;
        }

        if (componentCommonVersionId <= 0) {
            LOGGER.error("componentCommonVersionId validation failure. Reason: componentCommonVersionId provided is [{}]. " +
                    "It should be a positive integer.", componentCommonVersionId);
            retVal = false;
        }

        if (componentTypeId <= 0) {
            LOGGER.error("componentTypeId validation failure. Reason: componentTypeId provided is [{}]. " +
                    "It should be a positive integer.", componentCommonVersionId);
            retVal = false;
        }

        if (StringUtils.isEmpty(kpiType)) {
            LOGGER.error("kpiType validation failure. Reason: kpiType is either NULL or empty. " +
                    "It should be one of Availability, Core, FileWatch or ConfigWatch.");
            retVal = false;
        }

        if (StringUtils.isEmpty(dataType)) {
            LOGGER.error("dataType validation failure. Reason: dataType is either NULL or empty. " +
                    "It should be one of Integer, Float or Text.");
            retVal = false;
        }

        if (valueType == null) {
            LOGGER.error("valueType validation failure. Reason: valueType is either NULL or empty.");
            retVal = false;
        }

        if (StringUtils.isEmpty(clusterOperation)) {
            LOGGER.error("clusterOperation validation failure. Reason: clusterOperation is either NULL or empty. " +
                    "It should be one of Sum, Average or None.");
            retVal = false;
        }

        if (StringUtils.isEmpty(rollupOperation)) {
            LOGGER.error("rollupOperation validation failure. Reason: rollupOperation is either NULL or empty. " +
                    "It should be one of Sum, Average or None.");
            retVal = false;
        }

        if (StringUtils.isEmpty(clusterAggregation)) {
            LOGGER.error("clusterAggregation validation failure. Reason: clusterAggregation is either NULL or empty." +
                    "It should be one of SingleValue, MultiValue or None.");
            retVal = false;
        }

        if (StringUtils.isEmpty(instanceAggregation)) {
            LOGGER.error("instanceAggregation validation failure. Reason: instanceAggregation is either NULL or empty." +
                    "It should be one of SingleValue, MultiValue or None.");
            retVal = false;
        }

        if (groupKpiDetails != null && !groupKpiDetails.isValid()) {
            LOGGER.info("Group KPI details are invalid");
            retVal = false;
        }

        if (kpiCategoryDetails == null || !kpiCategoryDetails.validate() || kpiCategoryDetails.getId() <= 0) {
            LOGGER.info("Category details unavailable or are invalid");
            retVal = false;
        }

        if (computedKpiDetails != null && !computedKpiDetails.validateForCreate()) {
            LOGGER.error("Computed KPI details unavailable or are invalid");
            retVal = false;
        }

        if(cronExpression != null && !cronExpression.trim().isEmpty()) {
            try {
                new CronExpression(cronExpression);
            } catch (Exception e) {
                LOGGER.error("Cron expression is invalid. Details: ", e);
                retVal = false;
            }
        }

        return retVal;
    }

    public boolean validateForUpdate() {
        boolean retVal = true;

        if (name != null && (name.trim().length() > 128 || name.trim().isEmpty())) {
            LOGGER.error("KpiName validation failure. Reason: kpiName is NULL or empty or its length is greater than 128 characters.");
            retVal = false;
        }

        if (description != null && (description.length() > 256 || description.trim().isEmpty())) {
            LOGGER.error("Description validation failure. Reason: description is NULL or empty or its length is greater than 256 characters.");
            retVal = false;
        }

        if (computedKpiDetails != null && !computedKpiDetails.validateForUpdate()) {
            LOGGER.error("Computed KPI details unavailable or are invalid");
            retVal = false;
        }

        if (kpiCategoryDetails != null && (!kpiCategoryDetails.validate() || kpiCategoryDetails.getId() <= 0)) {
            LOGGER.info("Category details unavailable or are invalid");
            retVal = false;
        }


        if (collectionInterval < 0 || collectionInterval % 60 != 0) {
            LOGGER.error("collectionInterval validation failure. Reason: collectionInterval " +
                    "provided is [{}]. It should be a multiple of 60.", collectionInterval);
            retVal = false;
        }

        if (dataType != null && dataType.isEmpty()) {
            LOGGER.error("dataType validation failure. Reason: dataType is empty. " +
                    "It should be one of Integer, Float or Text.");
            retVal = false;
        }

        if (kpiUnit != null && (kpiUnit.isEmpty() || kpiUnit.trim().length() > 16)) {
            LOGGER.error("kpiUnit validation failure. Reason: kpiUnit is empty or its length is greater than 16 characters.");
            retVal = false;
        }

        if (clusterOperation != null && clusterOperation.isEmpty()) {
            LOGGER.error("clusterOperation validation failure. Reason: clusterOperation is empty. " +
                    "It should be one of Sum, Average or None.");
            retVal = false;
        }

        if (rollupOperation != null && rollupOperation.isEmpty()) {
            LOGGER.error("rollupOperation validation failure. Reason: rollupOperation is empty. " +
                    "It should be one of Sum, Average or None.");
            retVal = false;
        }

        if (clusterAggregation != null && clusterAggregation.isEmpty()) {
            LOGGER.error("clusterAggregation validation failure. Reason: clusterAggregation is empty." +
                    "It should be one of SingleValue, MultiValue or None.");
            retVal = false;
        }

        if (instanceAggregation != null && instanceAggregation.isEmpty()) {
            LOGGER.error("instanceAggregation validation failure. Reason: instanceAggregation is empty." +
                    "It should be one of SingleValue, MultiValue or None.");
            retVal = false;
        }

        if (components != null && !components.isEmpty() && components.parallelStream().anyMatch(component ->
                !component.validate())) {
            retVal = false;
        }

        if(cronExpression != null && !cronExpression.trim().isEmpty()) {
            try {
                new CronExpression(cronExpression);
            } catch (Exception e) {
                LOGGER.error("Cron expression is invalid. Details: ", e);
                retVal = false;
            }
        }

        return retVal;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ComponentMapping {
        private int id;
        private int status;
        private int unmap;

        public boolean validate() {
            if (id < 1 || (status != 0 && status != 1) || (unmap != 0 && unmap != 1)) {
                LOGGER.error("'id' of the component should be greater than 1. 'status' and 'unmap' can only be 0 or 1.");
                return false;
            }
            return true;
        }
    }
}
