package com.appnomic.appsone.controlcenter.pojo;

import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Stream;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"notificationDetails", "timezoneDetail"})
public class UserInfo {
    private int mysqlId;
    // Here `id` is user identifier
    private String id;
    private String userName;
    private String firstName;
    private String lastName;
    private String emailId;
    private String contactNumber;
    private int status;
    private int roleId;
    private int profileId;
    private int profileChange;
    private String userDetailsId;
    private List<AccessDetails> accessDetails;
    private boolean editInKeycloak;
    private NotificationDetails notificationDetails;
    private UserTimezoneResponse timezoneDetail;
    private int isTimezoneMychoice;
    private int isNotificationsTimezoneMychoice;
    private List<AccessDetails> deletedAccessDetails;

    private static final Logger LOGGER = LoggerFactory.getLogger(UserInfo.class);

    private static final String EMAIL_REGEX = "^(.+)@([a-zA-Z0-9_\\-.]+)\\.([a-zA-Z]{2,5})$";

    public boolean validate() {

        if (this.userName == null || this.userName.trim().length() < 2) {
            LOGGER.error("username : null or length is less than 2 characters - {}", this.userName);
            return false;
        }

        if (!this.userName.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Username");
            return false;
        }

        if (this.id == null && this.firstName == null) {
            LOGGER.error("firstName : null  - {}", this.firstName);
        }

        if (this.firstName != null && (this.firstName.trim().length() < 2 || this.firstName.trim().length() > 255)) {
            LOGGER.error("firstName : length is less than 2 characters or greater than 255 characters - {}", this.firstName);
            return false;
        }

        if (this.firstName != null && !this.firstName.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "First name");
            return false;
        }

        if (this.lastName != null && this.lastName.trim().length() > 255) {
            LOGGER.error("firstName : null or length is greater than 255 characters - {}", this.lastName);
            return false;
        }

        if (this.lastName != null && !this.lastName.matches(Constants.ALLOWED_NAME_CHARACTERS)) {
            LOGGER.error(UIMessages.ALLOWED_NAME_CHARACTERS, "Last name");
            return false;
        }

        if (this.roleId < 0 || this.profileId < 0) {
            LOGGER.error("roleId ({}) or profileId ({}) invalid.", this.roleId, this.profileId);
            return false;
        }

        if (this.status != 0 && this.status != 1) {
            LOGGER.error("status invalid - {}", this.status);
            return false;
        }

        if (this.emailId != null) {
            if (this.emailId.isEmpty() || this.emailId.length() > 256) {
                LOGGER.error("emailId ({}) invalid : max 256 characters are allowed.", this.emailId);
                return false;
            }
            if (!this.emailId.matches(EMAIL_REGEX)) {
                LOGGER.error("emailId ({}) invalid : Not matching the pattern (^(.+)@([a-zA-Z0-9_\\-.]+)\\.([a-zA-Z]{2,5})$).", this.emailId);
                return false;
            }
        }

        if (this.contactNumber != null && !this.contactNumber.isEmpty()) {
            String[] contactNumbers = this.contactNumber.split(",");
            if(this.contactNumber.length() > 256) {
                LOGGER.error("Contact Number details should have maximum 256 characters in {}", this.contactNumber);
                return false;
            }
            if(Stream.of(contactNumbers).anyMatch(contactNum -> contactNum.length() < 10 || contactNum.length() > 16)) {
                LOGGER.error("Contact Number should have 10 to 16 characters in {}", this.contactNumber);
                return false;
            }
        }

        if (this.accessDetails != null && !this.accessDetails.isEmpty()) {
            if (this.accessDetails.parallelStream().anyMatch(a -> a.getAccountId().equals("*")) && this.accessDetails.size() != 1) {
                LOGGER.error("User has all account access '*'. No other accessDetails should be present. {}", this.accessDetails);
                return false;
            }
            for (AccessDetails a : this.accessDetails) {
                if (!a.getAccountId().equals("*") && !a.validate()) {
                    LOGGER.error("accessDetails invalid. {}", this.accessDetails);
                    return false;
                }
            }
        }

        return true;

    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccessDetails {
        private String action;
        private Object accountId;
        private List<UserApp> applications;

        public boolean validate() {
            if (Integer.parseInt(String.valueOf(this.accountId)) < 1) {
                LOGGER.error("AccessDetails - accountId ({}) invalid.", this.accountId);
                return false;
            }
            if (this.applications != null && !this.applications.isEmpty()) {
                for (UserApp a : this.applications) {
                    if (a.ids.contains("*") && this.applications.size() != 1) {
                        LOGGER.error("User has access to all applications in the account '*'. " +
                                "No other application should be provided. {}", this.applications);
                        return false;
                    }

                    if (!a.ids.contains("*") && !a.validate()) {
                        LOGGER.error("applications invalid. {}", this.applications);
                        return false;
                    }
                }
            }
            return true;
        }


        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class UserApp {

            private String action;
            private List<String> ids;

            public boolean validate() {
                for (String i : ids) {
                    int app;
                    try {
                        app = Integer.parseInt(i);
                    } catch (Exception e) {
                        LOGGER.error("AccessDetails - application ({}) invalid.", i);
                        return false;
                    }
                    if (app < 1) {
                        LOGGER.error("AccessDetails - applications ({}) invalid.", i);
                        return false;
                    }
                }
                return true;
            }
        }
    }

    @Data
    public static class NotificationDetails {

        private int emailEnabled;
        private int smsEnabled;
        private List<ApplicationDetails> applicationDetails;
    }
}
