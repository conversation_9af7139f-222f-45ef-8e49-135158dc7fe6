package com.appnomic.appsone.controlcenter.rest;

import com.appnomic.appsone.common.beans.MaintenanceWindowBean;
import com.appnomic.appsone.common.beans.discovery.Entity;
import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.beans.autodiscovery.Component;
import com.appnomic.appsone.controlcenter.cache.CCCache;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.ResponseObject;
import com.appnomic.appsone.controlcenter.dao.mysql.ConfigurationDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.FileUploadDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.CommandDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.WebHookDataBean;
import com.appnomic.appsone.controlcenter.pojo.Agent;
import com.appnomic.appsone.controlcenter.pojo.IdPojo;
import com.appnomic.appsone.controlcenter.pojo.Supervisor;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.pojo.agentconfig.AccountConfiguration;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.GetCompInstance;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.GetConnection;
import com.appnomic.appsone.controlcenter.pojo.autodiscovery.GetHost;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorDetails;
import com.appnomic.appsone.controlcenter.pojo.connectors.ConnectorTemplate;
import com.appnomic.appsone.controlcenter.pojo.connectors.TemplateUploadStatus;
import com.appnomic.appsone.controlcenter.service.ServiceConfiguration;
import com.appnomic.appsone.controlcenter.service.*;
import com.appnomic.appsone.controlcenter.service.connectors.ConnectorControllerService;
import com.appnomic.appsone.controlcenter.service.connectors.GetConnectorDetailsService;
import com.appnomic.appsone.controlcenter.service.connectors.UploadConnectorTemplateService;
import com.heal.configuration.pojos.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Route;

import java.util.List;
import java.util.Map;
import java.util.Set;

class ControlCenterService {
    private static final Logger log = LoggerFactory.getLogger(ControlCenterService.class);
    private static final String LOG_CALLED_API_STRING = "Called API {} at time {}";

    static Route auraUsersSync = (request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), st);
        AuraSyncService auraSyncService = new AuraSyncService();
        GenericResponse<String> genericResponse = auraSyncService.syncAuraUsers(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
       return genericResponse;
    };

    static Route keycloakSettings = (request, response) -> {
        long st = System.currentTimeMillis();
        KeycloakSettingsResponse keycloakSettingsResponse = ConfigurationDataService.getKeyCloakSettings();
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return keycloakSettingsResponse;
    };

    static Route compConfigData = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<AccountConfiguration>> genericResponse = ConfigurationDataService.getAgentConfigurationDetails(request);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route agentRules = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<RulesBean>> genericResponse = ConfigurationDataService.getRulesConfigDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getNotificationSettings = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<NotificationSettingsBean>> genericResponse = NotificationSettingsService.getNotificationSettings(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateNotificationSettings = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = NotificationSettingsService.updateNotificationSettings(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getEntityCount = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetEntityCountService service = new GetEntityCountService();
        GenericResponse<Map<String, Object>> genericResponse = service.getEntityCount(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getForensicActions = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetForensicActionsService service = new GetForensicActionsService();
        GenericResponse<List<ForensicActionsPojo>> genericResponse = service.getForensicActions(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getKpiList = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetKPIsService service = new GetKPIsService();
        GenericResponse<List<KpiDetailsPojo>> genericResponse = service.getKPIs(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getHealthOfInstances = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<InstanceHealthDetails>> genericResponse = new InstanceHealthService().getInstanceHealthBreakage(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getAutoDiscoveryComponents = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), st);
        AutoDiscoveryKnownCompService autoDiscoveryKnownCompService = new AutoDiscoveryKnownCompService();
        GenericResponse<List<Component>> genericResponse = autoDiscoveryKnownCompService.getComponentAttributeInfo(response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAutoDiscoveryConfigurationEntities = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), st);
        AutoDiscoveryConfigurationEntityService autoDiscoveryConfigurationEntityService = new AutoDiscoveryConfigurationEntityService();
        GenericResponse<List<Entity>> genericResponse = autoDiscoveryConfigurationEntityService.getConfigurationEntities(response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getServiceConfigurations = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        ServiceConfiguration service = new ServiceConfiguration();
        GenericResponse<Map<String, ServiceConfigDetails>> genericResponse = service.getServiceConfigurationDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postServiceConfigurations = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        ServiceConfiguration service = new ServiceConfiguration();
        GenericResponse<String> genericResponse = service.updatePersistenceSuppression(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postCategories = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new AddCategoryService().addCategory(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getCategories = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<CategoryDetails>> genericResponse = new GetCategoriesService().getCategories(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putCategories = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new UpdateCategoryService().updateCategory(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteCategories = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new DeleteCategoryService().deleteCategory(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAvailabilityCategories = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<GetCategory>> genericResponse = new GetAvailabilityCategoriesService().getAvailabilityCategories(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postActions = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = ActionScriptService.addActions(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route commandAudit = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        ResponseObject<String> genericResponse = new CommandAuditService().addCommandAuditData(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postInstancesOld = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        ComponentInstanceService componentInstanceService = new ComponentInstanceService();
        ResponseObject<Object> genericResponse = componentInstanceService.addComponentInstance(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);

        return genericResponse;
    });

    static Route getInstancesAtAccLevel = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<GetCompInstance>> genericResponse = new GetCompInstanceAtAccLvlService().getComponentInstanceDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postInstances = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<IdPojo>> genericResponse = ComponentInstanceService.addComponentInstances(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateInstances = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ComponentInstanceService().updateComponentInstance(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateInstanceName = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateCompInstanceNameService().updateInstanceName(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteInstances = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = DeleteInstanceService.removeInstance(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getMetricGroups = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<IdPojo>> genericResponse = new GetMetricGroupsService().getMetricGroups(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getMetricGroupAttributes = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<Set<MetricGroupAttribute>> genericResponse = new GetMetricGroupAttributesService().getMetricGroupAttributes(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putMetricGroupAttributes = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateMetricGroupAttributesService().updateMetricGroupAttributes(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getCategoryForensics = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<CategoryForensicsPOJO> genericResponse = new GetCategoryForensicsService().getCategoryForensics(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putCategoryForensics = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateCategoryForensicsService().updateCategoryForensics(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postClusters = ((request, response) -> {
		long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        ComponentInstanceService componentInstanceService = new ComponentInstanceService();
        ResponseObject<String> genericResponse = componentInstanceService.addCluster(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);

        return genericResponse;
    });

    static Route postHosts = ((request, response) -> {
		long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        ComponentInstanceService componentInstanceService = new ComponentInstanceService();
        ResponseObject<Object> genericResponse = componentInstanceService.addHost(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);

        return genericResponse;
    });

    static Route getHosts = ((request, response) -> {
		long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetHostService getHostService = new GetHostService();
        GenericResponse<List<GetHost>> genericResponse = getHostService.getHost(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getActiveLicenseHosts = ((request, response) -> {
		long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetActiveLicenseHostsService getActiveLicenseHosts = new GetActiveLicenseHostsService();
        GenericResponse<Map<String, Object>> genericResponse = getActiveLicenseHosts.getActiveLicenseHosts(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getEntityTags = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetEntityTagsService getEntityTagsService = new GetEntityTagsService();
        GenericResponse<List<EntityTags>> genericResponse = getEntityTagsService.getEntityTags(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateInstanceDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        UpdateInstanceService updateInstanceService = new UpdateInstanceService();
        GenericResponse<Object> genericResponse = updateInstanceService.updateInstance(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateInstancesEnvDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<Object> genericResponse = ComponentInstanceEnvUpdateService.updateComponentInstanceEnvDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postTransactions = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        TransactionService transactionService = new TransactionService();
        ResponseObject genericResponse = transactionService.addTransaction(request);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        return genericResponse;
    });

    static Route postTransactionWrappers = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.debug(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        TransactionService transactionService = new TransactionService();
        ResponseObject genericResponse = transactionService.addTransactionWrapper(request);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        return genericResponse;
    });

    static Route postSupervisors = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.debug(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AddSupervisorService().addSupervisor(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateSupervisor = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.debug(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateSupervisorService().updateSupervisor(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getSupervisors = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.debug(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<Supervisor>> genericResponse = new GetSupervisorsService().getSupervisors(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postAccounts = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AccountService accountService = new AccountService();
        GenericResponse genericResponse = accountService.addAccount(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAccounts = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AccountResponse genericResponse = AccountService.getAccountList(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getTimezones = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        TimezoneResponse genericResponse = new TimezoneService().getAllTimezones();
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getServiceTransactions = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<ServiceTransactionDetailsPojo>> genericResponse = TransactionService.getTxnDetailsList(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getTransactionThresholds = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<TransactionStaticThresholds>> genericResponse = TransactionThresholdService.getStaticThresholds(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postTransactionThresholds = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<IdPojo>> genericResponse = TransactionThresholdService.createThresholds(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });


    static Route getServiceTransactionGroupTags = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<String>> genericResponse = TransactionGroupingService.getTransactionTags(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postServiceTransactionGroupTags = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new TagRequestDetailsService().tagRequestDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getTransactionGroupTags = (request, response) -> {
		long st = System.currentTimeMillis();
		GenericResponse<String> genericResponse = new TagRequestDetailsService().tagRequest(request, response);
		CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
	};

    static Route getServiceRules = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<Map<String, Object>>> genericResponse = new GetRulesService().getRuleDetailsList(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getServiceRule = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<RuleDetailsPojo> genericResponse = new GetRulesService().getRuleDetailsByRuleId(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route downloadRules = (request, response) -> {
        long st = System.currentTimeMillis();
        Object genericResponse = new GetRulesService().getRuleDetailsDownload(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        return genericResponse;
    };

    static Route postMaintenanceWindow = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<MaintenanceWindowBean> genericResponse = new AddMaintenanceWindowService().addMaintenanceWindowForService(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteMaintenanceWindow = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<Integer> genericResponse = new DeleteMaintenanceWindowService().deleteMaintenanceWindowForService(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getMaintenanceWindow = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<MaintenanceDetailsBean>> genericResponse = new GetMaintenanceWindowService().getMaintenanceWindow(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateMaintenanceWindow = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateMaintenanceWindowService().updateMaintenanceWindowForService(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAdhocMaintenanceWindow = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<MaintenanceDetailsBean>> genericResponse = new GetAdhocMaintenanceWindowService().getAdhocMaintenanceWindow(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postAdhocMaintenanceWindow = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<MaintenanceWindowBean> genericResponse = new AddAdhocMaintenanceWindowService().addMaintenanceWindow(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteAdhocMaintenanceWindow = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<Integer> genericResponse = new DeleteAdhocMaintenanceWindowService().deleteMaintenanceWindow(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postServiceRules = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<Integer> genericResponse = new AddRulesService().addRules(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postServiceRulesOrder = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<String> genericResponse = new UpdateRulesService().updateOrder(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postAgents = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AgentService agentService = new AgentService();
        GenericResponse<String> genericResponse = agentService.addAgent(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteAgents = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AgentService().remAgent(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateAgents = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateAgentService().updateAgents(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postApplicationsOld = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        ResponseObject genericResponse = new ApplicationService().addApplications(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        return genericResponse;
    });

    static Route getConnections = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<GetConnection>> genericResponse = new GetConnectionService().getConnections(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postConnections = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<IdPojo>> genericResponse = new AddConnectionService().addConnection(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteConnection = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new DeleteConnectionService().remConnection(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateServiceRules = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        UpdateRuleService updateRuleService = new UpdateRuleService();
        GenericResponse<String> genericResponse = updateRuleService.update(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });
    static Route updateTransactions = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        UpdateTransactionService updateTransactionService = new UpdateTransactionService();
        GenericResponse<String> genericResponse = updateTransactionService.update(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });
    static Route viewSummary = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        TransactionCountService transactionCountService = new TransactionCountService();
        GenericResponse<TransactionCountBean> genericResponse = transactionCountService.transactionCount(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });
    static Route processConfiguration = (request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UploadServiceApplicationCsv().uploadServiceApplicationDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getServices = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<ServiceListPage>> genericResponse = ServiceDetails.getServiceListPage(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route postServiceAgentCommands = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new CommandService().saveAgentCommand(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postMetricDetailsForService = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<MetricDetails>> genericResponse = new GetMetricDetailsService().getMetricDetailsForService(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putMetricDetailsForService = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateMetricDetailsService().updateMetricDetailsForService(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getServiceAgentCommands = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<AgentCommand> genericResponse = new CommandService().getAgentCommands(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postCommandStatus = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new CommandService().updateCommandStatus(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getInstallationAttributes = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        InstallationResponse genericResponse = FileUploadDataService.getInstallationAttributes();
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getJimCommands = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<AgentCommand> genericResponse = new CommandService().getJIMAgentCommands(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route generateSignature = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AccountService().getFileSignature(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postProducers = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AddProducerService producerService = new AddProducerService();
        GenericResponse<Map<Integer, String>> genericResponse = producerService.addProducer(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getProducers = ((request, response) -> {
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        return new GetProducerService().getProducers(request, response);
    });

    static Route getProducerKpiMapping = ((request, response) -> {
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        return new GetProducerKpiMappingService().getProducerKpiMapping(request, response);
    });

    static Route postComponents = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        ComponentService service = new ComponentService();
        GenericResponse<IdPojo> genericResponse = service.addComponent(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postKpiGroups = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse genericResponse = new AddGroupKpiService().addGroupKpi(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postNonGroupKpis = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new AddKpiService().addKpi(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postCustomKpis = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new AddCustomKPIService().addCustomKPI(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putCustomKpis = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateCustomKpiService().updateCustomKPI(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteCustomKpi = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new DeleteCustomKPIService().deleteCustomKPI(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAgentTypes = ((request, response) -> {
        long st = System.currentTimeMillis();
        AgentTypeService agentTypeService = new AgentTypeService();
        GenericResponse genericResponse = agentTypeService.getAgentType(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAgentTypesAtAccLvl = ((request, response) -> {
        long st = System.currentTimeMillis();
        GetAgentTypeAtAccLvlService getAgentTypeAtAccLvlService = new GetAgentTypeAtAccLvlService();
        GenericResponse<List<AgentTypePojo>> genericResponse = getAgentTypeAtAccLvlService.getAgentType(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getComponentAttributes = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<ComponentAttributesMapping>> genericResponse = new GetComponentAttributesService().getComponentAttributesData(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAgentInstances = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<CompInstancePojo>> genericResponse = AgentStatusService.getInstance(request,response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getServicePageAttributes = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<ServicePageAttribute>> genericResponse = ServicePageAttributesService.getServicePageAttributes(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getInstances = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<ComptInstancePojo>> genericResponse = new GetComponentInstanceService().getComponentInstances(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getInstanceAttributes = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<CompInstAttributesPojo>> genericResponse = new GetCompInstAttributesService().getComponentInstanceAttributes(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route editInstanceAttributes = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<CompInstAttributesBean>> genericResponse = new UpdateCompInstAttributesService().editComponentInstanceAttributes(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAgentHealth = ((request, response) -> {
        long st = System.currentTimeMillis();
        AgentStatusService agentStatusService = new AgentStatusService();
        GenericResponse<List<AgentStatusPojo>> genericResponse = agentStatusService.getAgentStatus(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postCommandTrigger = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        TriggerAgentCommandService statusCommandService = new TriggerAgentCommandService();
        GenericResponse<List<AgentCommandTriggeredStatusPojo>> genericResponse = statusCommandService.triggerAgentCommand(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getCommandTriggerStatus = (request, response) -> {
        long st = System.currentTimeMillis();
        GetAgentStatusCmdReportingService statusCommandReport = new GetAgentStatusCmdReportingService();
        GenericResponse<List<CommandTriggerStatusPojo>> genericResponse = statusCommandReport.getCommandTriggerStatus(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getPageDetails = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<InstanceHealthPagePojo>> genericResponse = InstanceHealthPageService.getInstanceHealthPageDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getServicesClusters = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<ServiceClusterPojo>> genericResponse = new GetServicesClustersService().getServicesClustersMap(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getApplications = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<GetApplication>> genericResponse = ApplicationService.getApplications(request,response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postApplications = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = ApplicationService.add(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateApplication = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<Integer> genericResponse = ApplicationService.editApplication(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteApplications = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new DeleteApplicationsService().deleteApplications(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAppPercentiles = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<ApplicationPercentiles> genericResponse = new GetAppPercentilesService().getAppPercentiles(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateAppPercentiles = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateAppPercentilesService().updateAppPercentiles(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postParentApplications = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AddParentApplicationsService().addParentApplication(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getParentApplications = (request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<ParentApplication>> genericResponse = new GetParentApplicationsService().getParentApplication(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route deleteParentApplication = (request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new DeleteParentApplicationsService().deleteParentApplication(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route postServices = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<IdPojo>> genericResponse = new ControllerService().addServices(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateServiceDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new UpdateService().updateService(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteService = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ControllerService().deleteService(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getServiceThresholds = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<StaticThresholdRules>> genericResponse = StaticThresholdService.getStaticThresholds(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postServiceThresholds = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<StaticThresholdRules>> genericResponse = StaticThresholdService.createThresholds(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getWebHook = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<WebHookDataBean> genericResponse = WebHookService.getWebHook(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postWebHook = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse genericResponse = WebHookService.addWebHook(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateWebHook = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse genericResponse = WebHookService.updateWebHook(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteWebHook = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse genericResponse = WebHookService.remWebHook(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postServiceEntryPoint = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        TaggingService taggingService = new TaggingService();
        GenericResponse genericResponse = taggingService.addTagging(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteServiceEntryPoint = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        TaggingService taggingService = new TaggingService();
        GenericResponse genericResponse = taggingService.deleteTagging(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postSmsConfigurations = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new SmsService().addSMSConfiguration(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getSmsConfigurations = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<SmsDetails> genericResponse = new SmsService().getSmsConfiguration(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postEmailConfigurations = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new EmailService().addEmailConfiguration(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getEmailConfigurations = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<SmtpDetails> genericResponse = new EmailService().getEmailConfiguration(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateSmsConfigurations = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new SmsService().updateSmsConfigurations(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateEmailConfigurations = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new EmailService().updateEmailConfiguration(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getUserNotifPreferences = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<NotificationsPojo> genericResponse = NotificationService.getNotifications(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postUserNotifPreferences = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = NotificationService.saveNotifications(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAccountNotifPreferences = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse genericResponse = ApplicationNotificationService.getNotificationConfiguration(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postAccountNotifPreferences = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse genericResponse = ApplicationNotificationService.updateNotificationConfiguration(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getUserAccessInformation = ((request, response) -> {
//        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        UserAccessibleActionService service = new UserAccessibleActionService();
        GenericResponse<UserAccessibleActions> genericResponse = service.getUserAccessibilityDetails(request, response);
//        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
//        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getUserProfiles = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse genericResponse = UserProfileService.getUserProfiles(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route auditTrailService = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AuditTrailService auditTrailService = new AuditTrailService();
        GenericResponse<String> genericResponse = auditTrailService.getAuditAuditData(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route componentDetailsService = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetComponentDetailsService getComponentDetailsService = new GetComponentDetailsService();
        GenericResponse<List<ComponentDetails>> genericResponse = getComponentDetailsService.getComponentDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });


    static Route postInstanceKpiThresholds = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AddInstanceLevelKpiThresholdService service = new AddInstanceLevelKpiThresholdService();
        GenericResponse<String> genericResponse = service.addThresholds(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putInstanceKpiThresholds = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        UpdateInstanceLevelKpiThresholdService service = new UpdateInstanceLevelKpiThresholdService();
        GenericResponse<String> genericResponse = service.updateThresholds(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getInstanceKpiThresholds = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetInstanceLevelKpiThresholdService service = new GetInstanceLevelKpiThresholdService();
        GenericResponse<KpiAttrThresholdInfo> genericResponse = service.getThresholds(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteInstanceKpiThresholds = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        DeleteInstanceLevelKpiThresholdService service = new DeleteInstanceLevelKpiThresholdService();
        GenericResponse<String> genericResponse = service.deleteThresholds(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postInstanceKpiPersistenceSuppressionConfig = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AddInstanceKpiPersistSuppService service = new AddInstanceKpiPersistSuppService();
        GenericResponse<String> genericResponse = service.addPersistenceSuppression(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putInstanceKpiPersistenceSuppressionConfig = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        UpdateInstanceKpiPersistSuppService service = new UpdateInstanceKpiPersistSuppService();
        GenericResponse<String> genericResponse = service.updatePersistenceSuppression(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getInstanceKpiPersistenceSuppressionConfig = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetKpiPersistenceSuppressionService service = new GetKpiPersistenceSuppressionService();
        GenericResponse<KpiAttrConfigInfo> genericResponse = service.getPersistenceSuppressionConfig(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteInstanceKpiPersistenceSuppressionConfig = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        DeleteInstanceKpiPersistSuppService service = new DeleteInstanceKpiPersistSuppService();
        GenericResponse<String> genericResponse = service.deletePersistenceSuppressionConfig(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateWhatsappOptIn = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        WhatsappService whatsappService = new WhatsappService();
        GenericResponse<String> genericResponse = whatsappService.updateWhatsappOptIn(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getWhatsappOptIn = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        WhatsappService whatsappService = new WhatsappService();
        GenericResponse<WhatsappOptInResponse> genericResponse = whatsappService.getWhatsappOptIn(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route webhookWhatsappOptIn = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        WhatsappService whatsappService = new WhatsappService();
        GenericResponse<String> genericResponse = whatsappService.webhookWhatsappOptIn(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postProcessDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new AddBatchProcessService().addBatchProcess(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putProcessDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateBatchProcessService().updateBatchProcessDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteProcessDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new DeleteBatchProcessService().disableBatchProcess(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteUser = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<String> genericResponse = DeleteUserService.deleteUser(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getUsers = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<String> genericResponse = UserService.getUsers(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route validateUsername = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<String> genericResponse = UserService.validateUserName(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route putUserNotifications = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateUserNotificationService().updateUserNotificationDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route addUser = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<String> genericResponse = UserService.add(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route putUsers = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateUsersService().updateUsers(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route editUser = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<String> genericResponse = UserService.update(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getUserDetails = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<String> genericResponse = UserService.getUserDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getRoles = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<IdPojo>> genericResponse = UserService.getRoles(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getProfiles = (request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<List<IdPojo>> genericResponse = UserService.getProfilesForRoleId(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    };

    static Route getWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<WhitelistPojo> genericResponse = new WhitelistService().getWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route addApplicationWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ApplicationWhitelistService().addApplicationWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateApplicationWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ApplicationWhitelistService().updateApplicationWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteApplicationWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ApplicationWhitelistService().deleteApplicationWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route addServiceWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ServiceWhitelistService().addServiceWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateServiceWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ServiceWhitelistService().updateServiceWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteServiceWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ServiceWhitelistService().deleteServiceWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route addKPIWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new KPIWhitelistService().addKPIWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateKPIWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new KPIWhitelistService().updateKPIWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteKPIWhitelist = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new KPIWhitelistService().deleteKPIWhitelist(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateWhitelistSettings = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new WhitelistSettingService().updateWhitelistSettings(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAgentHeartbeatDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<AgentHeartBeatPojo>> genericResponse = new GetAgentHeartbeatDetailsService().getAgentHeartbeatDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateAgentHeartbeatDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateAgentHeartbeatDetailsService().updateAgentHeartbeatDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putInstanceKpiMaintenanceStatus = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateKpiMaintenanceStatusService().updateKpiMaintenanceStatus(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postMapToService = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AutoDiscoveryMapEntityToService mapToService = new AutoDiscoveryMapEntityToService();
        GenericResponse genericResponse = mapToService.mapEntity(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postEntityStatus = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AutoDiscoveryEntityStatusService autoDiscoveryEntityStatusService = new AutoDiscoveryEntityStatusService();
        GenericResponse genericResponse = autoDiscoveryEntityStatusService.isIgnored(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route pushDiscoveryData = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new PushDiscoveryDataService().pushDiscoveryData(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getIgnoredEntities = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        AutoDiscoveryIgnoredEntitiesService adIgnoredEntities = new AutoDiscoveryIgnoredEntitiesService();
        GenericResponse genericResponse = adIgnoredEntities.ignoredEntities(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route addToSystem = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AutoDiscoveryService().addToSystem(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAgentList = ((request, response) -> {
        long st = System.currentTimeMillis();
        AgentService agentService = new AgentService();
        GenericResponse<List<Agent>> genericResponse = agentService.getAgentList(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAgentMappingForCompInstance = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<Set<AgentInstanceMappingDetails>> genericResponse = new GetAgentMappingForCompInstanceService().getAgentMappingForCompInstance(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route putAgentMappingForCompInstance = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateAgentMappingForCompInstanceService().updateAgentMappingForCompInstance(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postAgentsConfig = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new AgentConfigService().addAgent(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateAgentsConfig = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<IdPojo> genericResponse = new AgentConfigService().updateAgent(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route deleteAgentsConfig = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AgentConfigService().deleteAgent(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAgentDataSourcesAtAccLevel = ((request, response) -> {
        long st = System.currentTimeMillis();
        GetAgentDataSourcesAtAccLevelService agentDataSourcesAtAccLevelService = new GetAgentDataSourcesAtAccLevelService();
        GenericResponse<List<String>> genericResponse = agentDataSourcesAtAccLevelService.getAgentDataSource(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route updateAgentsDataSources = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AgentConfigService().updateAgentsDataSources(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });
    static Route getInstallationLogs = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new GetInstallationLogsService().getInstallationLogDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getConnectorsDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<List<ConnectorDetails>> genericResponse = new GetConnectorDetailsService().getConnectorsDetails(request,response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getConnectorTemplate = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<ConnectorTemplate> genericResponse = new GetConnectorDetailsService().getConnectorTemplate(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getConnectorTemplateConfig = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<ConnectorTemplate> genericResponse = new GetConnectorDetailsService().getConnectorTemplateConfig(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route uploadConnectorTemplate = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UploadConnectorTemplateService().uploadConnector(request,response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getTemplateUploadStatus = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<TemplateUploadStatus> genericResponse = new GetConnectorDetailsService().getTemplateUploadStatus(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route connectorControllerActions = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<String> genericResponse = new ConnectorControllerService().triggerConnectorAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route connectorCommandStatus = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<List<ConnectorDetails.ConnectorCommandStatus>> genericResponse = new ConnectorControllerService().connectorCommandStatus(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route postInstanceKpiSeverityAnomaly = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = new UpdateKpiSeverityAnomaly().updateKpiSeverityAnomaly(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getConfiguredSchedulers = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<List<Schedulers>> genericResponse = new GetSchedulersService().getSchedulers(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getConfiguredScheduledJobs = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<List<ScheduledJob>> genericResponse = new GetScheduledJobService().getScheduledJobs(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getTriggeredScheduledJobs = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<List<TriggeredJob>> genericResponse = new GetTriggeredJobsService().getTriggeredJobs(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route performRequestedSchedulerJobAction = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<String> genericResponse = new RequestedJobActionService().performRequestedAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getRequestsByTransactionStatus = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<List<RequestedTransactionTypeEntity>> genericResponse = new GetTransactionsService().performRequestedAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route addRequestDiscoveryStatus = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AddRequestDiscoveryStatusService().performRequestedAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getCommitDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<CommitDetailsPojo> genericResponse = new GetCommitDetailsService().performRequestedAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getAutoAcceptanceSettings = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<AutoAcceptanceSettingsPojo> genericResponse = new GetAutoAcceptanceSettingsService().performRequestedAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route addAutoAcceptanceSettings = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING,request.pathInfo(),System.currentTimeMillis());
        GenericResponse<String> genericResponse = new AddAutoAcceptanceSettingsService().performRequestedAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getLicenseDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GetActiveLicenseHostsService activeLicenseHosts = new GetActiveLicenseHostsService();
        GenericResponse<List<LicenseInfo>> genericResponse = activeLicenseHosts.getLicenseDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });


    static Route updateApplicationNotificationSettings = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<String> genericResponse = ApplicationNotificationSettingService.editApplicationNotificationSettings(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getApplicationNotificationSettings = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<NotificationSettingsBean>> genericResponse = ApplicationNotificationSettingService.getApplicationNotificationSettings(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getServiceJIMAgents = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<AgentInstanceMapping>> genericResponse = JIMAgentsForensicActionsService.getJIMAgentByService(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getJIMForensicActions = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<CommandDetailsBean>> genericResponse = JIMAgentsForensicActionsService.getJIMAgentForensicActions(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getTriggeredForensicsList = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<TriggeredForensics>> genericResponse = JIMAgentsForensicActionsService.getTriggeredForensicsList(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route triggerAction = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<ForensicCmdTriggeredStatus>> genericResponse = JIMAgentsForensicActionsService.triggerAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route BulkTriggerAction = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<ForensicCmdTriggeredStatus>> genericResponse = JIMAgentsForensicActionsService.triggerBulkAction(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });
    static Route addInstanceMetadata = ((request, response) -> {
        long st = System.currentTimeMillis();
        GenericResponse<String> genericResponse = new UploadInstanceMetadataCSVService().uploadInstanceMetadataCSV(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });

    static Route getEnvironmentDetails = ((request, response) -> {
        long st = System.currentTimeMillis();
        log.trace(LOG_CALLED_API_STRING, request.pathInfo(), System.currentTimeMillis());
        GenericResponse<List<ObjPojo>> genericResponse = new GetEnvironmentDetailsService().getEnvDetails(request, response);
        CCCache.INSTANCE.updateResponseTime(request.pathInfo(), System.currentTimeMillis() - st);
        CCCache.INSTANCE.updateStatusCodes(response.status(), 1);
        return genericResponse;
    });
}
