package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.GetCommitDetailsBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.CommitDetailsPojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class GetCommitDetailsService {
    public GenericResponse<CommitDetailsPojo> performRequestedAction(Request request, Response response) {
        GenericResponse<CommitDetailsPojo> responseObject = new GenericResponse<>();

        RequestObject requestObject = new RequestObject(request);

        GetCommitDetailsBL getCommitDetails = new GetCommitDetailsBL();

        UtilityBean<String> utilityBean;

        try {
            utilityBean = getCommitDetails.clientValidation(requestObject);
            utilityBean = getCommitDetails.serverValidation(utilityBean);
            CommitDetailsPojo commitDetails = getCommitDetails.process(utilityBean);
            responseObject.setData(commitDetails);
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            response.status(Constants.SUCCESS_STATUS_CODE);

            return responseObject;
        } catch (ClientException | ServerException e) {
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), null, true);
        } catch (Exception e) {
            log.error("Error occurred while performing requested action on the transaction requests. Details: ", e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, e.getMessage(), null, true);
        }
    }
}
