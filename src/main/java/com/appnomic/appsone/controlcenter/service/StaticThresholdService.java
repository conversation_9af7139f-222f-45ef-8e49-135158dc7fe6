package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.businesslogic.StaticThreshold;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.exceptions.StaticThresholdException;
import com.appnomic.appsone.controlcenter.pojo.Controller;
import com.appnomic.appsone.controlcenter.pojo.StaticThresholdRules;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import java.util.List;


public class StaticThresholdService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StaticThresholdService.class);
    private static final String KPI_TYPE = "kpiType";
    private static final String ACCOUNT_STR = ":identifier";

    private StaticThresholdService() {
        //Dummy constructor to hide the implicit one.
    }

    public static GenericResponse<List<StaticThresholdRules>> getStaticThresholds(Request request, Response response) {
        GenericResponse<List<StaticThresholdRules>> responseObject = new GenericResponse<>();
        List<StaticThresholdRules> staticThresholdByKpiId;
        String accountIdentifier = request.params(ACCOUNT_STR);
        Controller service;
        try {
            StaticThreshold.validateRequestParameters(request);
            service = StaticThreshold.serverValidation(request);
            String kpiType = request.queryParams(KPI_TYPE);
            staticThresholdByKpiId = StaticThreshold.processStaticThresholds(service, kpiType, accountIdentifier);
        } catch (StaticThresholdException e) {
            CommonUtils.populateErrorResponse(responseObject, response, e.getMessage(), Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        }

        if (staticThresholdByKpiId.isEmpty()) {
            responseObject = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), Constants.SUCCESS_STATUS_CODE, "Static threshold list unavailable for the account identifier: " + accountIdentifier + ",service id: " + service.getAppId(), null, false);
            return responseObject;
        }
        LOGGER.debug("Static thresholds list is successfully fetched");
        responseObject = CommonUtils.getGenericResponse(response, StatusResponse.SUCCESS.name(), Constants.SUCCESS_STATUS_CODE, "List is fetched successfully.", null, false);
        responseObject.setData(staticThresholdByKpiId);
        return responseObject;
    }

    public static GenericResponse<List<StaticThresholdRules>> createThresholds(Request request, Response response) {
        GenericResponse<List<StaticThresholdRules>> responseObject = new GenericResponse<>();
        List<StaticThresholdRules> invalidJsonObjects;
        long st = System.currentTimeMillis();
        try {

            LOGGER.debug("Incoming JSON request: {}", request.body());

            String userId = CommonUtils.getUserId(request);
            List<StaticThresholdRules> sorRuleList = StaticThreshold.clientValidation(request);
            Controller service = StaticThreshold.serverValidation(request);

            invalidJsonObjects = StaticThreshold.updateProcess(request, service, userId, sorRuleList);
            if (invalidJsonObjects.size() == sorRuleList.size()) {
                LOGGER.error("All the KPI static thresholds provided are invalid");
                CommonUtils.populateErrorResponse(responseObject, response, "All the KPI static thresholds provided are invalid. please refer error messages in data.", Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
                responseObject.setData(invalidJsonObjects);
                return responseObject;
            }
            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(StatusResponse.SUCCESS.name());
            responseObject.setData(invalidJsonObjects);
            return responseObject;

        } catch (StaticThresholdException e) {
            LOGGER.error("Error while creating static thresholds. Reason: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE);
            return responseObject;
        } catch (Exception e) {
            LOGGER.error("Error while creating static thresholds. Reason: {}", e.getMessage(), e);
            responseObject.setResponseStatus(StatusResponse.FAILURE.name());
            responseObject.setMessage(e.getMessage());
            response.status(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE);
            return responseObject;
        } finally {
            LOGGER.debug("Time taken to complete rules creation is {} ms.", System.currentTimeMillis() - st);
        }
    }

}
