package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.*;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.cache.keys.MstSubType;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.dao.mysql.ThresholdDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ThresholdDataDao;
import com.appnomic.appsone.controlcenter.dao.redis.ComponentRepo;
import com.appnomic.appsone.controlcenter.dao.redis.MasterDataRepo;
import com.appnomic.appsone.controlcenter.dao.redis.TransactionRepo;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import com.appnomic.appsone.controlcenter.util.DateTimeUtil;
import com.appnomic.appsone.controlcenter.util.StringUtils;
import com.appnomic.appsone.controlcenter.util.ValidationUtils;
import com.heal.configuration.pojos.ComponentKpiEntity;
import org.skife.jdbi.v2.Handle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Kumar : 6/3/19
 */
public class ThresholdService {
    private static final Logger logger = LoggerFactory.getLogger(ThresholdService.class);

    public void addCompInstKpiThreshold(KPI kpi, int compInstId, String userDetails, int accountId, Handle handle) throws Exception {
        List<ViolationConfig> kpiViolationConfig = kpi.getViolationConfig();
        if (kpiViolationConfig == null || kpiViolationConfig.isEmpty()) {
            logger.info("No any kpi violation configuration is found for kpi - {}" , kpi.getName());
            return;
        }
        List<CompInstKpiThresholdDetailsBean> compInstKpiThresholdDetailsBeanList = new ArrayList<>();
        ThresholdDataDao thresholdDataDao = handle.attach(ThresholdDataDao.class);
        for (ViolationConfig violationConfig : kpiViolationConfig) {
            compInstKpiThresholdDetailsBeanList.add(prepareSingleCompInstKpiThreshold(violationConfig, kpi.getId(), compInstId, userDetails, accountId));
        }
        if (!compInstKpiThresholdDetailsBeanList.isEmpty()) {
            ThresholdDataService.addCompInstanceKpiThreshold(compInstKpiThresholdDetailsBeanList, thresholdDataDao);
            logger.info("Threshold value is successfully inserted into 'comp_instance_kpi_threshold_details' table for kpi- {} for component instance id- {}" ,kpi.getName(),compInstId);
        }
    }

    public CompInstKpiThresholdDetailsBean prepareSingleCompInstKpiThreshold(ViolationConfig violationConfig, int kpiId, int compInstId, String userDetails, int accountId) throws Exception {
        ViewTypes operation = MasterCache.getMstTypeForSubTypeName("Operations", violationConfig.getOperation());
        if (operation == null) {
            String log = "Operation-" + violationConfig.getOperation() + " is not found in mst sub type table";
            logger.warn(log);
            throw new ControlCenterException(log);
        }
        CompInstKpiThresholdDetailsBean compInstKpiThresholdDetailsBean = createCompInstKpiThresholdDetailsBean(kpiId, compInstId, operation.getSubTypeId(), violationConfig, userDetails, accountId);
        int profileId = CommonUtils.getCoverageWindowProfileId(violationConfig.getProfileName());
        if (profileId == -1) {
            String log = "Operation-" + violationConfig.getOperation() + " is not found in mst sub type table";
            logger.warn(log);
            throw new ControlCenterException(log);
        }
        return compInstKpiThresholdDetailsBean;
    }

    public void addCompInstGroupKpiThreshold(int kpiId, int kpiGroupId, Attributes attribute, int compInstId, String userDetails, int accountId, Handle handle) throws Exception {
        List<ViolationConfig> kpiViolationConfig = attribute.getViolationConfig();
        if (kpiViolationConfig == null || kpiViolationConfig.isEmpty()) {
            logger.info("No any group kpi violation configuration is found for attribute value - {}" , attribute.getValue());
            return;
        }
        ThresholdDataDao thresholdDataDao = handle.attach(ThresholdDataDao.class);
        List<CompInstKpiThresholdDetailsBean> compInstKpiThresholdDetailsBeanList = new ArrayList<>();
        for (ViolationConfig violationConfig : kpiViolationConfig) {
            MstSubType mstSubType = new MstSubType();
            mstSubType.setAccountId(accountId);
            mstSubType.setMstSubTypeName(violationConfig.getOperation());
            ViewTypes operation = MasterCache.getMstTypeForSubTypeName("Operations", violationConfig.getOperation());
            if (operation == null) {
                logger.warn("Operation- {} is not found in mst sub type table",violationConfig.getOperation());
                continue;
            }
            CompInstKpiThresholdDetailsBean compInstKpiThresholdDetailsBean = createCompInstKpiThresholdDetailsBean(kpiId, compInstId, operation.getSubTypeId(), violationConfig, userDetails, accountId);
            int profileId = CommonUtils.getCoverageWindowProfileId(violationConfig.getProfileName());
            if (profileId == -1) continue;
            compInstKpiThresholdDetailsBean.setKpiGroupId(kpiGroupId);
            compInstKpiThresholdDetailsBean.setKpiAttribute(attribute.getValue());
            compInstKpiThresholdDetailsBeanList.add(compInstKpiThresholdDetailsBean);
        }
        if (!compInstKpiThresholdDetailsBeanList.isEmpty()) {
            ThresholdDataService.addCompInstanceKpiThreshold(compInstKpiThresholdDetailsBeanList, thresholdDataDao);
            logger.info("Threshold value is successfully inserted into 'comp_instance_kpi_threshold_details' table for group kpi id-{} " +
                    "attribute value-{} component instance id-{}" ,kpiGroupId,attribute.getValue(), compInstId);
        }
    }

    private CompInstKpiThresholdDetailsBean createCompInstKpiThresholdDetailsBean(int kpiId, int compInstId, int operationId, ViolationConfig violationConfig, String userDetails, int accountId) throws ParseException {
        CompInstKpiThresholdDetailsBean compInstKpiThresholdDetailsBean = new CompInstKpiThresholdDetailsBean();
        compInstKpiThresholdDetailsBean.setCompInstanceId(compInstId);
        compInstKpiThresholdDetailsBean.setKpiId(kpiId);
        compInstKpiThresholdDetailsBean.setOperationId(operationId);
        compInstKpiThresholdDetailsBean.setMinThreshold(Double.parseDouble(violationConfig.getMinThreshold()));
        compInstKpiThresholdDetailsBean.setMaxThreshold(Double.parseDouble(violationConfig.getMaxThreshold()));
        java.sql.Timestamp date = new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        compInstKpiThresholdDetailsBean.setCreatedTime(date);
        compInstKpiThresholdDetailsBean.setUpdatedTime(date);
        compInstKpiThresholdDetailsBean.setUserDetailsId(userDetails);
        String startTime = violationConfig.getStartTime();
        if (!StringUtils.isEmpty(startTime))
            compInstKpiThresholdDetailsBean.setStartTime(DateTimeUtil.getTimestampInGMT(startTime));
        else compInstKpiThresholdDetailsBean.setStartTime(date);
        String endTime = violationConfig.getEndTime();
        if (!StringUtils.isEmpty(endTime))
            compInstKpiThresholdDetailsBean.setEndTime(DateTimeUtil.getTimestampInGMT(endTime));
        compInstKpiThresholdDetailsBean.setAccountId(accountId);
        return compInstKpiThresholdDetailsBean;
    }

    public void addApplicationThreshold(int applicationId, String userDetails, int accountId, Application application, Handle handle) throws Exception {
        logger.info("Started adding application threshold for applicationId-{} accountId-{} user details-{}" , applicationId,accountId,userDetails);
        List<TransactionViolationConfig> violationConfigs = application.getTxnViolationConfigs();
        if (violationConfigs == null || violationConfigs.isEmpty()) {
            logger.info("No any violation configuration is found for application -{}" , application.getName());
            return;
        }

        ViewTypes operation, txnKpiType, responseType;
        ThresholdDataDao thresholdDataDao = handle.attach(ThresholdDataDao.class);
        List<ApplicationThresholdDetailsBean> applicationThresholdDetailsBeanList = new ArrayList<>();
        for (TransactionViolationConfig violationConfig : violationConfigs) {
            logger.debug("ViolationConfig: {}" , violationConfigs);
            operation = MasterCache.getMstTypeForSubTypeName(Constants.OPERATIONS_TYPE_NAME, violationConfig.getOperation());
            if (operation == null) {
                logger.error(Constants.OPERATIONS_TYPE_NAME + "- {} is not found in mst sub type table for account:{}, applicationId:{}" , violationConfig.getOperation(),accountId,applicationId);
                continue;
            }

            ApplicationThresholdDetailsBean applicationThresholdDetailsBean = new ApplicationThresholdDetailsBean();
            java.sql.Timestamp date = new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            applicationThresholdDetailsBean.setCreatedTime(date);
            applicationThresholdDetailsBean.setUpdatedTime(date);
            applicationThresholdDetailsBean.setUserDetailsId(userDetails);
            applicationThresholdDetailsBean.setApplicationId(applicationId);
            applicationThresholdDetailsBean.setOperationId(operation.getSubTypeId());

            txnKpiType = MasterCache.getMstTypeForSubTypeName(Constants.TRANSACTION_KPI_TYPES_NAME, violationConfig.getTransactionKpiType());
            if (txnKpiType == null) {
                logger.error(Constants.TRANSACTION_KPI_TYPES_NAME + "-{} is not found in mst sub type table for account:{} , applicationId:{}" , violationConfig.getTransactionKpiType(),accountId,applicationId);
                continue;
            }
            applicationThresholdDetailsBean.setTransactionKpiTypeId(txnKpiType.getSubTypeId());
            responseType = MasterCache.getMstTypeForSubTypeName(Constants.TRANSACTION_RESPONSE_TYPE, violationConfig.getResponseTimeType());
            if (responseType == null) {
                logger.error(Constants.TRANSACTION_RESPONSE_TYPE + "- {} is not found in mst sub type table for account:{}, applicationId:{}" , violationConfig.getResponseTimeType(),accountId,applicationId);
                continue;
            }
            applicationThresholdDetailsBean.setResponseTimeTypeId(responseType.getSubTypeId());
            applicationThresholdDetailsBean.setMinThreshold(Float.parseFloat(violationConfig.getMinThreshold()));
            applicationThresholdDetailsBean.setMaxThreshold(Float.parseFloat(violationConfig.getMaxThreshold()));
            String startTime = violationConfig.getStartTime();
            java.sql.Timestamp startTimestamp = null;
            if (startTime != null) startTimestamp = DateTimeUtil.getTimestampInGMT(startTime);
            String endTime = violationConfig.getEndTime();
            java.sql.Timestamp endTimestamp = null;
            if (endTime != null) endTimestamp = DateTimeUtil.getTimestampInGMT(endTime);
            if ((startTime != null && endTime != null) && endTimestamp.before(startTimestamp)) {
                logger.warn("End time -{} should not be less that start time-{}",endTime, startTime);
                return;
            }
            if (startTimestamp != null) applicationThresholdDetailsBean.setStartTime(startTimestamp);
            else applicationThresholdDetailsBean.setStartTime(date);
            if (endTimestamp != null) applicationThresholdDetailsBean.setEndTime(endTimestamp);
            int profileId = CommonUtils.getCoverageWindowProfileId(violationConfig.getProfileName());
            if (profileId == -1) {
                logger.error("Profile name {} does not exists for account: {} applicationId:{}" , violationConfig.getProfileName(),accountId,applicationId);
                throw new ControlCenterException("Profile name " + violationConfig.getProfileName() + " does not exists for account:" + accountId + ", applicationId:" + applicationId);
            }
            applicationThresholdDetailsBean.setCoverageWindowProfileId(profileId);
            applicationThresholdDetailsBean.setAccountId(accountId);
            applicationThresholdDetailsBeanList.add(applicationThresholdDetailsBean);
        }


        if (!applicationThresholdDetailsBeanList.isEmpty()) {
            ThresholdDataService.addApplicationThreshold(applicationThresholdDetailsBeanList, thresholdDataDao);
            logger.info("Threshold value is successfully inserted into 'application_threshold_details' table for application-{}" , application.getName());
        }
    }


    public Map<String, Double> addTransactionResponseThreshold(List<TransactionResponseThresholdPojo> txnResponseThresholds, int transactionId, String userDetails, int accountId, Handle handle) throws ParseException {
        logger.info("Started adding application threshold for transactionId- {} accountId- {}, user details-{}" , transactionId,accountId,userDetails);
        if (txnResponseThresholds == null || txnResponseThresholds.isEmpty()) {
            logger.info("No any threshold is configured for a given transaction");
            return Collections.emptyMap();
        }
        Map<String, Double> responseThresholdBean = new HashMap<>();
        List<TransactionResponseThresholdViolationBean> transactionResponseThresholdViolationBeanList = new ArrayList<>();
        ThresholdDataDao thresholdDataDao = handle.attach(ThresholdDataDao.class);
        for (TransactionResponseThresholdPojo txnResponseConfig : txnResponseThresholds) {
            ViewTypes subType = ValidationUtils.validateTxnResponseType(txnResponseConfig.getResponseTimeType());
            if (subType == null) return Collections.emptyMap();
            TransactionResponseThresholdViolationBean transactionResponseThresholdBean = new TransactionResponseThresholdViolationBean();
            java.sql.Timestamp date = new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            String startTime = txnResponseConfig.getStartTime();
            java.sql.Timestamp startTimestamp = null;
            if (startTime != null) startTimestamp = DateTimeUtil.getTimestampInGMT(startTime);
            String endTime = txnResponseConfig.getEndTime();
            java.sql.Timestamp endTimestamp = null;
            if (endTime != null) endTimestamp = DateTimeUtil.getTimestampInGMT(endTime);
            if ((startTime != null && endTime != null) && endTimestamp.before(startTimestamp)) {
                logger.error("End time -{} should not be less that start time-{}" , endTime,startTime);
                return Collections.emptyMap();
            }
            if (startTimestamp != null) transactionResponseThresholdBean.setStartTime(startTimestamp);
            else transactionResponseThresholdBean.setStartTime(date);
            if (endTimestamp != null) transactionResponseThresholdBean.setEndTime(endTimestamp);
            transactionResponseThresholdBean.setUserDetailsId(userDetails);
            transactionResponseThresholdBean.setCreatedTime(date);
            transactionResponseThresholdBean.setUpdatedTime(date);
            transactionResponseThresholdBean.setResponseTimeTypeId(subType.getSubTypeId());
            transactionResponseThresholdBean.setTransactionId(transactionId);
            transactionResponseThresholdBean.setAccountId(accountId);
            int profileId = CommonUtils.getCoverageWindowProfileId(txnResponseConfig.getProfileName());
            if (profileId == -1) return Collections.emptyMap();
            transactionResponseThresholdBean.setCoverageWindowProfileId(profileId);
            transactionResponseThresholdBean.setSlowThresholdValue(txnResponseConfig.getSlowThresholdValue());
            transactionResponseThresholdViolationBeanList.add(transactionResponseThresholdBean);

            responseThresholdBean.put(txnResponseConfig.getResponseTimeType(), (double) transactionResponseThresholdBean.getSlowThresholdValue());
        }
        if (!transactionResponseThresholdViolationBeanList.isEmpty()) {
            ThresholdDataService.addTransactionResponseThreshold(transactionResponseThresholdViolationBeanList, thresholdDataDao);
            logger.info("Threshold value is successfully inserted into 'transaction_response_threshold' table ");
        }

        return responseThresholdBean;
    }

    public void addTransactionThresholdDetails(List<TransactionViolationConfig> txnThresholds, int transactionId, String txnIdentifier , String userDetails, int accountId, String accountIdentifier, Handle handle) throws ParseException {
        logger.info("Started adding application threshold for transactionId- {} accountId- {} user details-{}", transactionId, accountId, userDetails);
        MasterDataRepo masterDataRepo = new MasterDataRepo();
        ComponentRepo componentRepo = new ComponentRepo();
        ThresholdDataDao thresholdDataDao = handle.attach(ThresholdDataDao.class);

        java.sql.Timestamp date = Timestamp.valueOf(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()));
        SimpleDateFormat formatter = new SimpleDateFormat(Constants.DATE_TIME);
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));

        List<ComponentKpiEntity> transactionKpiDetails = componentRepo.getComponentKpiDetails(accountIdentifier, Constants.TRANSACTION_IDENTIFIER_DEFAULT);
        List<Integer> transactionKpiIds = transactionKpiDetails.parallelStream().map(ComponentKpiEntity::getId).collect(Collectors.toList());
        List<com.heal.configuration.pojos.ViewTypes> types = masterDataRepo.getTypes();
        Map<String, Integer> viewTypesMap = types.parallelStream()
                .filter(f ->
                        (f.getTypeName().equalsIgnoreCase(Constants.TRANSACTION_SIGNAL_SEVERITY_TYPE) && f.getSubTypeName().equalsIgnoreCase(Constants.TRANSACTION_SIGNAL_SEVERITY_SUB_TYPE))
                                || (f.getTypeName().equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_TYPE) && f.getSubTypeName().equalsIgnoreCase(Constants.TRANSACTION_RESPONSE_SUB_TYPE)))
                .collect(Collectors.toMap(com.heal.configuration.pojos.ViewTypes::getSubTypeName, com.heal.configuration.pojos.ViewTypes::getSubTypeId));

        addDefaultTransactionThresholdsInDB(transactionId, userDetails, accountId, thresholdDataDao, date, transactionKpiIds,viewTypesMap);

        addDefaultTransactionThresholdsInRedis(txnIdentifier, accountIdentifier, date, formatter, transactionKpiIds, viewTypesMap);

        if (txnThresholds == null || txnThresholds.isEmpty()) {
            logger.info("No any threshold is configured for a given transaction");
            return;
        }
        List<TransactionThresholdDetailsBean> transactionThresholdDetailsBeanList = new ArrayList<>();
        for (TransactionViolationConfig violationConfig : txnThresholds) {

            ViewTypes txnKpiType = MasterCache.getMstTypeForSubTypeName(Constants.TRANSACTION_KPI_TYPES_NAME, violationConfig.getTransactionKpiType());
            if (txnKpiType == null) {
                logger.error(Constants.TRANSACTION_KPI_TYPES_NAME + "-" + violationConfig.getTransactionKpiType() + " is not found in mst sub type table for account:" + accountId + " ");
                continue;
            }
            ViewTypes respTimeType = MasterCache.getMstTypeForSubTypeName(Constants.TRANSACTION_RESPONSE_TYPE, violationConfig.getResponseTimeType());
            if (respTimeType == null) {
                logger.error(Constants.TRANSACTION_RESPONSE_TYPE + "-" + violationConfig.getResponseTimeType() + " is not found in mst sub type table for account:" + accountId + " ");
                return;
            }
            ViewTypes operationType = MasterCache.getMstTypeForSubTypeName(Constants.OPERATIONS_TYPE_NAME, violationConfig.getOperation());
            if (operationType == null) {
                logger.error(Constants.OPERATIONS_TYPE_NAME + "-" + violationConfig.getOperation() + " is not found in mst sub type table for account:" + accountId + " ");
                return;
            }

            TransactionThresholdDetailsBean transactionThresholdDetailsBean = new TransactionThresholdDetailsBean();
            transactionThresholdDetailsBean.setCreatedTime(date);
            transactionThresholdDetailsBean.setUpdatedTime(date);
            transactionThresholdDetailsBean.setUserDetailsId(userDetails);
            transactionThresholdDetailsBean.setResponseTimeTypeId(respTimeType.getSubTypeId());
            transactionThresholdDetailsBean.setTransactionId(transactionId);
            String startTime = violationConfig.getStartTime();
            java.sql.Timestamp startTimestamp = null;
            if (startTime != null) startTimestamp = DateTimeUtil.getTimestampInGMT(startTime);
            String endTime = violationConfig.getEndTime();
            java.sql.Timestamp endTimestamp = null;
            if (endTime != null) endTimestamp = DateTimeUtil.getTimestampInGMT(endTime);
            if ((startTime != null && endTime != null) && endTimestamp.before(startTimestamp)) {
                logger.error("End time -{} should not be less that start time-{}" , endTime,startTime);
                return;
            }
            if (startTimestamp != null) transactionThresholdDetailsBean.setStartTime(startTimestamp);
            else transactionThresholdDetailsBean.setStartTime(date);
            if (endTimestamp != null) transactionThresholdDetailsBean.setEndTime(endTimestamp);
            transactionThresholdDetailsBean.setOperationId(operationType.getSubTypeId());
//            transactionThresholdDetailsBean.setTransactionKpiTypeId(txnKpiType.getSubTypeId());
            transactionThresholdDetailsBean.setMinThreshold(Float.parseFloat(violationConfig.getMinThreshold()));
            transactionThresholdDetailsBean.setMaxThreshold(Float.parseFloat(violationConfig.getMaxThreshold()));
//            int profileId = CommonUtils.getCoverageWindowProfileId(violationConfig.getProfileName());
//            if (profileId == -1) return;
//            transactionThresholdDetailsBean.setCoverageWindowProfileId(profileId);
            transactionThresholdDetailsBean.setAccountId(accountId);
            transactionThresholdDetailsBeanList.add(transactionThresholdDetailsBean);
        }

        if (!transactionThresholdDetailsBeanList.isEmpty()) {
            ThresholdDataService.addTransactionThresholdDetails(transactionThresholdDetailsBeanList, thresholdDataDao);
            logger.info("Threshold value is successfully inserted into 'transaction_threshold_details' table ");
        }

    }

    private void addDefaultTransactionThresholdsInRedis(String txnIdentifier, String accountIdentifier, Timestamp date, SimpleDateFormat formatter, List<Integer> transactionKpiIds, Map<String, Integer> viewTypesMap) {
        TransactionRepo transactionRepo = new TransactionRepo();
        List<com.heal.configuration.pojos.KpiViolationConfig> kpiViolationConfigList = new ArrayList<>();
        List<com.heal.configuration.pojos.TxnKPIViolationConfig> txnKPIViolationConfigList = new ArrayList<>();

        for (Integer transactionKpiId : transactionKpiIds) {
            com.heal.configuration.pojos.KpiViolationConfig kpiViolationConfigObject = com.heal.configuration.pojos.KpiViolationConfig.builder()
                    .startTime(formatter.format(date))
                    .severity(viewTypesMap.get(Constants.TRANSACTION_SIGNAL_SEVERITY_SUB_TYPE))
                    .definedBy(Constants.THRESHOLD_DEFINED_BY_SYSTEM)
                    .maxThreshold(Constants.DEFAULT_MAX_VALUE)
                    .minThreshold(Constants.DEFAULT_MIN_VALUE)
                    .kpiId(transactionKpiId)
                    .build();
            kpiViolationConfigList.add(kpiViolationConfigObject);
        }

        txnKPIViolationConfigList.add(com.heal.configuration.pojos.TxnKPIViolationConfig.builder()
                                        .responseTimeType(Constants.TRANSACTION_RESPONSE_SUB_TYPE)
                                        .kpiViolationConfig(kpiViolationConfigList)
                                        .build());

        transactionRepo.postTransactionViolationConfigDetails(accountIdentifier, txnIdentifier,txnKPIViolationConfigList);
    }

    private void addDefaultTransactionThresholdsInDB(int transactionId, String userDetails, int accountId, ThresholdDataDao thresholdDataDao, Timestamp date, List<Integer> transactionKpiIds, Map<String, Integer> viewTypesMap) {
        List<TransactionThresholdDetailsBean> defaultTransactionThresholdDetailsList = new ArrayList<>();
        transactionKpiIds.forEach(transactionKpiId -> {
                    TransactionThresholdDetailsBean defaultTransactionThresholdDetailsObject = TransactionThresholdDetailsBean.builder()
                            .createdTime(date)
                            .updatedTime(date)
                            .userDetailsId(userDetails)
                            .transactionId(transactionId)
                            .operationId(Constants.DEFAULT_OPERATION_ID)
                            .minThreshold(Constants.DEFAULT_MIN_THRESHOLD_VALUE)
                            .maxThreshold(Constants.DEFAULT_MAX_THRESHOLD_VALUE)
                            .responseTimeTypeId(viewTypesMap.get(Constants.TRANSACTION_RESPONSE_SUB_TYPE))
                            .startTime(date)
                            .accountId(accountId)
                            .severityId(viewTypesMap.get(Constants.TRANSACTION_SIGNAL_SEVERITY_SUB_TYPE))
                            .status(Constants.DEFAULT_STATUS)
                            .persistence(Constants.DEFAULT_PERSISTENCE_VALUE)
                            .suppression(Constants.DEFAULT_SUPPRESSION_VALUE)
                            .definedBy(Constants.THRESHOLD_DEFINED_BY_SYSTEM)
                            .excludeMaintenance(Constants.DEFAULT_EXCLUDE_MAINTENANCE_VALUE)
                            .kpiId(transactionKpiId)
                            .build();
                    defaultTransactionThresholdDetailsList.add(defaultTransactionThresholdDetailsObject);
                }
        );

        ThresholdDataService.addTransactionStaticThresholds(defaultTransactionThresholdDetailsList, accountId,thresholdDataDao);
        logger.info("Default system defined threshold values are successfully inserted into 'transaction_threshold_details' table ");
    }


    public void addComponentThresholdDetails(KPI kpi, int compId, String userDetails, int accountId, int mstCommonVersionId, Handle handle) throws ParseException {
        List<ViolationConfig> kpiViolationConfig = kpi.getViolationConfig();
        if (kpiViolationConfig == null || kpiViolationConfig.isEmpty()) {
            logger.info("No any kpi violation configuration is found for kpi -{}" , kpi.getName());
            return;
        }
        List<ComponentKpiThresholdDetailsBean> componentKpiThresholdDetailsBeans = new ArrayList<>();
        ThresholdDataDao thresholdDataDao = handle.attach(ThresholdDataDao.class);
        for (ViolationConfig violationConfig : kpiViolationConfig) {
            ViewTypes operation = MasterCache.getMstTypeForSubTypeName("Operations", violationConfig.getOperation());
            if (operation == null) {
                logger.warn("Operation-{} is not found in mst sub type table",violationConfig.getOperation());
                continue;
            }

            int profileId = CommonUtils.getCoverageWindowProfileId(violationConfig.getProfileName());
            if (profileId == -1) continue;


            ComponentKpiThresholdDetailsBean componentKpiThresholdDetailsBean = new ComponentKpiThresholdDetailsBean();
            java.sql.Timestamp date = new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
            componentKpiThresholdDetailsBean.setCreatedTime(date);
            componentKpiThresholdDetailsBean.setUpdatedTime(date);
            componentKpiThresholdDetailsBean.setUserDetailsId(userDetails);
            String startTime = violationConfig.getStartTime();
            java.sql.Timestamp startTimestamp = null;
            if (startTime != null) startTimestamp = DateTimeUtil.getTimestampInGMT(startTime);
            String endTime = violationConfig.getEndTime();
            java.sql.Timestamp endTimestamp = null;
            if (endTime != null) endTimestamp = DateTimeUtil.getTimestampInGMT(endTime);
            if ((startTime != null && endTime != null) && endTimestamp.before(startTimestamp)) {
                logger.error("End time -{} should not be less that start time-{}" , endTime,startTime);
                return;
            }
            if (startTimestamp != null) componentKpiThresholdDetailsBean.setStartTime(startTimestamp);
            else componentKpiThresholdDetailsBean.setStartTime(date);
            if (endTimestamp != null) componentKpiThresholdDetailsBean.setEndTime(endTimestamp);
            componentKpiThresholdDetailsBean.setMinThreshold(Float.parseFloat(violationConfig.getMinThreshold()));
            componentKpiThresholdDetailsBean.setMaxThreshold(Float.parseFloat(violationConfig.getMaxThreshold()));
            componentKpiThresholdDetailsBean.setCoverageWindowProfileId(profileId);
            componentKpiThresholdDetailsBean.setComponentId(compId);
            componentKpiThresholdDetailsBean.setMstCommonVersionId(mstCommonVersionId);
            componentKpiThresholdDetailsBean.setKpiId(kpi.getId());
            componentKpiThresholdDetailsBean.setAccountId(accountId);
            componentKpiThresholdDetailsBean.setOperationId(operation.getSubTypeId());
            componentKpiThresholdDetailsBean.setUserDetailsId(userDetails);
            componentKpiThresholdDetailsBeans.add(componentKpiThresholdDetailsBean);
        }

        if (!componentKpiThresholdDetailsBeans.isEmpty()) {
            ThresholdDataService.addComponentThresholdDetails(componentKpiThresholdDetailsBeans, thresholdDataDao);
            logger.info("Threshold value is successfully inserted into 'component_kpi_threshold_details' table for kpi-{} for component instance id-{}" , kpi.getName(),compId);
        }

    }


    public void addComponentGroupThreshold(GroupKPIs kpi, int compId, String userDetails, int accountId, int mstCommonVersionId, Handle handle) throws ParseException {
        List<Attributes> attributesList = kpi.getAttributes();
        List<ComponentKpiThresholdDetailsBean> componentKpiThresholdDetailsBeansList = new ArrayList<>();
        ThresholdDataDao thresholdDataDao = handle.attach(ThresholdDataDao.class);
        if (attributesList == null || attributesList.isEmpty()) {
            logger.info("No any attributes configuration is found for kpi -{}" , kpi.getKpiName());
            return;
        }

        for (Attributes attribute : attributesList) {
            List<ViolationConfig> kpiViolationConfig = attribute.getViolationConfig();
            if (kpiViolationConfig == null || kpiViolationConfig.isEmpty()) {
                logger.info("No any Kpi Group violation configuration is found for attribute -{}", attribute.getName());
                return;
            }
            for (ViolationConfig violationConfig : kpiViolationConfig) {
                ViewTypes operation = MasterCache.getMstTypeForSubTypeName("Operations", violationConfig.getOperation());
                if (operation == null) {
                    logger.warn("Operation-{} is not found in mst sub type table",violationConfig.getOperation() );
                    continue;
                }
                int profileId = CommonUtils.getCoverageWindowProfileId(violationConfig.getProfileName());
                if (profileId == -1) continue;
                ComponentKpiThresholdDetailsBean componentKpiThresholdDetailsBean = new ComponentKpiThresholdDetailsBean();
                java.sql.Timestamp date = new java.sql.Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
                componentKpiThresholdDetailsBean.setCreatedTime(date);
                componentKpiThresholdDetailsBean.setUpdatedTime(date);
                componentKpiThresholdDetailsBean.setUserDetailsId(userDetails);
                String startTime = violationConfig.getStartTime();
                java.sql.Timestamp startTimestamp = null;
                if (startTime != null) startTimestamp = DateTimeUtil.getTimestampInGMT(startTime);
                String endTime = violationConfig.getEndTime();
                java.sql.Timestamp endTimestamp = null;
                if (endTime != null) endTimestamp = DateTimeUtil.getTimestampInGMT(endTime);
                if ((startTime != null && endTime != null) && endTimestamp.before(startTimestamp)) {
                    logger.error("End time -{} should not be less that start time-{}" , endTime,startTime);
                    return;
                }
                if (startTimestamp != null) componentKpiThresholdDetailsBean.setStartTime(startTimestamp);
                else componentKpiThresholdDetailsBean.setStartTime(date);
                if (endTimestamp != null) componentKpiThresholdDetailsBean.setEndTime(endTimestamp);
                componentKpiThresholdDetailsBean.setMinThreshold(Float.parseFloat(violationConfig.getMinThreshold()));
                componentKpiThresholdDetailsBean.setMaxThreshold(Float.parseFloat(violationConfig.getMaxThreshold()));
                componentKpiThresholdDetailsBean.setCoverageWindowProfileId(profileId);
                componentKpiThresholdDetailsBean.setComponentId(compId);
                componentKpiThresholdDetailsBean.setMstCommonVersionId(mstCommonVersionId);
                componentKpiThresholdDetailsBean.setKpiId(kpi.getKpiId());
                if (kpi.getDiscovery() == 0) componentKpiThresholdDetailsBean.setKpiGroupValue(attribute.getValue());
                componentKpiThresholdDetailsBean.setKpiGroupId(kpi.getGroupKpiId());
                componentKpiThresholdDetailsBean.setAccountId(accountId);
                componentKpiThresholdDetailsBean.setOperationId(operation.getSubTypeId());
                componentKpiThresholdDetailsBean.setUserDetailsId(userDetails);
                componentKpiThresholdDetailsBeansList.add(componentKpiThresholdDetailsBean);
            }
            if (kpi.getDiscovery() == 1) break;
        }
        if (!componentKpiThresholdDetailsBeansList.isEmpty()) {
            ThresholdDataService.addComponentThresholdDetails(componentKpiThresholdDetailsBeansList, thresholdDataDao);
            logger.info("Threshold value is successfully inserted into 'component_kpi_threshold_details' table for kpi-{} for component instance id-{}" , kpi.getKpiName() ,compId);
        }
    }
}
