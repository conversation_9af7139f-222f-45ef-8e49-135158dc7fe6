package com.appnomic.appsone.controlcenter.service;

import com.appnomic.appsone.controlcenter.beans.UtilityBean;
import com.appnomic.appsone.controlcenter.businesslogic.UpdateCompInstanceNameBL;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.pojo.ComponentInstancePojo;
import com.appnomic.appsone.controlcenter.pojo.RequestObject;
import com.appnomic.appsone.controlcenter.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import spark.Request;
import spark.Response;

@Slf4j
public class UpdateCompInstanceNameService {

    public GenericResponse<String> updateInstanceName(Request request, Response response) {

        GenericResponse<String> responseObject = new GenericResponse<>();
        try {
            RequestObject requestObject = new RequestObject(request);

            UpdateCompInstanceNameBL businessLogic = new UpdateCompInstanceNameBL();

            UtilityBean<ComponentInstancePojo> integerUtilityBean = businessLogic.clientValidation(requestObject);
            UtilityBean<ComponentInstancePojo> accountId = businessLogic.serverValidation(integerUtilityBean);
            String message = businessLogic.process(accountId);

            responseObject.setResponseStatus(StatusResponse.SUCCESS.name());
            responseObject.setMessage(message);
            response.status(Constants.SUCCESS_STATUS_CODE);

            return responseObject;
        } catch (ClientException | ServerException | DataProcessingException e) {
            log.error(UIMessages.INVALID_REQUEST_EXCEPTION_MESSAGE, e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, e.getMessage(), null, true);
        } catch (Exception e) {
            log.error("Error while updating instance name", e);
            return CommonUtils.getGenericResponse(response, StatusResponse.FAILURE.name(),
                    Constants.INTERNAL_SERVER_ERROR_STATUS_CODE,
                    "Error while updating instance name", null, true);
        }
    }
}
