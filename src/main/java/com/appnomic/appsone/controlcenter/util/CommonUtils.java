package com.appnomic.appsone.controlcenter.util;

import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.controlcenter.beans.TagDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ViewCoverageWinProfDetailsBean;
import com.appnomic.appsone.controlcenter.beans.ViewTypes;
import com.appnomic.appsone.controlcenter.cache.MasterCache;
import com.appnomic.appsone.controlcenter.common.Constants;
import com.appnomic.appsone.controlcenter.common.GenericResponse;
import com.appnomic.appsone.controlcenter.common.StatusResponse;
import com.appnomic.appsone.controlcenter.common.UIMessages;
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.NotificationDataService;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMSDetailsBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMSParameterBean;
import com.appnomic.appsone.controlcenter.dao.mysql.entity.SMTPDetailsBean;
import com.appnomic.appsone.controlcenter.exceptions.ClientException;
import com.appnomic.appsone.controlcenter.exceptions.ControlCenterException;
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException;
import com.appnomic.appsone.controlcenter.exceptions.ServerException;
import com.appnomic.appsone.controlcenter.health.ApplicationInfo;
import com.appnomic.appsone.controlcenter.pojo.*;
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService;
import com.appnomic.appsone.model.JWTData;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.reflect.Type;
import java.security.Security;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class CommonUtils {
    private static final Logger logger = LoggerFactory.getLogger(CommonUtils.class);

    private static final Gson gson = new GsonBuilder().create();

    private CommonUtils() {
        //Dummy constructor
    }

    public static <T> T jsonToObject(String jsonStr, Type clazz) {
        return gson.fromJson(jsonStr, clazz);
    }

    public static List<Controller> getControllersByType(String serviceType, int accountId) {
        List<Controller> filtratedControllerList = new ArrayList<>();
        try {
            //get the service mst sub type
            ViewTypes subTypeBean = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, serviceType);

            //get the all app for accountId
            List<Controller> controllerList = MasterCache.getControllerList(accountId);

            //filter with controller_type_id
            filtratedControllerList = controllerList.stream()
                    .filter(t -> t.getControllerTypeId() == subTypeBean.getSubTypeId())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error occurred while fetching controller details for service name: {} account id: {}", serviceType, accountId, e);
        }
        return filtratedControllerList;
    }

    /*
     * This utility method is used for adding SDM dependency connections which requires latest updated value from
     * database rather than the cached values
     *
     * @param serviceType The parameter identifies to type of controller
     * @param accountId The parameter identifies to Id of any account.
     * @return List of controller, it can be application or services.
     */
    public static List<Controller> getControllersByTypeBypassCache(String serviceType, int accountId) {

        List<Controller> filtratedControllerList = new ArrayList<>();
        try {
            //get the service mst sub type
            ViewTypes subTypeBean = MasterCache.getMstTypeForSubTypeName(Constants.CONTROLLER_TYPE_NAME_DEFAULT, serviceType);

            //get the all app for accountId by passing cache
            List<Controller> controllerList = MasterDataService.getControllerList(accountId);

            //filter with controller_type_id
            filtratedControllerList = controllerList.stream()
                    .filter(t -> t.getControllerTypeId() == subTypeBean.getSubTypeId())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("Error occurred while fetching controller details for service name: {} account id: {}", serviceType, accountId, e);
        }
        return filtratedControllerList;
    }

    public static int getCoverageWindowProfileId(String profileName) {
        List<ViewCoverageWinProfDetailsBean> viewCoverageWinProfDetailsBeans = MasterCache.getCoverageWinProfDetailsList(profileName);
        if (viewCoverageWinProfDetailsBeans == null || viewCoverageWinProfDetailsBeans.isEmpty()) {
            logger.warn("Profile name -{} is not found in 'view_coverage_window_profile_details' table", profileName);
            return -1;
        }
        return viewCoverageWinProfDetailsBeans.get(0).getProfileId();
    }


    public static String getDecryptedData(String encryptedData) {
        //TODO BouncyCastle encryption algorithm has to be used in further releases.
        return new String(Base64.getDecoder().decode(encryptedData));
    }

    public static <T> GenericResponse<T> getGenericResponse(Response response, String status, int statusCode, String message, Throwable throwable, boolean isError) {
        GenericResponse<T> responseObject = new GenericResponse<>();
        responseObject.setResponseStatus(status);
        responseObject.setMessage(message);
        response.status(statusCode);
        if (!isError) {
            logger.info(message);
            return responseObject;
        }

        if (throwable == null) {
            logger.error(message);
        } else {
            logger.error(message, throwable);
        }
        return responseObject;
    }

    public static int getAccountId(final Request request) throws ControlCenterException {
        String accountIdString = request.params(":identifier");
        AccountBean account = ValidationUtils.validAndGetAccount(accountIdString);
        if (account == null) {
            String errorMessage = "Invalid account id provided";
            throw new ControlCenterException(errorMessage);
        }
        int accountId = account.getId();
        if (accountId == -1) {
            String errorMessage = "Invalid account id provided";
            throw new ControlCenterException(errorMessage);
        }
        return accountId;
    }

    public static String getUserId(final Request request) throws ControlCenterException {
        String authKey = request.headers("Authorization");
        JWTData jwtData = KeyCloakAuthService.extractUserDetails(authKey);
        return jwtData.getSub();
    }

    public static <T> void populateErrorResponse(GenericResponse<T> responseObject, Response response, final String errorMessage, int statusCode) {
        responseObject.setResponseStatus(StatusResponse.FAILURE.name());
        responseObject.setMessage(errorMessage);
        response.status(statusCode);
        logger.error(errorMessage);
    }

    /*
     * Get only cluster instances of service.
     * @param serviceId The parameter identifies to Id of any service.
     * @param accountId The parameter identifies to Id of any account.
     * @return List of component and host cluster details.
     */
    public static List<CompInstClusterDetails> getComponentClusterList(int serviceId, int accountId) {
        List<CompInstClusterDetails> componentInstanceLists = getAllInstancesByService(serviceId, accountId);
        if (componentInstanceLists.isEmpty()) {
            return new ArrayList<>();
        }

        return componentInstanceLists.stream()
                .filter(inst -> inst.getIsCluster() == 1)
                .collect(Collectors.toList());
    }

    /*
     * Get all instances mapped to service.
     *
     * @param serviceId The parameter identifies to Id of any service.
     * @param accountId The parameter identifies to Id of any account.
     * @return List of component instance details.
     */
    private static List<CompInstClusterDetails> getAllInstancesByService(int serviceId, int accountId) {
        List<TagMappingDetails> tagMappings = MasterDataService.getTagMappingDetails(accountId);
        if (tagMappings.isEmpty()) {
            return new ArrayList<>();
        }

        Set<Integer> instIds = tagMappings.stream()
                .filter(t -> t.getTagId() == 1)
                .filter(t -> t.getTagKey().equals(String.valueOf(serviceId)))
                .filter(t -> t.getObjectRefTable().equalsIgnoreCase(Constants.COMP_INSTANCE_TABLE))
                .map(TagMappingDetails::getObjectId).collect(Collectors.toSet());
        if (instIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<CompInstClusterDetails> componentInstanceLists = MasterDataService.getCompInstanceDetails(accountId);
        if (componentInstanceLists.isEmpty()) {
            return new ArrayList<>();
        }

        return componentInstanceLists.stream()
                .filter(inst -> instIds.contains(inst.getInstanceId()))
                .collect(Collectors.toList());
    }

    public static List<TagMappingDetails> getTagMapping(String tagKey, String tagValue, String tagName,
                                                        String objectRef, int objectRefId, int accountId) {

        TagDetailsBean tagBean = MasterDataService.getTagDetailsForAccount(accountId).parallelStream()
                .filter(tagDetailsBean -> tagDetailsBean.getName().equalsIgnoreCase(tagName))
                .findAny().orElse(null);
        if (tagBean == null) {
            return new ArrayList<>();
        }

        List<TagMappingDetails> tagMappings = MasterDataService.getTagMappingDetails(accountId);
        return tagMappings.parallelStream()
                .filter(t -> t.getTagId() == tagBean.getId())
                .filter(t -> Objects.isNull(objectRef) || t.getObjectRefTable().equalsIgnoreCase(objectRef))
                .filter(t -> objectRefId == 0 || t.getObjectId() == objectRefId)
                .filter(t -> Objects.isNull(tagKey) || tagKey.equals(t.getTagKey()))
                .filter(t -> Objects.isNull(tagValue) || tagValue.equals(t.getTagValue()))
                .collect(Collectors.toList());
    }

    public static String encryptInBCEC(String input) throws ServerException {
        if (input != null && !input.isEmpty()) {
            try {
                Security.removeProvider(Constants.BC_PROVIDER_NAME);
                return Commons.encrypt(input);
            } catch (Exception e) {
                logger.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
                throw new ServerException("Error while decrypting the password");
            }
        }
        return "";
    }

    public static String decryptInBCEC(String input) throws ServerException {
        if (input != null && !input.isEmpty()) {
            try {
                Security.removeProvider(Constants.BC_PROVIDER_NAME);
                return Commons.decrypt(input);
            } catch (Exception e) {
                logger.error("Exception encountered while decrypting the password. Details: {}", e.getMessage(), e);
                throw new ServerException("Error while decrypting the password");
            }
        }
        return "";
    }



    private static List<CompInstClusterDetails> getComponentInstanceList(int accountId, List<Integer> instIds, HostComInstanceType instanceTypes) throws DataProcessingException {
        List<CompInstClusterDetails> compInstClusterDetails;
        try {
            compInstClusterDetails = MasterDataService.getCompInstanceDetails(accountId);
        } catch (Exception e) {
            logger.error("Exception occurred while getting compInstances for accountId");
            throw new DataProcessingException("Exception occurred while getting compInstances for accountId");
        }
        if (Objects.isNull(compInstClusterDetails)) {
            logger.error("There is no data in comp_instance table for the provided accountId [{}].", accountId);
            return Collections.emptyList();
        }
        Predicate<CompInstClusterDetails> instPredicate = getInstancePredicate(instIds, instanceTypes);
        return compInstClusterDetails.stream().filter(instPredicate).collect(Collectors.toList());

    }

    private static Predicate<CompInstClusterDetails> getInstancePredicate(List<Integer> instIds, HostComInstanceType instanceTypes) {
        Predicate<CompInstClusterDetails> getPredicate = null;
        if (instanceTypes.equals(HostComInstanceType.HOST_CLUSTER)) {
            getPredicate = cc -> cc.getIsCluster() == 1 && cc.getHostId() == 0 && instIds.contains(cc.getInstanceId());
        } else if (instanceTypes.equals(HostComInstanceType.COMP_CLUSTER)) {
            getPredicate = c -> c.getIsCluster() == 1 && c.getHostId() > 0 && instIds.contains(c.getInstanceId());

        }
        return getPredicate;
    }

    private static SmsParameter getSMSParameter(SMSParameterBean smsParameterBean) {
        SmsParameter smsParameter = new SmsParameter();
        smsParameter.setParameterId(smsParameterBean.getId());
        smsParameter.setParameterName(smsParameterBean.getParameterName());
        smsParameter.setParameterValue(smsParameterBean.getParameterValue());
        smsParameter.setParameterType(MasterCache.getMstSubTypeForSubTypeId(smsParameterBean.getParameterTypeId()).getSubTypeName());
        smsParameter.setIsPlaceholder(smsParameterBean.getIsPlaceholder() == 1);
        return smsParameter;
    }

    public static SmsDetails getSMSDetails(SMSDetailsBean smsDetailsBean) throws ServerException {
        List<SMSParameterBean> smsParameterBeans = new NotificationDataService().getSMSParameters(smsDetailsBean.getId(), null);
        List<SmsParameter> smsParameters = new ArrayList<>();
        if (!smsParameterBeans.isEmpty()) {
            smsParameters = smsParameterBeans.stream()
                    .map(CommonUtils::getSMSParameter)
                    .collect(Collectors.toList());
        }
        return SmsDetails.builder()
                .address(smsDetailsBean.getAddress())
                .id(smsDetailsBean.getId())
                .countryCode(smsDetailsBean.getCountryCode())
                .port(smsDetailsBean.getPort())
                .protocolName(MasterCache.getMstSubTypeForSubTypeId(smsDetailsBean.getProtocolId()).getSubTypeName())
                .httpMethod(smsDetailsBean.getHttpMethod())
                .httpRelativeUrl(smsDetailsBean.getHttpRelativeUrl())
                .isMultiRequest(smsDetailsBean.getIsMultiRequest())
                .postData(smsDetailsBean.getPostData())
                .parameters(smsParameters)
                .username(smsDetailsBean.getUsername())
                .password(smsDetailsBean.getPassword())
                .persistSmsNotifications(smsDetailsBean.getPersistSmsNotifications())
                .build();
    }

    public static SmtpDetails getSMTPDetails(SMTPDetailsBean smtpDetailsBean) {
        String security = "";
        if (smtpDetailsBean.getSecurityId() > 0) {
            security = MasterCache.getMstSubTypeForSubTypeId(
                    smtpDetailsBean.getSecurityId()).getSubTypeName();
        }
        return SmtpDetails.builder()
                .id(smtpDetailsBean.getId())
                .address(smtpDetailsBean.getAddress())
                .port(smtpDetailsBean.getPort())
                .username(smtpDetailsBean.getUsername())
                .password(smtpDetailsBean.getPassword())
                .fromRecipient(smtpDetailsBean.getFromRecipient())
                .security(security)
                .persistEmailNotifications(smtpDetailsBean.getPersistEmailNotifications())
                .build();
    }

    public static ObjectMapper getObjectMapperWithHtmlEncoder() {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule("HTML-Encoder", objectMapper.version()).addDeserializer(String.class, new EscapeHTML());

        objectMapper.registerModule(simpleModule);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    public static ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    public static void registerMBean(String beanName, String objName, ApplicationInfo applicationInfo) {
        try {
            ObjectName objectName = new ObjectName(objName+":name="+beanName);
            MBeanServer server = ManagementFactory.getPlatformMBeanServer();
            server.registerMBean(applicationInfo, objectName);
        } catch (Exception e) {
            logger.error("Error occurred while registering the bean, Bean:{}, Object:{}", beanName, objName);
        }
    }

    public static void basicRequestValidation(RequestObject requestObject) throws ClientException {
        if (requestObject == null) {
            logger.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (!requestObject.getHttpType().equalsIgnoreCase("GET") && StringUtils.isEmpty(requestObject.getBody())) {
            logger.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        String authKey = requestObject.getHeaders().get(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            logger.error("Invalid authorization token. Reason: It is either NULL or empty");
            throw new ClientException("Invalid authorization token");
        }

        String identifier = requestObject.getParams().get(Constants.ACCOUNT_IDENTIFIER);
        if (identifier == null || identifier.trim().isEmpty()) {
            logger.error("Account identifier is invalid");
            throw new ClientException("Account identifier is invalid");
        }
    }
    public static String customReadLine(BufferedReader br) {
        try {
            int intC;
            StringBuilder line = new StringBuilder();
            boolean isNewLine = false;

            while ((intC = br.read()) != -1) {
                char c = (char) intC;

                if (c == '\r') {
                    continue;
                }

                if (c == '\n') {
                    isNewLine = true;
                    break;
                }

                line.append(c);
            }

            if (line.length() > 0 || isNewLine) {
                return line.toString();
            }
        } catch (IOException e) {
            logger.error("Error occurred while reading line", e);
        }

        return null;
    }

}

class EscapeHTML extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser jp, DeserializationContext ctxt)
            throws IOException {
        String s = jp.getValueAsString();
        return StringEscapeUtils.escapeHtml4(s);
    }
}
