package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.ConnectionDetailsPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class AddConnectionBLTest extends Specification {

    def "Client validation failure - NULL request object"() {
        when:
        new AddConnectionBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL request body"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        requestObject.setQueryParams(queryMapData)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_REQUEST_BODY
    }

    def "Client validation failure - request body garbage String"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("testing-with-dummy-body")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.JSON_INVALID
    }

    def "Client validation failure - request body list empty"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_REQUEST_BODY
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Invalid datatype of request body 'sourceServiceIdentifier' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": {\"a\": \"b\"},\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."
    }

    def "Client validation failure - Invalid datatype of request body 'isDiscovery' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": \"Sample-String\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."
    }

    def "Client validation failure - Duplicate Connection Pairs"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.DUPLICATE_CONNECTION
    }

    def "Client validation failure - null value of request body 'sourceServiceIdentifier' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": null,\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Client validation failure - empty value of request body 'sourceServiceIdentifier' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Client validation failure - null value of request body 'destinationServiceIdentifier' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": null,\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Client validation failure - empty value of request body 'destinationServiceIdentifier' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Client validation failure - empty/null value of both request body 'sourceServiceIdentifier' & 'destinationServiceIdentifier' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": null,\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Client validation failure - same value of both request body 'sourceServiceIdentifier' & 'destinationServiceIdentifier' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-Web-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Client validation failure - isDiscovery value other than 0 or 1"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 10\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Success Case - With Single request body 'sourceServiceIdentifier' & 'destinationServiceIdentifier' key pair"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<ConnectionDetailsPojo>> data = new AddConnectionBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 1
        data.getPojoObject().get(0).getSourceServiceIdentifier() == "NB-Web-Service"
        data.getPojoObject().get(0).getDestinationServiceIdentifier() == "NB-DB-Service"
    }

    def "Success Case - With Multiple request body 'sourceServiceIdentifier' & 'destinationServiceIdentifier' key pair"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-Web-Service\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-Web-Service\",\n" +
                "        \"destinationName\": \"NB-DB-Service\",\n" +
                "        \"destinationServiceIdentifier\": \"NB-DB-Service\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"sourceName\": \"NB-User\",\n" +
                "        \"sourceServiceIdentifier\": \"NB-User\",\n" +
                "        \"destinationName\": \"LOS-User\",\n" +
                "        \"destinationServiceIdentifier\": \"LOS-User\",\n" +
                "        \"isDiscovery\": 1\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<ConnectionDetailsPojo>> data = new AddConnectionBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0).getSourceServiceIdentifier() == "NB-Web-Service"
        data.getPojoObject().get(1).getDestinationServiceIdentifier() == "LOS-User"

    }

}
