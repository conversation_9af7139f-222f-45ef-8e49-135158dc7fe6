package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.InstanceKpiThresholdDetails
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class AddInstanceLevelKpiThresholdBLTest extends Specification {

    def "Client validation failure - Invalid request body"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "Client validation failure - NULL request object"() {
        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account identifier is invalid"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", " ")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account identifier is invalid"
    }

    def "Client validation failure - empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", " ")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - invalid request"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"lala\" : 1\n" +
                "  }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(Constants.INSTANCE_ID, "26")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error while parsing request body"
    }

    def "Client validation failure - invalid JSON - NULL attributeValue"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\":" + null + ",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation of violation configuration(s) failed"
    }

    def "Client validation failure - invalid JSON - Empty attributeValue"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"\" ,\n" +
                "    \"operation\": \"lesser than lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation of violation configuration(s) failed"
    }

    def "Client validation failure - invalid JSON"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 0,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"dsfsdfsdfsdfsdfsdfsdfsdfsdfskdjflksdjflksdjflksdjflksdjflksdjflksjfwoerpweirpoweirpoweirpoweisdkjfslkdjflksjflksjdflksjdflksc,cv,mxnv,mxnvc,xmbvcnmxbvdms;ldkfsldjfweporwpoeirwoeirwoersjdflksjdflksjfdlsdnf,smnv,xmnv,mxnv,xmnvlxmvlkdsjflskdjfsdkfjlskdjfsdfskldjfslkddsfsdfsdfsdfsdfsdfsdfsdfsdfskdjflksdjflksdjflksdjflksdjflksdjflksjfwoerpweirpoweirpoweirpoweisdkjfslkdjflksjflksjdflksjdflksc,cv,mxnv,mxnvc,xmbvcnmxbvdms;ldkfsldjfweporwpoeirwoeirwoersjdflksjdflksjfdlsdnf,smnv,xmnv,mxnv,xmnvlxmvlkdsjflskdjfsdkfjlskdj\" ,\n" +
                "    \"operation\": \"\",\n" +
                "    \"minThreshold\": -3,\n" +
                "   \"maxThreshold\" : -1,\n" +
                "   \"status\" : 2,\n" +
                "   \"severity\" : 2\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation of violation configuration(s) failed"
    }

    def "Client validation failure - invalid JSON operation - Empty"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 0,\n" +
                "    \"groupKpiId\": 0,\n" +
                "    \"attributeValue\": \"ALL\" ,\n" +
                "    \"operation\": \"\",\n" +
                "    \"minThreshold\": -3,\n" +
                "   \"maxThreshold\" : -1,\n" +
                "   \"status\" : 2,\n" +
                "   \"severity\" : 2\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation of violation configuration(s) failed"
    }

    def "Client validation failure - invalid JSON operation - NULL"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 0,\n" +
                "    \"groupKpiId\": 0,\n" +
                "    \"attributeValue\": \"sshd\" ,\n" +
                "    \"operation\":"+ null + ",\n" +
                "    \"minThreshold\": -3,\n" +
                "   \"maxThreshold\" : -1,\n" +
                "   \"status\" : 2,\n" +
                "   \"severity\" : 2\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation of violation configuration(s) failed"
    }

    def "Client validation failure - invalid JSON status"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 0,\n" +
                "    \"groupKpiId\": 0,\n" +
                "    \"attributeValue\": \"sshd\" ,\n" +
                "    \"operation\":"+ null + ",\n" +
                "    \"minThreshold\": -3,\n" +
                "   \"maxThreshold\" : -1,\n" +
                "   \"status\" : 0,\n" +
                "   \"severity\" : 2\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation of violation configuration(s) failed"
    }

    def "Client validation failure - invalid threshold values"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 0,\n" +
                "    \"groupKpiId\": 0,\n" +
                "    \"attributeValue\": \"sshd\" ,\n" +
                "    \"operation\":\"not between\", \n" +
                "    \"minThreshold\": 3,\n" +
                "   \"maxThreshold\" : 1,\n" +
                "   \"status\" : 0,\n" +
                "   \"severity\" : 2\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation of violation configuration(s) failed"
    }

    def "Client validation failure - invalid threshold values for valid operation"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 0,\n" +
                "    \"groupKpiId\": 0,\n" +
                "    \"attributeValue\": \"sshd\" ,\n" +
                "    \"operation\":\"lesser than\", \n" +
                "    \"minThreshold\": 1,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 0,\n" +
                "   \"severity\" : 2\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation of violation configuration(s) failed"
    }

    def "Client validation failure - duplicates in request"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\"instances\" : [\"26\"],\"thresholds\" : [\n" +
                " {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  },\n" +
                "   {\n" +
                "    \"kpiId\": 32,\n" +
                "    \"groupKpiId\": 4,\n" +
                "    \"attributeValue\": \"sshd\",\n" +
                "    \"operation\": \"lesser than\",\n" +
                "    \"minThreshold\": 2,\n" +
                "   \"maxThreshold\" : 3,\n" +
                "   \"status\" : 1,\n" +
                "   \"severity\" : 1\n" +
                "  }\n" +
                "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Duplicate threshold details for KPI attributes(s) provided in the request"
    }

    def "Client validation success"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody(
                "{\"instances\" : [\"26\", \"27\"],\"thresholds\" : [\n" +
                        " {\n" +
                        "    \"kpiId\": 32,\n" +
                        "    \"groupKpiId\": 4,\n" +
                        "    \"attributeValue\": \"sshd\",\n" +
                        "    \"operation\": \"lesser than\",\n" +
                        "    \"minThreshold\": 2,\n" +
                        "   \"maxThreshold\" : 3,\n" +
                        "   \"status\" : 1,\n" +
                        "   \"severity\" : 1\n" +
                        "  },\n" +
                        "   {\n" +
                        "    \"kpiId\": 33,\n" +
                        "    \"groupKpiId\": 4,\n" +
                        "    \"attributeValue\": \"sshd\",\n" +
                        "    \"operation\": \"lesser than\",\n" +
                        "    \"minThreshold\": 2,\n" +
                        "   \"maxThreshold\" : 3,\n" +
                        "   \"status\" : 1,\n" +
                        "   \"severity\" : 1\n" +
                        "  }\n" +
                        "]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<InstanceKpiThresholdDetails> data = new AddInstanceLevelKpiThresholdBL().clientValidation(requestObject)

        then:
        data.account == null
        data.accountIdentifier == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.pojoObject.thresholds.size() == 2
        data.pojoObject.instances.get(0) == 26
    }
}
