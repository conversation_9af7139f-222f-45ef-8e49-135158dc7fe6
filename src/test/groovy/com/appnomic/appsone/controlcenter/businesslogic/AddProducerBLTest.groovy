package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.ProducerDetails
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import org.apache.commons.lang.RandomStringUtils
import spock.lang.Specification

class AddProducerBLTest extends Specification{

    def "Client validation failure - NULL request object"() {
        when:
        new AddProducerBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL request body"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        requestObject.setQueryParams(queryMapData)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{  \"name\": \"TP-DefaultKPI\",  \"description\": \"This is a producer\",  \"kpiType\": " +
            "\"Core\",  \"isGroupKpi\": 0,  \"producerType\": \"SCRIPT\",  \"producerAttributes\": {    " +
            "\"script_name\": \"test.sh\",    \"signature\": \"testSignature\"  },  \"kpiMapping\": [    {     " +
            " \"kpiIdentifier\": \"CPU_UTIL\",      \"componentVersionId\": \"6.x\",      \"componentName\": " +
            "\"RHEL\",      \"componentTypeName\": \"Host\",      \"isDefault\": 1    }  ],  \"parameters\": [    " +
            "{      \"parameterType\": \"COMMANDLINE\",      \"parameterName\": \"host\",      " +
            "\"parameterValue\": \"{HostAddress}\"    }  ]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{  \"name\": \"TP-DefaultKPI\",  \"description\": \"This is a producer\",  \"kpiType\": " +
                "\"Core\",  \"isGroupKpi\": 0,  \"producerType\": \"SCRIPT\",  \"producerAttributes\": {    " +
                "\"script_name\": \"test.sh\",    \"signature\": \"testSignature\"  },  \"kpiMapping\": [    {     " +
                " \"kpiIdentifier\": \"CPU_UTIL\",      \"componentVersionId\": \"6.x\",      \"componentName\": " +
                "\"RHEL\",      \"componentTypeName\": \"Host\",      \"isDefault\": 1    }  ],  \"parameters\": [    " +
                "{      \"parameterType\": \"COMMANDLINE\",      \"parameterName\": \"host\",      " +
                "\"parameterValue\": \"{HostAddress}\"    }  ]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty authorization token"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{  \"name\": \"TP-DefaultKPI\",  \"description\": \"This is a producer\",  \"kpiType\": " +
                "\"Core\",  \"isGroupKpi\": 0,  \"producerType\": \"SCRIPT\",  \"producerAttributes\": {    " +
                "\"script_name\": \"test.sh\",    \"signature\": \"testSignature\"  },  \"kpiMapping\": [    {     " +
                " \"kpiIdentifier\": \"CPU_UTIL\",      \"componentVersionId\": \"6.x\",      \"componentName\": " +
                "\"RHEL\",      \"componentTypeName\": \"Host\",      \"isDefault\": 1    }  ],  \"parameters\": [    " +
                "{      \"parameterType\": \"COMMANDLINE\",      \"parameterName\": \"host\",      " +
                "\"parameterValue\": \"{HostAddress}\"    }  ]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - NULL authorization token"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{  \"name\": \"TP-DefaultKPI\",  \"description\": \"This is a producer\",  \"kpiType\": " +
                "\"Core\",  \"isGroupKpi\": 0,  \"producerType\": \"SCRIPT\",  \"producerAttributes\": {    " +
                "\"script_name\": \"test.sh\",    \"signature\": \"testSignature\"  },  \"kpiMapping\": [    {     " +
                " \"kpiIdentifier\": \"CPU_UTIL\",      \"componentVersionId\": \"6.x\",      \"componentName\": " +
                "\"RHEL\",      \"componentTypeName\": \"Host\",      \"isDefault\": 1    }  ],  \"parameters\": [    " +
                "{      \"parameterType\": \"COMMANDLINE\",      \"parameterName\": \"host\",      " +
                "\"parameterValue\": \"{HostAddress}\"    }  ]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Empty authorization key"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{  \"name\": \"TP-DefaultKPI\",  \"description\": \"This is a producer\",  \"kpiType\": " +
                "\"Core\",  \"isGroupKpi\": 0,  \"producerType\": \"SCRIPT\",  \"producerAttributes\": {    " +
                "\"script_name\": \"test.sh\",    \"signature\": \"testSignature\"  },  \"kpiMapping\": [    {     " +
                " \"kpiIdentifier\": \"CPU_UTIL\",      \"componentVersionId\": \"6.x\",      \"componentName\": " +
                "\"RHEL\",      \"componentTypeName\": \"Host\",      \"isDefault\": 1    }  ],  \"parameters\": [    " +
                "{      \"parameterType\": \"COMMANDLINE\",      \"parameterName\": \"host\",      " +
                "\"parameterValue\": \"{HostAddress}\"    }  ]}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Invalid request body"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("testing-with-dummy-body")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error occurred while parsing request for add producer data. Check CC Logs."
    }

    def "Client validation failure - Empty keys in request body"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Producer name length > 45"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \""+RandomStringUtils.random(100,true,false)+"\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Invalid producer naming convention used"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"\$\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Producer description length > 256."(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \""+RandomStringUtils.random(300,true,true)+"\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'name' key missing"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": null,\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\":  [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'name' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": null,\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\":  [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'name' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'description' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": null,\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\":  [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'description' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'kpiType' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": null,\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\":  [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'kpiType' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation pass - Request body 'isGroupKpi' key missing"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\":  [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getPojoObject().getIsGroupKpi() == 0
    }

    def "Client validation pass - Request body 'isGroupKpi' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": null,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\":  [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getPojoObject().getIsGroupKpi() == 0
    }

    def "Client validation pass - Request body 'isGroupKpi' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": \"\",\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getPojoObject().getIsGroupKpi() == 0
    }

    def "Client validation failure - Invalid isGroupKPI value > 1"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 3,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Invalid isGroupKPI value < 0"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": -3,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'producerType' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": null,\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\":  [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'producerType' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'producerAttributes' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": null,\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().getClass() == ProducerDetails
        data.getPojoObject().getName() == "TP-DefaultKPI"
        data.getPojoObject().getDescription() == "This is a producer"
        data.getPojoObject().getKpiType() == "Core"
        data.getPojoObject().getIsGroupKpi() == 0
        data.getPojoObject().getProducerType() == "SCRIPT"
        data.getPojoObject().getProducerAttributes() == null
        data.getPojoObject().getKpiMapping().get(0).getKpiIdentifier() == "CPU_UTIL"
        data.getPojoObject().getKpiMapping().get(0).getComponentVersionId() == "6.x"
        data.getPojoObject().getKpiMapping().get(0).getComponentName() == "RHEL"
        data.getPojoObject().getKpiMapping().get(0).getComponentTypeName() == "Host"
        data.getPojoObject().getParameters().get(0).getParameterType() == "COMMANDLINE"
        data.getPojoObject().getParameters().get(0).getParameterName() == "host"
        data.getPojoObject().getParameters().get(0).getParameterValue() == "{HostAddress}"
    }

    def "Client validation failure - Request body 'producerAttributes' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": \"\",\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error occurred while parsing request for add producer data. Check CC Logs."
    }

    def "Client validation failure - Request body 'producerAttributes' random garbage value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": \""+RandomStringUtils.random(10,true,true)+"\",\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error occurred while parsing request for add producer data. Check CC Logs."
    }

    def "Client validation pass - Request body 'producerAttributes' key missing"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().getClass() == ProducerDetails
        data.getPojoObject().getName() == "TP-DefaultKPI"
        data.getPojoObject().getDescription() == "This is a producer"
        data.getPojoObject().getKpiType() == "Core"
        data.getPojoObject().getIsGroupKpi() == 0
        data.getPojoObject().getProducerType() == "SCRIPT"
        data.getPojoObject().getProducerAttributes().size() == 0
        data.getPojoObject().getKpiMapping().get(0).getKpiIdentifier() == "CPU_UTIL"
        data.getPojoObject().getKpiMapping().get(0).getComponentVersionId() == "6.x"
        data.getPojoObject().getKpiMapping().get(0).getComponentName() == "RHEL"
        data.getPojoObject().getKpiMapping().get(0).getComponentTypeName() == "Host"
        data.getPojoObject().getParameters().get(0).getParameterType() == "COMMANDLINE"
        data.getPojoObject().getParameters().get(0).getParameterName() == "host"
        data.getPojoObject().getParameters().get(0).getParameterValue() == "{HostAddress}"
    }

    def "Client validation failure - Request body 'kpiMapping' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\":  null,\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'kpiMapping' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": \"\",\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error occurred while parsing request for add producer data. Check CC Logs."
    }

    def "Client validation failure - Request body 'kpiMapping' random garbage value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": "+RandomStringUtils.random(10,true,true)+",\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error occurred while parsing request for add producer data. Check CC Logs."
    }

    def "Client validation failure - Request body 'kpiMapping' value empty array"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'kpiMapping' key missing"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'parameters' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": null\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - Request body 'parameters' value empty "(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": \"\"\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error occurred while parsing request for add producer data. Check CC Logs."
    }

    def "Client validation pass - Request body 'parameters' value empty array"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": []\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getPojoObject().getParameters() == []
    }

    def "Client validation failure - Request body 'parameters' random garbage value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": "+RandomStringUtils.random(10,true,true)+"\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)


        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error occurred while parsing request for add producer data. Check CC Logs."
    }

    def "Client validation pass - Request body 'parameters' key missing"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getPojoObject().getParameters() == []
    }

    def "Client validation failure - kpiMapping body 'kpiIdentifier' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": null,\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - kpiMapping body 'kpiIdentifier' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - kpiMapping body 'componentVersionId' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": null,\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - kpiMapping body 'componentVersionId' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - kpiMapping body 'componentName' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": null,\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - kpiMapping body 'componentName' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - kpiMapping body 'componentTypeName' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": null,\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - kpiMapping body 'componentTypeName' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation pass - kpiMapping body 'isDefault' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getPojoObject().getKpiMapping().get(0).isDefault == 0
    }

    def "Client validation pass - kpiMapping body 'isDefault' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getPojoObject().getKpiMapping().get(0).isDefault == 0
    }

    def "Client validation failure - kpiMapping body 'isDefault' value > 1"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 3\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - kpiMapping body 'isDefault' value < 0"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": -3\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation pass - kpiMapping body 'isDefault' key missing"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getPojoObject().getKpiMapping().get(0).isDefault == 1
    }

    def "Client validation failure - parameters body 'parameterType' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": null,\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - parameters body 'parameterType' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - parameters body 'parameterName' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": null,\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - parameters body 'parameterName' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - parameters body 'parameterName' value length > 45"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \""+RandomStringUtils.random(100,true,true)+"\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - parameters body 'parameterValue' value null"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": null\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - parameters body 'parameterValue' value empty"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Client validation failure - parameters body 'parameterValue' value length > 512"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \""+ RandomStringUtils.random(600,true,true)+"\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddProducerBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Validation failure for ProducerDetails"
    }

    def "Success Case"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "    \"name\": \"TP-DefaultKPI\",\n" +
                "    \"description\": \"This is a producer\",\n" +
                "    \"kpiType\": \"Core\",\n" +
                "    \"isGroupKpi\": 0,\n" +
                "    \"producerType\": \"SCRIPT\",\n" +
                "    \"producerAttributes\": {\n" +
                "        \"script_name\": \"test.sh\",\n" +
                "        \"signature\": \"testSignature\"\n" +
                "    },\n" +
                "    \"kpiMapping\": [\n" +
                "        {\n" +
                "            \"kpiIdentifier\": \"CPU_UTIL\",\n" +
                "            \"componentVersionId\": \"6.x\",\n" +
                "            \"componentName\": \"RHEL\",\n" +
                "            \"componentTypeName\": \"Host\",\n" +
                "            \"isDefault\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"parameterType\": \"COMMANDLINE\",\n" +
                "            \"parameterName\": \"host\",\n" +
                "            \"parameterValue\": \"{HostAddress}\"\n" +
                "        }\n" +
                "    ]\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<ProducerDetails> data = new AddProducerBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().getClass() == ProducerDetails
        data.getPojoObject().getName() == "TP-DefaultKPI"
        data.getPojoObject().getDescription() == "This is a producer"
        data.getPojoObject().getKpiType() == "Core"
        data.getPojoObject().getIsGroupKpi() == 0
        data.getPojoObject().getProducerType() == "SCRIPT"
        data.getPojoObject().getProducerAttributes().size() == 2
        data.getPojoObject().getProducerAttributes().get("script_name") == "test.sh"
        data.getPojoObject().getProducerAttributes().get("signature") == "testSignature"
        data.getPojoObject().getKpiMapping().get(0).getKpiIdentifier() == "CPU_UTIL"
        data.getPojoObject().getKpiMapping().get(0).getComponentVersionId() == "6.x"
        data.getPojoObject().getKpiMapping().get(0).getComponentName() == "RHEL"
        data.getPojoObject().getKpiMapping().get(0).getComponentTypeName() == "Host"
        data.getPojoObject().getParameters().get(0).getParameterType() == "COMMANDLINE"
        data.getPojoObject().getParameters().get(0).getParameterName() == "host"
        data.getPojoObject().getParameters().get(0).getParameterValue() == "{HostAddress}"
    }
}
