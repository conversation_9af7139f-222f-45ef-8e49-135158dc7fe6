package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.pojo.Supervisor
import spock.lang.Shared
import spock.lang.Specification

class AddSupervisorBLTest extends Specification {

    def "add supervisor - verification of IOException"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "  \"name\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorType\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddSupervisorBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Error while parsing request body"
    }

    def "add supervisor - invalid Supervisor attributes"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("{\n" +
                "  \"name\": \"Test_20_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA_eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new AddSupervisorBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input parameter(s) 'supervisorName, supervisorTypeName' invalid"
    }

    def "add supervisor - invalid authorization token"() {
        setup:
        UtilityBean<Supervisor> utilityBean = UtilityBean.<Supervisor> builder()
                .authToken("testing-with-dummy-authorization-header")
                .build()

        when:
        new AddSupervisorBL().serverValidation(utilityBean)

        then:
        final ServerException e = thrown()
        e.getMessage() == "ServerException : Error in retrieving user identifier"
    }

    def "Add supervisor - failure"() {
        when:
        new AddSupervisorBL().process(null)

        then:
        final DataProcessingException e = thrown()
        e.getMessage() == "DataProcessingException : Error while adding supervisor"
    }

    @Shared
    def s = new Supervisor()

    def "Verification of invalid parameters for add supervisor"() {
        expect:
        s.validate() == retVal

        where:
        supervisorIdentifier    | supervisorName  | supervisorType                | hostAddress            | hostBoxName            | version            | mode                      | status             | retVal
        s.setSupervisorId(null) | s.setName(null) | s.setSupervisorTypeName(null) | s.setHostAddress(null) | s.setHostBoxName(null) | s.setVersion(null) | s.setMode("LOCAL-REMOTE") | s.setStatus(false) | "supervisorName, hostBoxName, supervisorIdentifier, supervisorTypeName, version, hostAddress, mode"
        s.setSupervisorId(" ")  | s.setName(" ")  | s.setSupervisorTypeName(" ")  | s.setHostAddress(" ")  | s.setHostBoxName(" ")  | s.setVersion(" ")  | s.setMode(" ")            | s.setStatus(false) | "supervisorName, hostBoxName, supervisorIdentifier, supervisorTypeName, version, hostAddress, mode"
    }

    def "Verification of invalid parameters of add supervisor - max length"() {
        setup:
        Supervisor supervisor = Supervisor.builder()
                .name("supervisorName-123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890")
                .supervisorId("supervisorIdentifier-123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890")
                .hostBoxName("hostBoxName-123456789012345678901234567890123456789012345678901234567890")
                .supervisorTypeName(" ")
                .version("version-123456789012345678901234567890")
                .hostAddress("hostAddress-123456789012345678901234567890123456789012345678901234567890")
                .mode("mode-12345678901234567890123456789012345678901234567890")
                .build()

        when:
        def retVal = supervisor.validate()

        then:
        retVal == "supervisorName, hostBoxName, supervisorIdentifier, supervisorTypeName, version, hostAddress, mode"
    }

    def "Verification of invalid parameters for update supervisor"() {
        expect:
        s.validate() == retVal

        where:
        supervisorName  | hostAddress            | hostBoxName            | mode                      | retVal
        s.setName(null) | s.setHostAddress(null) | s.setHostBoxName(null) | s.setMode("LOCAL-REMOTE") | "supervisorName, hostBoxName, supervisorIdentifier, supervisorTypeName, version, hostAddress, mode"
        s.setName(" ")  | s.setHostAddress(" ")  | s.setHostBoxName(" ")  | s.setMode(" ")            | "supervisorName, hostBoxName, supervisorIdentifier, supervisorTypeName, version, hostAddress, mode"
    }

    def "Verification of invalid parameters of update supervisor - max length"() {
        setup:
        Supervisor supervisor = Supervisor.builder()
                .name("supervisorName-123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890")
                .hostBoxName("hostBoxName-123456789012345678901234567890123456789012345678901234567890")
                .hostAddress("hostAddress-123456789012345678901234567890123456789012345678901234567890")
                .mode("mode-12345678901234567890123456789012345678901234567890")
                .build()

        when:
        def retVal = supervisor.validateForUpdate()

        then:
        retVal == "name, hostBoxName, hostAddress, mode"
    }
}
