package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.DataProcessingException
import com.appnomic.appsone.controlcenter.pojo.ApplicationWhitelist
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class ApplicationWhitelistBLTest extends Specification {
    def "NULL Request "() {

        when:
        new ApplicationWhitelistBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."

    }

    def "NULL Auth Token "() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)
        when:
        new ApplicationWhitelistBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Authorization Token is empty."

    }

    def "Auth Token Checking empty value"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", " ")
        requestObject.setHeaders(headers)
        when:
        new ApplicationWhitelistBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Authorization Token is empty."

    }

    def "NULL Account Identifier "() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "Auth-Token")
        requestObject.setHeaders(headers)
        when:
        new ApplicationWhitelistBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account Identifier should not be empty."

    }

    def "Negative Value in Suppression Interval"() {
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("[{\"applicationName\":\"Heal Health Application\",\"applicationSuppressionInterval\":-1," +
                "\"serviceWhitelist\":[{\"serviceName\":\"CCService\",\"kpiWhitelist\":[]," +
                "\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"UI Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"Event_Detector Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"A1_Event_Handler Service\",\"kpiWhitelist\":[\"CPU Util\",\"Memory Util\"]," +
                "\"serviceSuppressionInterval\":0}]},{\"applicationName\":\"apps2\",\"applicationSuppressionInterval\":0," +
                "\"serviceWhitelist\":[]}]")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal_health")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "Auth-token")
        requestObject.setHeaders(headers)

        when:
        new ApplicationWhitelistBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Whitelist suppression interval cannot be negative: -1"

    }

    def "NULL RequestBody "() {
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("{\"serviceSuppressionInterval\":0}")
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal_health")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "Auth-token")
        requestObject.setHeaders(headers)

        when:
        new ApplicationWhitelistBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage()

    }

    def "Success Case with NULL value"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal_health")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "Auth-token")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<Object> data = new ApplicationWhitelistBL().clientValidation(requestObject)

        then:
        data.getAccountIdentifier() == "heal_health"
        data.getPojoObject() == null
        data.getAuthToken() == "Auth-token"

    }

    def "SUCCESS Case"() {
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("[{\"applicationName\":\"Heal Health Application\",\"applicationSuppressionInterval\":0," +
                "\"serviceWhitelist\":[{\"serviceName\":\"CCService\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"UI Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"Event_Detector Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"A1_Event_Handler Service\",\"kpiWhitelist\":[\"CPU Util\",\"Memory Util\"]," +
                "\"serviceSuppressionInterval\":0}]},{\"applicationName\":\"apps2\",\"applicationSuppressionInterval\":0," +
                "\"serviceWhitelist\":[]}]")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal_health")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "Auth-token")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<Object> data = new ApplicationWhitelistBL().clientValidation(requestObject)

        then:
        data.getAccountIdentifier() == "heal_health"
        data.getAuthToken() == "Auth-token"
    }
    def "Add whitelist "(){
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("[{\"applicationName\":\"DEMO_Application\",\"applicationSuppressionInterval\":0," +
                "\"serviceWhitelist\":[{\"serviceName\":\"CCService\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"UI Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"Event_Detector Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"A1_Event_Handler Service\",\"kpiWhitelist\":[\"CPU Util\",\"Memory Util\"]," +
                "\"serviceSuppressionInterval\":0}]},{\"applicationName\":\"apps2\",\"applicationSuppressionInterval\":0," +
                "\"serviceWhitelist\":[]}]")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal_health")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw")
        requestObject.setHeaders(headers)
        UtilityBean<Object> data = new ApplicationWhitelistBL().clientValidation(requestObject)

        when:
        new ApplicationWhitelistBL().addWhitelist(data)
        then:
        noExceptionThrown()

    }
    def "Add whitelist error "(){
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("[]")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw")
        requestObject.setHeaders(headers)
        UtilityBean<Object> data = new ApplicationWhitelistBL().clientValidation(requestObject)

        when:
         new ApplicationWhitelistBL().addWhitelist(data)
        then:
        final DataProcessingException e=thrown()
        e.getMessage()== "DataProcessingException : Error occurred while updating whitelist"

    }
    def "Update whitelist "(){
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("[{\"applicationName\":\"Heal Heal_Demo\",\"applicationSuppressionInterval\":0," +
                "\"serviceWhitelist\":[{\"serviceName\":\"CCService\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"UI Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"Event_Detector Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"A1_Event_Handler Service\",\"kpiWhitelist\":[\"CPU Util\",\"Memory Util\"]," +
                "\"serviceSuppressionInterval\":0}]},{\"applicationName\":\"apps2\",\"applicationSuppressionInterval\":0," +
                "\"serviceWhitelist\":[]}]")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal_health")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw")
        requestObject.setHeaders(headers)
        UtilityBean<Object> data = new ApplicationWhitelistBL().clientValidation(requestObject)

        when:
        new ApplicationWhitelistBL().updateWhitelist(data)
        then:
        noExceptionThrown()


    }
    def "Update whitelist error "(){
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("[]")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw")
        requestObject.setHeaders(headers)
        UtilityBean<Object> data = new ApplicationWhitelistBL().clientValidation(requestObject)

        when:
         new ApplicationWhitelistBL().updateWhitelist(data)
        then:
        final DataProcessingException e=thrown()
        e.getMessage()== "DataProcessingException : Error occurred while updating whitelist"

    }

    def "delete method"(){
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw")
        requestObject.setHeaders(headers)

        UtilityBean<Object> data = new ApplicationWhitelistBL().clientValidation(requestObject)
        List<ApplicationWhitelist> whitelistentry= data.getPojoObject()

        when:
         new ApplicationWhitelistBL().deleteWhitelist(whitelistentry)

        then:
        final DataProcessingException e=thrown()
        e.getMessage()== "DataProcessingException : Error occurred while deleting application whitelist"

    }
    def "Success Case: delete method"(){
        setup:
        RequestObject requestObject = new RequestObject()

        requestObject.setBody("[{\"applicationName\":\"Heal Health Application\",\"applicationSuppressionInterval\":0,"+
                "\"serviceWhitelist\":[{\"serviceName\":\"CCService\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"UI Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"Event_Detector Service\",\"kpiWhitelist\":[],\"serviceSuppressionInterval\":0}," +
                "{\"serviceName\":\"A1_Event_Handler Service\",\"kpiWhitelist\":[\"CPU Util\",\"Memory Util\"]," +
                "\"serviceSuppressionInterval\":0}]},{\"applicationName\":\"apps2\",\"applicationSuppressionInterval\":0," +
                "\"serviceWhitelist\":[]}]")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "heal")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw")
        requestObject.setHeaders(headers)

        UtilityBean<Object> data = new ApplicationWhitelistBL().clientValidation(requestObject)
        List<ApplicationWhitelist> whitelistentry= data.getPojoObject()

        when:
        new ApplicationWhitelistBL().deleteWhitelist(whitelistentry)

        then:
        noExceptionThrown()
    }

}