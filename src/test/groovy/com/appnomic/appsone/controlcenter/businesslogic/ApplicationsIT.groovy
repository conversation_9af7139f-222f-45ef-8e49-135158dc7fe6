package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.ApplicationBean
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService
import com.appnomic.appsone.controlcenter.exceptions.RequestException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AddApplication
import com.appnomic.appsone.controlcenter.pojo.Controller
import com.appnomic.appsone.controlcenter.pojo.EditController
import com.appnomic.appsone.controlcenter.pojo.TimezoneDetail
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.businesslogic.Applications.addServerValidations
import static com.appnomic.appsone.controlcenter.businesslogic.Applications.edit
import static com.appnomic.appsone.controlcenter.businesslogic.Applications.getServerValidations

class ApplicationsIT extends Specification {

    private static final String DEFAULT_TIMEZONE = "(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi"


    private static AddApplication getApplicationPojo(){
        List<String> services = new ArrayList<>()
        services.add("WebLogic-Service")
        services.add("Oracle-DB-Service")
        return AddApplication.builder()
                .name("app_test")
                .accountIdentifier("d681ef13-d690-4917-jkhg-6c79b-1")
                .timezone(DEFAULT_TIMEZONE)
                .services(services)
                .build()
    }

    private static ApplicationBean getApplicationBean(){
        List<Controller> services =new ArrayList<>()
        Controller service = new Controller()
        service.setIdentifier("Oracle-DB-Service")
        service.setAppId("4")
        TimezoneDetail timezone = new TimezoneDetail()
        timezone.setTimeZoneId("(GMT+09:00) Seoul")
        timezone.setOffset(********)
        return ApplicationBean.builder()
                .name("app_test")
                .identifier("app_test_identifier")
                .timezone(timezone)
                .addServices(services)
                .accountId(2)
                .userId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .build()
    }


    def "getServerValidations failure invalid accountIdentifier"() {
        setup :
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)

        when :
        getServerValidations("d681ef13-d-4917-jkhg-6c79b-1")

        then :
        thrown(RequestException)
    }

    def "add server validations invalid identifier "()
    {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        AddApplication app = getApplicationPojo()

        when:
        app.setIdentifier("Oracle-DB-Service")
        addServerValidations(app)

        then:
        thrown(RequestException)
    }

    def "add server validations invalid name "()
    {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        AddApplication app = getApplicationPojo()

        when:
        app.setName("enetbanking_1")
        addServerValidations(app)

        then:
        thrown(RequestException)
    }


    def "add server validations invalid services "()
    {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        AddApplication app = getApplicationPojo()

        when:
        List<String> services = new ArrayList<>()
        services.add("guihklj.o")
        services.add("yhiohoio")
        app.setServices(services)
        addServerValidations(app)

        then:
        thrown(RequestException)
    }

    def "add server validations invalid timezone "()
    {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        AddApplication app = getApplicationPojo()

        when:
        app.setTimezone("india")
        addServerValidations(app)

        then:
        thrown(RequestException)
    }

    def "Edit server validation success edit application"() {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        ApplicationBean bean = getApplicationBean()
        List<Controller> conServ = new ArrayList<>()
        Controller service = new Controller()
        service.setIdentifier("69f71544-4cbb-4d6b-8285-78c2882035d8")
        service.setAppId("8")
        conServ.add(service)
        List<Integer>deleteIds = new ArrayList<>()
        deleteIds.add(33)
        deleteIds.add(6)

        when:
        bean.setName("Test-App13")
        bean.setId(7)
        bean.setIdentifier("245a8d1c-e7b5-434a-9e33-127a81e28050")
        bean.setAddServices(null)
        bean.setAddServices(conServ)
        bean.setDeleteServiceIds(deleteIds)
        bean.setTimezone(null)
        int appId = edit(bean)

        then:
        List<Controller> list = MasterDataService.getControllerList(2)
        list.parallelStream()
                .anyMatch({c -> (c.appId.equals(Integer.toString(appId)) && (c.name.equals("Test-App13")))})

    }

    def "Edit server validation success edit service name"() {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        ApplicationBean bean = getApplicationBean()

        when:
        bean.setIdentifier(null)
        bean.setName("Test-Serv1")
        bean.setAddServices(new ArrayList<Controller>(0))
        bean.setDeleteServiceIds(new ArrayList<Integer>(0))
        bean.setId(8)
        bean.setAccountId(2)
        bean.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")

        int appId = edit(bean)

        then:
        List<Controller> list = MasterDataService.getControllerList(2)
        list.parallelStream()
                .anyMatch({c -> (c.appId.equals(Integer.toString(appId)) && (c.name.equals("Test-Serv1")))})
    }


}
