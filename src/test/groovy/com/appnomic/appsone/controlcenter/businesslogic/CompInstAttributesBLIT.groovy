package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.CompInstAttributesBean
import com.appnomic.appsone.controlcenter.beans.HierarchyBean
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.CompInstAttributesPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spock.lang.Shared
import spock.lang.Specification

class CompInstAttributesBLIT extends Specification{
    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }


    def cleanupSpec() {
        DBTestCache.rollback()
    }

    String authToken = KeycloakConnectionManager.getAccessToken();

    def "client validation serviceId :empty"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        CompInstanceAttributesBL compInstanceAttributesBL = new CompInstanceAttributesBL()
        compInstanceAttributesBL.clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Instance Id should not be empty."
    }

    def "client validation accountId :empty"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        CompInstanceAttributesBL compInstanceAttributesBL = new CompInstanceAttributesBL()
        compInstanceAttributesBL.clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }

    def "instance id empty check"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","2")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        CompInstanceAttributesBL compInstanceAttributesBL = new CompInstanceAttributesBL()
        when:
        UtilityBean<HierarchyBean> beanUtilityBean = compInstanceAttributesBL.clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Instance Id should not be empty."
    }

    def "valid instance attributes check "() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","8")
        params.put(":instanceId","53")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization",authToken)
        requestObject.setHeaders(headers)

        CompInstanceAttributesBL compInstanceAttributesBL = new CompInstanceAttributesBL()
        UtilityBean<HierarchyBean> beanUtilityBean = compInstanceAttributesBL.clientValidation(requestObject)
        List<CompInstAttributesBean> compInstAttributesBeans = compInstanceAttributesBL.serverValidation(beanUtilityBean)
        when:
        List<CompInstAttributesPojo> comptInstancePojoList = compInstanceAttributesBL.process(compInstAttributesBeans)
        then:
        comptInstancePojoList.size() > 0
    }

    def "invalid instance id check for service and account"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":instanceId","999")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", authToken)
        requestObject.setHeaders(headers)

        CompInstanceAttributesBL compInstanceAttributesBL = new CompInstanceAttributesBL()
        UtilityBean<HierarchyBean> beanUtilityBean = compInstanceAttributesBL.clientValidation(requestObject)
        when:
        List<CompInstAttributesBean> compInstAttributesBeans = compInstanceAttributesBL.serverValidation(beanUtilityBean)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : Invalid instance id provided."
    }

}
