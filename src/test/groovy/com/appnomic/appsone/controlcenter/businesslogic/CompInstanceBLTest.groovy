package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.HierarchyBean
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.ComptInstancePojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Shared
import spock.lang.Specification

class CompInstanceBLTest extends Specification {

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;" +
                "INIT=RUNSCRIPT FROM './src/test/resources/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/views.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }


    def "client validation serviceId :empty"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-12")
        requestObject.setParams(params)
        when:
        CompInstanceBL compInstanceBL = new CompInstanceBL()
        compInstanceBL.clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Service Id should not be empty or null"
    }

    def "client validation accountId :empty"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        requestObject.setParams(params)
        when:
        CompInstanceBL compInstanceBL = new CompInstanceBL()
        compInstanceBL.clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }

    def "server validation invalid serviceId"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","2")
        requestObject.setParams(params)
        CompInstanceBL compInstanceBL = new CompInstanceBL()
        UtilityBean<HierarchyBean> beanUtilityBean = compInstanceBL.clientValidation(requestObject)
        when:
        compInstanceBL.serverValidation(beanUtilityBean)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : The service id provided is invalid in this case as it does not exist or is not mapped to the concerned account"
    }

    def "server validation invalid account Identifier"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(":serviceId","2")
        requestObject.setParams(params)
        CompInstanceBL compInstanceBL = new CompInstanceBL()
        UtilityBean<HierarchyBean> beanUtilityBean = compInstanceBL.clientValidation(requestObject)
        when:
        compInstanceBL.serverValidation(beanUtilityBean)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : Invalid Account Identifier."
    }

    def "server validation for service having only component instances"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","8")
        requestObject.setParams(params)
        CompInstanceBL compInstanceBL = new CompInstanceBL()
        UtilityBean<HierarchyBean> beanUtilityBean = compInstanceBL.clientValidation(requestObject)
        HierarchyBean bean = compInstanceBL.serverValidation(beanUtilityBean)
        when:
        List<ComptInstancePojo> comptInstancePojoList = compInstanceBL.process(bean)
        then:
        comptInstancePojoList.size() == 1
        comptInstancePojoList.get(0).getInstanceId() == 86
        comptInstancePojoList.get(0).getInstanceName() == 'NB_Oracle_DB_169_Inst_1'
        comptInstancePojoList.get(0).getHostId() == 85
        comptInstancePojoList.get(0).getHostName() == 'RHEL_Oracle_DB_Host_169_Inst_1'
    }

    def "server validation for service having only host component instances"() {
        given:
        RequestObject requestObject = new RequestObject(null)
        HashMap<String,String> params = new HashMap<>()
        params.put(":identifier","d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":serviceId","3")
        requestObject.setParams(params)
        CompInstanceBL compInstanceBL = new CompInstanceBL()
        UtilityBean<HierarchyBean> beanUtilityBean = compInstanceBL.clientValidation(requestObject)
        HierarchyBean bean = compInstanceBL.serverValidation(beanUtilityBean)
        when:
        List<ComptInstancePojo> comptInstancePojoList = compInstanceBL.process(bean)
        then:
        comptInstancePojoList.size() == 1
        comptInstancePojoList.get(0).getInstanceId() == 0
        comptInstancePojoList.get(0).getInstanceName() == null
        comptInstancePojoList.get(0).getHostId() == 85
        comptInstancePojoList.get(0).getHostName() == 'RHEL_Oracle_DB_Host_169_Inst_1'
    }
}
