package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.exceptions.CommandException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class CompletedCommandTest extends Specification {

    class DummyRequest extends Request {
        boolean nullCheck = true
        String body

        @Override
        String body() {
            if(body != null) {
                return body
            }

            if(nullCheck) {
                return null
            } else {
                return " "
            }
        }
    }

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def "CompletedCommand client validation invalid request body"() {

        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        CompletedCommand completedCommand = new CompletedCommand()

        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-5"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hX14Ij8HZvWKuJT2T1tlFs_YJjduh-qLKb6O_5cjC72FlWMErfiW-5yc4bWLTqu_sMIkkZCrOeodhjEq7gaeavDeO0GoMc76hr7cuuszFxDY76ayTF0GLfSkW3rEtxlt0C--W0Q3ySCqTUOhBkcELuWRmoJSGUwSIyLQ9aGq9C1Hkqvkoy5tUhrrz4rwZCWwj3nV7CWd0V_3NNVNsXNyK7yXxVjLoI7EaJhwxmHIfcvsiocMmj04AxDR34k3xd4QqqXID7Bgd9wOOKtyfM6-zFPPwsPxK5WlJtQvw3wyXOqJ0OYDJn4zd_E0o08L-F0c1wFJfTke-yaF8SM30KPEkg"
        request.body() >> """{
                "commandIdentifier": "fdb4c5ba-e441-463c-ab2a-7ab28027164e",
                "agentIdentifier"  : "Test-Agent-1",
                "agentCurrentState": "Running"
               }"""

        when:
        completedCommand.clientValidation(request.body())

        then:
        final CommandException exception = thrown()
        exception.getMessage() == """CommandException : CompletedCommandBean validation failure. For more details, refer to application log file."""

    }

    def "CompletedCommand client validation invalid parameter"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        CompletedCommand completedCommand = new CompletedCommand()

        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-5"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hX14Ij8HZvWKuJT2T1tlFs_YJjduh-qLKb6O_5cjC72FlWMErfiW-5yc4bWLTqu_sMIkkZCrOeodhjEq7gaeavDeO0GoMc76hr7cuuszFxDY76ayTF0GLfSkW3rEtxlt0C--W0Q3ySCqTUOhBkcELuWRmoJSGUwSIyLQ9aGq9C1Hkqvkoy5tUhrrz4rwZCWwj3nV7CWd0V_3NNVNsXNyK7yXxVjLoI7EaJhwxmHIfcvsiocMmj04AxDR34k3xd4QqqXID7Bgd9wOOKtyfM6-zFPPwsPxK5WlJtQvw3wyXOqJ0OYDJn4zd_E0o08L-F0c1wFJfTke-yaF8SM30KPEkg"
        request.body() >> """{
                "commandIdentifier": "fdb4c5ba-e441-463c-ab2a-7ab28027164e",
                "commandJobId"     : null,
                "agentIdentifier"  : "Test-Agent-1",
                "agentCurrentState": "Running"
               }"""

        when:
        completedCommand.clientValidation(request.body())

        then:
        final CommandException exception = thrown()
        exception.getMessage() == """CommandException : CompletedCommandBean validation failure. For more details, refer to application log file."""
    }

    def "CompletedCommand client validation successful"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        CompletedCommand completedCommand = new CompletedCommand()

        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-5"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hX14Ij8HZvWKuJT2T1tlFs_YJjduh-qLKb6O_5cjC72FlWMErfiW-5yc4bWLTqu_sMIkkZCrOeodhjEq7gaeavDeO0GoMc76hr7cuuszFxDY76ayTF0GLfSkW3rEtxlt0C--W0Q3ySCqTUOhBkcELuWRmoJSGUwSIyLQ9aGq9C1Hkqvkoy5tUhrrz4rwZCWwj3nV7CWd0V_3NNVNsXNyK7yXxVjLoI7EaJhwxmHIfcvsiocMmj04AxDR34k3xd4QqqXID7Bgd9wOOKtyfM6-zFPPwsPxK5WlJtQvw3wyXOqJ0OYDJn4zd_E0o08L-F0c1wFJfTke-yaF8SM30KPEkg"
        request.body() >> """{
                "commandIdentifier": "fdb4c5ba-e441-463c-ab2a-7ab28027164e",
                "commandJobId"     : "123",
                "agentIdentifier"  : "Test-Agent-1",
                "agentCurrentState": "Running"
               }"""

        when:
       def completedCommandRequest = completedCommand.clientValidation(request.body())

        then:
        notThrown()
        completedCommandRequest.toString() == """CompletedCommandBean(commandIdentifier=fdb4c5ba-e441-463c-ab2a-7ab28027164e, commandJobId=123, agentIdentifier=Test-Agent-1, agentCurrentState=Running)"""
    }

    def "CompletedCommand server invalid commandIdentifier"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        CompletedCommand completedCommand = new CompletedCommand()

        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hX14Ij8HZvWKuJT2T1tlFs_YJjduh-qLKb6O_5cjC72FlWMErfiW-5yc4bWLTqu_sMIkkZCrOeodhjEq7gaeavDeO0GoMc76hr7cuuszFxDY76ayTF0GLfSkW3rEtxlt0C--W0Q3ySCqTUOhBkcELuWRmoJSGUwSIyLQ9aGq9C1Hkqvkoy5tUhrrz4rwZCWwj3nV7CWd0V_3NNVNsXNyK7yXxVjLoI7EaJhwxmHIfcvsiocMmj04AxDR34k3xd4QqqXID7Bgd9wOOKtyfM6-zFPPwsPxK5WlJtQvw3wyXOqJ0OYDJn4zd_E0o08L-F0c1wFJfTke-yaF8SM30KPEkg"
        request.body() >> """{
                "commandIdentifier": "Test-Command-1",
                "commandJobId"     : "123",
                "agentIdentifier"  : "A1_PSAgent",
                "agentCurrentState": "Running"
               }"""
        def completedCommandRequest = completedCommand.clientValidation(request.body())

        when:
        completedCommand.serverValidation(completedCommandRequest)

        then:
        final CommandException exception = thrown()
        exception.getMessage() == """CommandException : Command with commandIdentifier Test-Command-1 is unavailable"""
    }

    def "CompletedCommand server invalid agentIdentifier"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Thread.sleep(20000)
        CompletedCommand completedCommand = new CompletedCommand()

        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hX14Ij8HZvWKuJT2T1tlFs_YJjduh-qLKb6O_5cjC72FlWMErfiW-5yc4bWLTqu_sMIkkZCrOeodhjEq7gaeavDeO0GoMc76hr7cuuszFxDY76ayTF0GLfSkW3rEtxlt0C--W0Q3ySCqTUOhBkcELuWRmoJSGUwSIyLQ9aGq9C1Hkqvkoy5tUhrrz4rwZCWwj3nV7CWd0V_3NNVNsXNyK7yXxVjLoI7EaJhwxmHIfcvsiocMmj04AxDR34k3xd4QqqXID7Bgd9wOOKtyfM6-zFPPwsPxK5WlJtQvw3wyXOqJ0OYDJn4zd_E0o08L-F0c1wFJfTke-yaF8SM30KPEkg"
        request.body() >> """{
                "commandIdentifier": "FetchDiskIO",
                "commandJobId"     : "123",
                "agentIdentifier"  : "Test-Agent-2",
                "agentCurrentState": "Running"
               }"""
        def completeCommandRequest = completedCommand.clientValidation(request.body())

        when:
        completedCommand.serverValidation(completeCommandRequest)
        Thread.sleep(20000)

        then:
        final CommandException exception = thrown()
        exception.getMessage() == """CommandException : Agent with agentIdentifier Test-Agent-2 is unavailable"""
    }

    /*def "CompletedCommand server invalid agent account mapping"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Thread.sleep(20000)
        CompletedCommand completedCommand = new CompletedCommand()

        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hX14Ij8HZvWKuJT2T1tlFs_YJjduh-qLKb6O_5cjC72FlWMErfiW-5yc4bWLTqu_sMIkkZCrOeodhjEq7gaeavDeO0GoMc76hr7cuuszFxDY76ayTF0GLfSkW3rEtxlt0C--W0Q3ySCqTUOhBkcELuWRmoJSGUwSIyLQ9aGq9C1Hkqvkoy5tUhrrz4rwZCWwj3nV7CWd0V_3NNVNsXNyK7yXxVjLoI7EaJhwxmHIfcvsiocMmj04AxDR34k3xd4QqqXID7Bgd9wOOKtyfM6-zFPPwsPxK5WlJtQvw3wyXOqJ0OYDJn4zd_E0o08L-F0c1wFJfTke-yaF8SM30KPEkg"
        request.body() >> """{
                "commandIdentifier": "FetchDiskIO",
                "commandJobId"     : "123",
                "agentIdentifier"  : "Test-Agent-1",
                "agentCurrentState": "Running"
               }"""
        def completeCommandRequest = completedCommand.clientValidation(request.body())

        when:
        completedCommand.serverValidation(completeCommandRequest)

        then:
        final CommandException exception =  thrown()
        exception.getMessage() == """CommandException : Agent account mapping not available for account id 1 and agent id 10"""
    }*/
}
