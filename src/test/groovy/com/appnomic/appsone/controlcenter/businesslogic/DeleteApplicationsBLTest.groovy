package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class DeleteApplicationsBLTest extends Specification{

    String[] t1 = ["2e615f5b-29a9-419a-a4e6-88edb463eff0,deleteApITestApp1"]
    String[] t2 = ["2e615f5b-29a9-419a-a4e6-88edb463eff0,deleteApITestApp1,deleteApITestApp1"]
    String[] t3 = null
    String[] t4 = []
    String[] t5 = [""]
    String[] t6 = [null]
    String[] t7 = ["deleteApITestApp1"]
    String[] t8 = ["2e615f5b-29a9-419a-a4e6-88edb463eff0, "]


    def "Client validation failure - NULL request object"() {
        when:
        new DeleteApplicationsBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Null query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t3)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : appIdentifier should not be empty or null in the query parameter."
    }

    def "Client validation failure - Empty query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t4)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : appIdentifier should not be empty or null in the query parameter."
    }

    def "Client validation failure - Empty string value in query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t5)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : appIdentifier should not be empty or null in the query parameter."
    }

    def "Client validation failure - Null value in query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t6)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : appIdentifier should not be empty or null in the query parameter."
    }

    def "Client validation failure - one Empty value in multiple application in query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t8)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Found empty/null Application(s) name in request parameter."
    }

    def "Success case - With Single Application"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t7)
        requestObject.setQueryParams(queryMapData)

        when:
        UtilityBean<List<String>> data = new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 1
        data.getPojoObject().get(0) == "deleteApITestApp1"

    }

    def "Success case - With Multiple Application"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        UtilityBean<List<String>> data = new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0) == "2e615f5b-29a9-419a-a4e6-88edb463eff0"
        data.getPojoObject().get(1) == "deleteApITestApp1"

    }

    def "Success case - With Multiple Application having duplicates"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t2)
        requestObject.setQueryParams(queryMapData)

        when:
        UtilityBean<List<String>> data = new DeleteApplicationsBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0) == "2e615f5b-29a9-419a-a4e6-88edb463eff0"
        data.getPojoObject().get(1) == "deleteApITestApp1"

    }

}
