package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.ConnectionDetailsPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class DeleteConnectionBLTest extends Specification {

    String[] t1 = ["NB-Web-Service|NB-DB-Service,NB-User|LOS-User"]
    String[] t2 = ["NB-W|b-Service|NB-DB-Service,NB-User|LOS-User"]
    String[] t3 = [" |NB-DB-Service,NB-User|LOS-User"]
    String[] t4 = ["NB-Web-Service|NB-DB-Service,NB-User| "]
    String[] t5 = ["NB-Web-Service|NB-DB-Service,NB-User|NB-User"]
    String[] t6 = ["NB-Web-Service|NB-DB-Service"]
    String[] t7 = ["NB-Web-Service|NB-DB-Service,NB-Web-Service|NB-DB-Service,NB-User|LOS-User,NB-Web-Service|NB-DB-Service,NB-User|LOS-User"]
    String[] t8 = null
    String[] t9 = []
    String[] t10 = [""]
    String[] t11 = [null]

    def "Client validation failure - NULL request object"() {
        when:
        new DeleteConnectionBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Empty authorization key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t1)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Null query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t8)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Data should not be empty or null in the query parameter."
    }

    def "Client validation failure - Empty query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t9)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Data should not be empty or null in the query parameter."
    }

    def "Client validation failure - Empty string value in query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t10)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Data should not be empty or null in the query parameter."
    }

    def "Client validation failure - Null value in query parameters"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t11)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Data should not be empty or null in the query parameter."
    }

    def "Client validation failure - Invalid character present in query parameter 'serviceIdentifier' part"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")


        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t2)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid query parameter(s) detected. '|' not allowed in the service identifiers."
    }

    def "Client validation failure - Empty value of query parameter 'sourceServiceIdentifier' part"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t3)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Client validation failure - Empty value of query parameter 'destinationServiceIdentifier' part"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t4)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Client validation failure - same value of query parameter 'sourceServiceIdentifier' & 'destinationServiceIdentifier' key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t5)
        requestObject.setQueryParams(queryMapData)

        when:
        new DeleteConnectionBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_SERVICE_IDENTIFIER_FOR_CONNECTION
    }

    def "Success case - With Single pair in query parameter"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t6)
        requestObject.setQueryParams(queryMapData)


        when:
        UtilityBean<List<ConnectionDetailsPojo>> data = new DeleteConnectionBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 1
        data.getPojoObject().get(0).getSourceServiceIdentifier() == "NB-Web-Service"
        data.getPojoObject().get(0).getDestinationServiceIdentifier() == "NB-DB-Service"
    }

    def "Success Case - With Multiple pair in query parameter"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t1)
        requestObject.setQueryParams(queryMapData)


        when:
        UtilityBean<List<ConnectionDetailsPojo>> data = new DeleteConnectionBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0).getSourceServiceIdentifier() == "NB-Web-Service"
        data.getPojoObject().get(1).getDestinationServiceIdentifier() == "LOS-User"
    }

    def "Success Case - With Multiple Duplicate pair in query parameter"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("data", t7)
        requestObject.setQueryParams(queryMapData)


        when:
        UtilityBean<List<ConnectionDetailsPojo>> data = new DeleteConnectionBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0).getSourceServiceIdentifier() == "NB-Web-Service"
        data.getPojoObject().get(1).getDestinationServiceIdentifier() == "LOS-User"
    }

}
