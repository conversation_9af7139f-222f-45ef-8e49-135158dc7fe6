package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spock.lang.Specification

class DeleteTagTest extends Specification {

    def "deleteTag valid input"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(20000)

        when:
        TagsDataService.deleteTags(7, 2, "Type", "controller", 2, "7640123a-fbde-4fe5-9812-581cd1e3a9c1",null)
        then:
        noExceptionThrown()
    }

}
