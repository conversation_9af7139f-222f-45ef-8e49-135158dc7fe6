package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GetEmailBLTest extends Specification {
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return "eyJhbGciOiJSUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.eyJqdGkiOiI5MzU2Y2NiZC03YmNjLTRkMjktYjY5MS00ZmY3ZjNmZjIyNzUiLCJleHAiOjE1NjIzMzA3ODAsIm5iZiI6MCwiaWF0IjoxNTYyMzI5ODgwLCJpc3MiOiJodHRwOi8vMTkyLjE2OC4xMy4xNjc6OTA4MC9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiI3NjQwMTIzYS1mYmRlLTRmZTUtOTgxMi01ODFjZDFlM2E5YzEiLCJ0eXAiOiJCZWFyZXIiLCJhenAiOiJhZG1pbi1jbGkiLCJhdXRoX3RpbWUiOjAsInNlc3Npb25fc3RhdGUiOiJhOTFmNjYyMC1iYmU5LTQ5ZmUtYTZiYy0wNjIzNjc3YzU0YWIiLCJhY3IiOiIxIiwic2NvcGUiOiJwcm9maWxlIGVtYWlsIiwiZW1haWxfdmVyaWZpZWQiOmZhbHNlLCJwcmVmZXJyZWRfdXNlcm5hbWUiOiJhcHBzb25lYWRtaW4ifQ.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    def "client validation failure-all error"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"\",\n" +
                "      \"port\": 0,\n" +
                "      \"username\": \"\",\n" +
                "      \"security\": \"\",\n" +
                "      \"password\": \"\",\n" +
                "      \"fromRecipient\": \"\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : {SMTP address=Address is either NULL, empty or its length is greater than 256 characters, SMTP port=Port is invalid. It should be an integer greater than 0, SMTP security type=SMTP security type is either NULL or empty. It should be one of TLS, SSL or NONE, SMTP recipient=SMTP from recipient is either NULL, empty or its length is more than 256 characters.}"

    }

    def "client validation failure:Invalid Account identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"port\": 9090,\n" +
                "      \"username\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "client validation failure:Invalid json"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"ports\": 9090,\n" +
                "      \"usernames\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid JSON."
    }
    def "client validation invalid auth token"() {
        given:

        request.headers() >> new HashSet<>()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"port\": 9090,\n" +
                "      \"username\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
    def "client validation success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"port\": 9090,\n" +
                "      \"username\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateEmailBL().clientValidation(RequestObject)
        then:
        noExceptionThrown()
    }

    def "Update Email: client validation failure-all error"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"\",\n" +
                "      \"port\": 0,\n" +
                "      \"username\": \"\",\n" +
                "      \"security\": \"\",\n" +
                "      \"password\": \"\",\n" +
                "      \"fromRecipient\": \"\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : {SMTP address=Address is either NULL, empty or its length is greater than 256 characters, SMTP port=Port is invalid. It should be an integer greater than 0, SMTP security type=SMTP security type is either NULL or empty. It should be one of TLS, SSL or NONE, SMTP recipient=SMTP from recipient is either NULL, empty or its length is more than 256 characters.}"

    }

    def "Update Email: client validation failure:Invalid Account identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"port\": 9090,\n" +
                "      \"username\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "Update Email: client validation failure:Invalid json"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"ports\": 9090,\n" +
                "      \"usernames\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid JSON."
    }
    def "Update Email: client validation invalid auth token"() {
        given:
        request.headers() >> new HashSet<>()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"port\": 9090,\n" +
                "      \"username\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
    def "Update Email: client validation success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"port\": 9090,\n" +
                "      \"username\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateEmailBL().clientValidation(RequestObject)
        then:
        noExceptionThrown()
    }
    def "Get Email: client validation invalid auth token"() {
        given:
        request.headers() >> new HashSet<>()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new GetEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
    def "Get Email: client validation failure:Invalid Account identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new GetEmailBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "Get Email: server validation invalid auth token"() {
        given:
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "      \"address\": \"www.appnomic.com\",\n" +
                "      \"port\": 9090,\n" +
                "      \"username\": \"<EMAIL>\",\n" +
                "      \"security\": \"NONE\",\n" +
                "      \"password\": \"appnomic_1\",\n" +
                "      \"fromRecipient\": \"<EMAIL>\"\n" +
                "}"
        when:
        def utility = UtilityBean.<Integer>builder().accountIdentifier("d681ef13-d690-4917-jkhg-6c79b-1").authToken("**********").build()
        new GetEmailBL().serverValidation(utility)
        then:
        final e = thrown(ServerException)
        e.getMessage() == "ServerException : Invalid Authorization Token."
    }
}
