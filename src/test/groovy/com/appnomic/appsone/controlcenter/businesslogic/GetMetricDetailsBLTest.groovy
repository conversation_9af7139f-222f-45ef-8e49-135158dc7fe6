package com.appnomic.appsone.controlcenter.businesslogic


import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.MetricDetailsRequest
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.util.CommonUtils
import com.fasterxml.jackson.core.type.TypeReference
import spock.lang.Specification

class GetMetricDetailsBLTest extends Specification {

    def "Auth Token Checking for NULL value"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)
        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid Authorization Token."

    }

    def "Auth Token Checking empty value"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", " ")
        requestObject.setHeaders(headers)
        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid Authorization Token."

    }

    def "Empty Account identifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put("identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)

        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account identifier is null or empty."
    }

    def "NULL Account Identifier"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account identifier is null or empty."
    }

    def "Service id is null or empty"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)


        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:

        final ClientException e = thrown()
        e.getMessage() == "ClientException : serviceId is null or empty."

    }

    def "Service id is not integer"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", "serviceid")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)


        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:

        final ClientException e = thrown()
        e.getMessage() == "ClientException : Service Id is not an integer."

    }

    def "empty request body"() {
        setup:

        RequestObject requestObject = new RequestObject()
        String body = null;

        requestObject.setBody(body)

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", "123")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)

        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : InstanceIds are not specified in the request body."

    }

    def "Instances Ids are not specified"() {
        setup:
        MetricDetailsRequest metricDetailsRequest;
        RequestObject requestObject = new RequestObject()
        List<Integer> instanceIds = new ArrayList<>()
        requestObject.setBody()

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", "123")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)

        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : InstanceIds are not specified in the request body."


    }

    def "Instances Ids are not specified proper Format"() {
        setup:
        MetricDetailsRequest metricDetailsRequest;
        RequestObject requestObject = new RequestObject()
        List<Integer> instanceIds = new ArrayList<>()

        requestObject.setBody("{\"instanceIds\":[1,2,'3']}")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", "123")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)

        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : InstanceIds are not specified in proper format in the request body."


    }

    def "InstanceId null or empty"() {
        setup:
        MetricDetailsRequest metricDetailsRequest;

        RequestObject requestObject = new RequestObject()
        List<Integer> instanceIds = new ArrayList<>()


        requestObject.setBody("{\"instanceIds\":null}")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", "123")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)

        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : InstanceIds is null or empty."
    }

    def "less than one instanceId"() {
        setup:
        MetricDetailsRequest metricDetailsRequest;

        RequestObject requestObject = new RequestObject()
        List<Integer> instanceIds = new ArrayList<>()


        requestObject.setBody("{\"instanceIds\":[-1]}")

        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", "123")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)

        when:
        new GetMetricDetails().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : InstanceIds cannot be less than 1."
    }

    def "Success Case with null value"() {
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)
        requestObject.setBody("{\"instanceIds\":null}")
        MetricDetailsRequest metricDetailsRequest = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(),
                new TypeReference<MetricDetailsRequest>() {
                })
        List<Integer> instanceIds = metricDetailsRequest.getInstanceIds();


        when:
        UtilityBean<Object> data = new GetHostBL().clientValidation(requestObject)

        then:
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getServiceId() == null
        data.getPojoObject() == instanceIds
        data.getAuthToken() == "testing-with-dummy-authorization-header"

    }

    def "Success Case"() {
        setup:
        MetricDetailsRequest metricDetailsRequest;

        RequestObject requestObject = new RequestObject()

        requestObject.setBody("{\"instanceIds\":[1,2,3]}")
        metricDetailsRequest = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(requestObject.getBody(),
                new TypeReference<MetricDetailsRequest>() {
                })
        List<Integer> instanceIds = metricDetailsRequest.getInstanceIds();


        Map<String, String> params = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        params.put(":serviceId", "123")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-identifier-using-dummy-token")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<Object> data = new GetMetricDetails().clientValidation(requestObject)


        then:
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getServiceId() == "123"
        data.getPojoObject() == instanceIds
        data.getAuthToken() == "testing-identifier-using-dummy-token"

    }
}