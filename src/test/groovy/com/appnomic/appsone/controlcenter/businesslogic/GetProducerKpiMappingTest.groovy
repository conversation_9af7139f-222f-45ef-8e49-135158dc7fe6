package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.pojo.KPIComponentPojo
import com.appnomic.appsone.controlcenter.pojo.ProducerPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

import java.sql.Timestamp

class GetProducerKpiMappingTest extends Specification{

    def "Client validation failure - NULL request object"() {
        when:
        new GetProducerKpiMapping().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetProducerKpiMapping().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetProducerKpiMapping().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }

    def "Client validation failure - Empty authorization token"(){
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new GetProducerKpiMapping().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Authorization Token is empty."
    }

    def "Client validation failure - NULL authorization token"(){
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new GetProducerKpiMapping().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Authorization Token is empty."
    }

    def "Client validation failure - ProducerId less than 1"() {
        setup:
        RequestObject requestObject = new RequestObject(null)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":producerid", "-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetProducerKpiMapping().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : ProducerId should be a positive non-zero integer."
    }

    def "Client validation failure - Provided a non-integer value ProducerId"() {
        setup:
        RequestObject requestObject = new RequestObject(null)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":producerid", "abc")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new GetProducerKpiMapping().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : ProducerId should be positive integer."
    }

    def "Client validation Success"() {
        setup:
        RequestObject requestObject = new RequestObject(null)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        params.put(":producerid", "2")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<Integer> data= new GetProducerKpiMapping().clientValidation(requestObject)

        then:
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getPojoObject() == 2
    }

    def "Server Validation failure - Invalid AuthToken"(){
        setup:
        UtilityBean<Integer> data = UtilityBean.builder()
        .authToken("testing-with-dummy-authorization-header")
        .accountIdentifier("heal_health")
        .pojoObject(2)
        .build()
        GetProducerKpiMapping getProducerKpiMapping= new GetProducerKpiMapping()

        when:
        getProducerKpiMapping.serverValidation(data)

        then:
        final ServerException e = thrown()
    }

    def "Server Validation failure - Invalid AccountIdentifier"() {
        setup:
        UtilityBean<Integer> data = UtilityBean.builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CBE4HwzzDHqp6P_ZjWw9QFNP8-iok2hWHyfAZur9Vi3Tb3CN_2l-b--Ypom91myJCpUoRgs_F5MxY_GSUxIEgRn9HKykxqiDRXmH0amrZK2xzz5H8oB-Ot7QGyBWhD9WolPQcdMR_3QFeJdZvOlhEum4Apyrkkxn3jPD7-XRrOv6jPJP4Xl384e3oOku5EdtD7WsKTrbJjvU622WgShUeJKZM_H3vuTwVdYYUdecAhSzarutqYRcPYH98DV-CdX9laTcjYYNXX7PquGohqYNPNhkoOfyf6xbcSafdeCC6PVrfU96tcCYQ7fYYqsYF9-5fBBw8nFjFBFqSRQSoyXXiA")
                .accountIdentifier("12")
                .pojoObject(2)
                .build()

        GetProducerKpiMapping getProducerKpiMapping= new GetProducerKpiMapping()

        when:
        getProducerKpiMapping.serverValidation(data)

        then:
        final ServerException e = thrown()
    }

    def "Server Validation - invalid producerId"(){
        setup:
        UtilityBean<Integer> data = UtilityBean.builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CBE4HwzzDHqp6P_ZjWw9QFNP8-iok2hWHyfAZur9Vi3Tb3CN_2l-b--Ypom91myJCpUoRgs_F5MxY_GSUxIEgRn9HKykxqiDRXmH0amrZK2xzz5H8oB-Ot7QGyBWhD9WolPQcdMR_3QFeJdZvOlhEum4Apyrkkxn3jPD7-XRrOv6jPJP4Xl384e3oOku5EdtD7WsKTrbJjvU622WgShUeJKZM_H3vuTwVdYYUdecAhSzarutqYRcPYH98DV-CdX9laTcjYYNXX7PquGohqYNPNhkoOfyf6xbcSafdeCC6PVrfU96tcCYQ7fYYqsYF9-5fBBw8nFjFBFqSRQSoyXXiA")
                .accountIdentifier("heal_health")
                .pojoObject(0)
                .build()
        GetProducerKpiMapping getProducerKpiMapping= new GetProducerKpiMapping()

        when:
        getProducerKpiMapping.serverValidation(data)

        then:
        final ServerException e = thrown()
    }

    def "Server Validation success"(){
        setup:
        UtilityBean<Integer> data = UtilityBean.builder()
                .authToken("eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CBE4HwzzDHqp6P_ZjWw9QFNP8-iok2hWHyfAZur9Vi3Tb3CN_2l-b--Ypom91myJCpUoRgs_F5MxY_GSUxIEgRn9HKykxqiDRXmH0amrZK2xzz5H8oB-Ot7QGyBWhD9WolPQcdMR_3QFeJdZvOlhEum4Apyrkkxn3jPD7-XRrOv6jPJP4Xl384e3oOku5EdtD7WsKTrbJjvU622WgShUeJKZM_H3vuTwVdYYUdecAhSzarutqYRcPYH98DV-CdX9laTcjYYNXX7PquGohqYNPNhkoOfyf6xbcSafdeCC6PVrfU96tcCYQ7fYYqsYF9-5fBBw8nFjFBFqSRQSoyXXiA")
                .accountIdentifier("heal_health")
                .pojoObject(2)
                .build()
        GetProducerKpiMapping getProducerKpiMapping= new GetProducerKpiMapping()

        when:
        ProducerPojo producerPojo = getProducerKpiMapping.serverValidation(data)

        then:
        producerPojo.getAccount().getId() == 2
        producerPojo.getAccount().getName() == "HealHealth"
        producerPojo.getAccount().getCreatedTime() == Timestamp.valueOf("2021-12-15 00:00:00.0")
        producerPojo.getAccount().getUpdatedTime() == Timestamp.valueOf("2021-12-15 00:00:00.0")
        producerPojo.getAccount().getUserIdDetails() == "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        producerPojo.getAccount().getStatus() == 1
        producerPojo.getAccount().getIdentifier() == "heal_health"
        producerPojo.getUserId() == "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        producerPojo.getProducerId() == 2
    }

    def "Process - No kpi mapped ProducerId passed"(){
        setup:
        AccountBean accountBean = new AccountBean()
        accountBean.setId(2)

        ProducerPojo producer= ProducerPojo.builder()
                .account(accountBean)
                .userId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .producerId(500)
                .build()
        GetProducerKpiMapping getProducerKpiMapping= new GetProducerKpiMapping()


        when:
        List<KPIComponentPojo> producerKpiMappingList = getProducerKpiMapping.process(producer)

        then:
        producerKpiMappingList == new ArrayList()
    }

    def "Process - success"(){
        setup:
        AccountBean accountBean = new AccountBean()
        accountBean.setId(2)

        ProducerPojo producer= ProducerPojo.builder()
                .account(accountBean)
                .userId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
                .producerId(2)
                .build()
        GetProducerKpiMapping getProducerKpiMapping= new GetProducerKpiMapping()

        when:
        List<KPIComponentPojo> producerKpiMappingList = getProducerKpiMapping.process(producer)

        then:
        producerKpiMappingList != new ArrayList()
    }
}
