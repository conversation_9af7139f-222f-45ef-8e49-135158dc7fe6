package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.RuleDetailsPojo
import com.appnomic.appsone.controlcenter.beans.UserAccountBean
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.ServerException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.util.ValidationUtils
import spock.lang.Shared
import spock.lang.Specification

class GetRulesDetailsBLTest extends Specification{
    GetRulesDetailsBL rulesDetailsBL = new GetRulesDetailsBL()

    @Shared
    AccountBean account = new AccountBean()
    @Shared
    UserAccountBean accountBean = new UserAccountBean()
    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    def setup(){
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        account.id = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.name= "INDIA"
        account.userIdDetails = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        accountBean.setAccount(account)
        accountBean.setUserId("1")
    }

    def cleanup(){
        DBTestCache.rollback()
    }

    def "Client validation without exception"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.SERVICE_ID.toLowerCase(), "4")
        params.put(":ruleId".toLowerCase(), "216")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("{\"addDiscoveryTags\":[\"Default2\",\"Default2\"],\"removeDiscoveryTags\":[\"XpT\",\"Default\"]}")

        when:

        UtilityBean<RuleDetailsPojo> utilityBean = rulesDetailsBL.clientValidation(requestObject)

        then:
        noExceptionThrown()
        utilityBean.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-12"
    }

    def "Client validation exception: Identifier"() {
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        params.put(Constants.SERVICE_ID.toLowerCase(), "4")
        params.put(":ruleId".toLowerCase(), "216")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("{\"addDiscoveryTags\":[\"Default2\",\"Default2\"],\"removeDiscoveryTags\":[\"XpT\",\"Default\"]}")

        when:
        rulesDetailsBL.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage() == "ClientException :: Invalid parameter received. Parameter name: :identifier, value: null."
    }

    def "Client validation exception: SERVICE_IDENTIFIER"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.SERVICE_ID.toLowerCase(), null)
        params.put(":ruleId".toLowerCase(), "216")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("{\"addDiscoveryTags\":[\"Default2\",\"Default2\"],\"removeDiscoveryTags\":[\"XpT\",\"Default\"]}")

        when:
        rulesDetailsBL.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage()== "ClientException :: Invalid parameter received. Parameter name: :serviceId, value: null."
    }

    def "Client validation exception: RULE_ID"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.SERVICE_ID.toLowerCase(), "4")
        params.put(":ruleId".toLowerCase(), null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("{\"addDiscoveryTags\":[\"Default2\",\"Default2\"],\"removeDiscoveryTags\":[\"XpT\",\"Default\"]}")

        when:
        rulesDetailsBL.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage()== "ClientException :: Invalid parameter received. Parameter name: :ruleId, value: null.Invalid parameter received. Parameter name: :ruleId, value: null."
    }

    def "Client validation exception: RULE_ID format"(){
        given:
        RequestObject requestObject = new RequestObject()
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")
        params.put(Constants.SERVICE_ID.toLowerCase(), "4")
        params.put(":ruleId".toLowerCase(), "a")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", AUTH_TOKEN)
        requestObject.setHeaders(headers)

        requestObject.setBody("{\"addDiscoveryTags\":[\"Default2\",\"Default2\"],\"removeDiscoveryTags\":[\"XpT\",\"Default\"]}")

        when:
        rulesDetailsBL.clientValidation(requestObject)

        then:
        final e = thrown(ClientException)
        e.getSimpleMessage()== "ClientException :: Invalid parameter received. Parameter name: :ruleId, value: a."
    }

    def "Server validation without exception"() {
        setup:
        RuleDetailsPojo ruleDetailsPojo = new RuleDetailsPojo()
        ruleDetailsPojo.setId(216)
        UtilityBean<RuleDetailsPojo> utilityBean = UtilityBean.<RuleDetailsPojo> builder().accountIdentifier(account.identifier).serviceId("4")
                .authToken(AUTH_TOKEN).pojoObject(ruleDetailsPojo).build()
        ValidationUtils.commonServerValidations(_ as String, _ as String) >> accountBean

        when:
        rulesDetailsBL.serverValidation(utilityBean)

        then:
        noExceptionThrown()
    }

    def "Server validation with exception"(){
        setup:
        RuleDetailsPojo ruleDetailsPojo = new RuleDetailsPojo()
        ruleDetailsPojo.setId(216)
        UtilityBean<RuleDetailsPojo> utilityBean = UtilityBean.<RuleDetailsPojo>builder().accountIdentifier(account.identifier).serviceId("6")
                .authToken(AUTH_TOKEN).pojoObject(ruleDetailsPojo).build()
        ValidationUtils.commonServerValidations(_ as String, _ as String) >> accountBean

        when:
        rulesDetailsBL.serverValidation(utilityBean)

        then:
        thrown(ServerException)
    }
}
