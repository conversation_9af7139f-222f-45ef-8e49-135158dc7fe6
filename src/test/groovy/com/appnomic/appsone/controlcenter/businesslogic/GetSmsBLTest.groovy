package com.appnomic.appsone.controlcenter.businesslogic


import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GetSmsBLTest extends Specification {
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return "eyJhbGciOiJSUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.eyJqdGkiOiI5MzU2Y2NiZC03YmNjLTRkMjktYjY5MS00ZmY3ZjNmZjIyNzUiLCJleHAiOjE1NjIzMzA3ODAsIm5iZiI6MCwiaWF0IjoxNTYyMzI5ODgwLCJpc3MiOiJodHRwOi8vMTkyLjE2OC4xMy4xNjc6OTA4MC9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiI3NjQwMTIzYS1mYmRlLTRmZTUtOTgxMi01ODFjZDFlM2E5YzEiLCJ0eXAiOiJCZWFyZXIiLCJhenAiOiJhZG1pbi1jbGkiLCJhdXRoX3RpbWUiOjAsInNlc3Npb25fc3RhdGUiOiJhOTFmNjYyMC1iYmU5LTQ5ZmUtYTZiYy0wNjIzNjc3YzU0YWIiLCJhY3IiOiIxIiwic2NvcGUiOiJwcm9maWxlIGVtYWlsIiwiZW1haWxfdmVyaWZpZWQiOmZhbHNlLCJwcmVmZXJyZWRfdXNlcm5hbWUiOiJhcHBzb25lYWRtaW4ifQ.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    def "Create Sms :client validation failure"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"\",\n" +
                "  \"port\": 0,\n" +
                "  \"countryCode\": \"\",\n" +
                "  \"httpMethod\": \"\",\n" +
                "  \"httpRelativeUrl\": \"\",\n" +
                "  \"protocolName\": \"\",\n" +
                "  \"postData\": \"\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterName\": \"\",\n" +
                "      \"parameterValue\": \"\",\n" +
                "      \"parameterType\": \"\",\n" +
                "      \"action\" : \"DELETE\"\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : {SMS Port=Port is invalid. It should be an integer greater than 0, SMS protocol=Protocol is either NULL or empty. It should be one of HTTP, HTTPS or TCP, SMS parameter placeholder=SMS PARAMETER placeholder is NULL or EMPTY, SMS address=Address is either NULL, empty or its length is greater than 128 characters, SMS parameter name=Parameter name is either NULL, empty or its length is more than 128 characters., SMS parameter Value=Parameter value is either NULL, empty or its length is more than 128 characters.}"

    }
    def "Create Sms :client validation failure: invalid json"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"\",\n" +
                "  \"port\": 0,\n" +
                "  \"countryCod\": \"\",\n" +
                "  \"httpMethod\": \"\",\n" +
                "  \"httpRelativeUrl\": \"\",\n" +
                "  \"protocolName\": \"\",\n" +
                "  \"postData\": \"\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterName\": \"\",\n" +
                "      \"parameterValue\": \"\",\n" +
                "      \"parameterType\": \"\",\n" +
                "      \"action\" : \"DELETE\"\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid JSON."
    }
    def "client validation failure:Invalid Account identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "client validation invalid auth token"() {
        given:

        request.headers() >> new HashSet<>()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
    def "client validation success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateSmsBL().clientValidation(RequestObject)
        then:
        noExceptionThrown()
    }
    def "client validation failure due to incorrect action "() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"edit\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : SMS parameters with action other than ‘add’ are not allowed"
    }
    def "client validation failure due to duplicate entry"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    },\n"+
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new CreateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Duplicate SMS parameter"
    }
    /*Update client validation*/
    def "Update Sms :client validation failure"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"\",\n" +
                "  \"port\": 0,\n" +
                "  \"countryCode\": \"\",\n" +
                "  \"httpMethod\": \"\",\n" +
                "  \"httpRelativeUrl\": \"\",\n" +
                "  \"protocolName\": \"\",\n" +
                "  \"postData\": \"\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterName\": \"\",\n" +
                "      \"parameterValue\": \"\",\n" +
                "      \"parameterType\": \"\",\n" +
                "      \"action\" : \"DELETE\"\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : {SMS Port=Port is invalid. It should be an integer greater than 0, SMS protocol=Protocol is either NULL or empty. It should be one of HTTP, HTTPS or TCP, SMS parameter placeholder=SMS PARAMETER placeholder is NULL or EMPTY, SMS address=Address is either NULL, empty or its length is greater than 128 characters, SMS parameter name=Parameter name is either NULL, empty or its length is more than 128 characters., SMS parameter Value=Parameter value is either NULL, empty or its length is more than 128 characters.}"

    }
    def "Update Sms :client validation failure: invalid json"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"\",\n" +
                "  \"port\": 0,\n" +
                "  \"countryCod\": \"\",\n" +
                "  \"httpMethod\": \"\",\n" +
                "  \"httpRelativeUrl\": \"\",\n" +
                "  \"protocolName\": \"\",\n" +
                "  \"postData\": \"\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterName\": \"\",\n" +
                "      \"parameterValue\": \"\",\n" +
                "      \"parameterType\": \"\",\n" +
                "      \"action\" : \"DELETE\"\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid JSON."
    }
    def "Update SMS: client validation failure:Invalid Account identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "Update SMS: client validation invalid auth token"() {
        given:

        request.headers() >> new HashSet<>()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
    def "Update SMS: client validation success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n"+
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateSmsBL().clientValidation(RequestObject)
        then:
        noExceptionThrown()
    }

    def "Update SMS: client validation failure due to duplicate entry"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "    ,{\n" +
                "      \"parameterValue\": \"{SMSContent}\",\n" +
                "      \"parameterName\": \"smsContent\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    },\n"+
                "    {\n" +
                "      \"parameterValue\": \"{MobileNumber}\",\n" +
                "      \"parameterName\": \"**********\",\n" +
                "      \"parameterType\": \"QueryParameter\",\n" +
                "      \"isPlaceholder\": true,\n" +
                "       \"action\": \"add\" \n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Duplicate SMS parameter"
    }
    /*Get Sms*/
    def "Get SMS: client validation failure:Invalid Account identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new UpdateSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "Get SMS: client validation invalid auth token"() {
        given:

        request.headers() >> new HashSet<>()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new GetSmsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
}
