package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.service.GetSupervisorsService
import org.boon.core.Sys
import spock.lang.Specification

class GetSupervisorsBLTest extends Specification{

    def "Success Case"(){
        setup:
        RequestObject requestObject=new RequestObject()

        when:
        UtilityBean<Object> data = new GetSupervisorsBL().clientValidation(requestObject)

        then:
        data.getAuthToken()==null
        data.getAccountIdentifier()==null

    }
}
