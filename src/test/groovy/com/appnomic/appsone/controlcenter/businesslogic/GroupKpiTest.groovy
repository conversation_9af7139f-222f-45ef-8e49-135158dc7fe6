package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.MasterKpiGroupBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.exceptions.KpiException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GroupKpiTest extends Specification {

    @Shared
    String oldH2URL

    def setupSpec() {
        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(30000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        //the following line was added ONLY to ensure that the existing test cases are not impacted.
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    class DummyRequest extends Request {
        boolean nullCheck = true
        String body

        @Override
        String body() {
            if(body != null) {
                return body
            }

            if(nullCheck) {
                return null
            } else {
                return " "
            }
        }
    }

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def "GroupKPI client validation invalid request body"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        GroupKpi groupKpi = new GroupKpi()

        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "group_Kpi_Name": "GROUP_KPI_NAME",
                "kpi_identifier": "GROUP_KPI_UNIQUE_IDENTIFIER",
                "description": "Description of the KPI Group",
                "kpi_Type": "CORE",
                "discovery" : 0
               }"""

        when:
        groupKpi.clientValidation(request.body())

        then:
        final KpiException exception = thrown()
        exception.getMessage() == """KpiException : Invalid request body. Reason: Request body is either NULL or empty. Refer more details, refer application log file."""
    }

    def "GroupKPI client validation invalid input parameter"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "e573f852-5057-11e9-8fd2-b37b61e52317"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "GROUP_KPI_NAME",
                "groupKpiIdentifier": "GROUP_KPI_UNIQUE_IDENTIFIER",
                "description": "Description of the KPI Group",
                "discovery" : 0
               }"""

        when:
        groupKpi.clientValidation(request.body())

        then:
        final KpiException exception = thrown()
        exception.getMessage() == """KpiException : Invalid request. Reason: Invalid values for one or more attributes. Refer more details, refer application log file."""
    }

    def "GroupKPI client validation successful"() {
        setup:
        MySQLConnectionManager manager = MySQLConnectionManager.getInstance()
        manager.setHaveToRunTestCases(true)

        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "GROUP_KPI_NAME",
                "groupKpiIdentifier": "GROUP_KPI_UNIQUE_IDENTIFIER",
                "description": "Description of the KPI Group",
                "kpiType": "CORE",
                "discovery" : 0
               }"""

        when:
        def groupKpiRequest = groupKpi.clientValidation(request.body())

        then:
        notThrown()
        groupKpiRequest.toString() == """GroupKpi(id=0, groupKpiName=GROUP_KPI_NAME, groupKpiIdentifier=GROUP_KPI_UNIQUE_IDENTIFIER, description=Description of the KPI Group, kpiType=CORE, discovery=0, custom=1)"""
    }

    def "GroupKPI server validation failure invalid KPIType"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "GROUP_KPI_NAME",
                "groupKpiIdentifier": "GROUP_KPI_UNIQUE_IDENTIFIER",
                "description": "Description of the KPI Group",
                "kpiType": "CORE_AVAILABILITY",
                "discovery" : 0
               }"""
        def groupKpiRequest = groupKpi.clientValidation(request.body())

        when:
        groupKpi.serverValidation(groupKpiRequest, 1)

        then:
        final KpiException exception = thrown()
        exception.getMessage() == """KpiException : KpiType validation failure"""
    }

    def "GroupKPI server validation failure invalid name and NULL aliasName"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Thread.sleep(30000)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "DiskSpace",
                "description": "Description of the KPI Group",
                "kpiType": "CORE",
                "discovery" : 0
               }"""
        def groupKpiRequest = groupKpi.clientValidation(request.body())

        when:
        groupKpi.serverValidation(groupKpiRequest, 1)

        then:
        final KpiException exception = thrown()
        exception.getMessage() == """KpiException : Kpi group with provided name already exists"""
    }

    def "GroupKPI server validation failure invalid aliasName"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Thread.sleep(30000)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "DiskSpace",
                "groupKpiIdentifier": "DiskSpace",
                "description": "Description of the KPI Group",
                "kpiType": "CORE",
                "discovery" : 0
               }"""
        def groupKpiRequest = groupKpi.clientValidation(request.body())

        when:
        groupKpi.serverValidation(groupKpiRequest, 1)

        then:
        final KpiException exception = thrown()
        exception.getMessage() == """KpiException : Kpi group with provided alias name already exists"""
    }

    def "GroupKPI server successful validation"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "GROUP_KPI_NAME",
                "groupKpiIdentifier": "GROUP_KPI_UNIQUE_IDENTIFIER",
                "description": "Description of the KPI Group",
                "kpiType": "CORE",
                "discovery" : 0
               }"""
        def groupKpiRequest = groupKpi.clientValidation(request.body())

        when:
        def retVal = groupKpi.serverValidation(groupKpiRequest, 1)

        then:
        retVal
        noExceptionThrown()
    }

    def "process groupKpi request failure"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "DiskSpace_Test",
                "groupKpiIdentifier": "DiskSpace_Test",
                "description": "Description of the KPI Group",
                "kpiType": "CORE",
                "discovery" : 0
               }"""

        def groupKpiRequest = groupKpi.clientValidation(request.body())
        groupKpi.serverValidation(groupKpiRequest, 1)

        when:
        groupKpi.processGroupKpi(groupKpiRequest, null, 1)

        then:
        final KpiException exception = thrown()
        println(exception)
    }

    def "process groupKpi request invalid Authorization token"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> ""
        request.body() >> """{
                "groupKpiName": "DiskSpace_Test",
                "groupKpiIdentifier": "DiskSpace_Test",
                "description": "Description of the KPI Group",
                "kpiType": "CORE",
                "discovery" : 0
               }"""

        def groupKpiRequest = groupKpi.clientValidation(request.body())
        groupKpi.serverValidation(groupKpiRequest, 1)
        def userId = groupKpi.getUserId(request.headers("Authorization"))

        when:
        groupKpi.processGroupKpi(groupKpiRequest, userId, 1)

        then:
        final KpiException e = thrown()
        e.getMessage() == """KpiException : Error while adding Group KPI details."""
    }

    def "process groupKpi request"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "DiskSpace_Test",
                "groupKpiIdentifier": "DiskSpace_Test",
                "description": "Description of the KPI Group",
                "kpiType": "CORE",
                "discovery" : 0
               }"""

        def groupKpiRequest = groupKpi.clientValidation(request.body())
        groupKpi.serverValidation(groupKpiRequest, 1)

        when:
        def kpiId = groupKpi.processGroupKpi(groupKpiRequest, "userId", 1)

        then:
        List<MasterKpiGroupBean> groupBeanList = new KPIDataService().checkForGroupKpiUsingIdentifier("DiskSpace_Test", null)
        groupBeanList.get(0).getId() == kpiId.id
        groupBeanList.get(0).getDescription() == "Description of the KPI Group"
        groupBeanList.get(0).getName() == "DiskSpace_Test"
        groupBeanList.get(0).getKpiTypeId() == 30
        groupBeanList.get(0).getIdentifier() == "DiskSpace_Test"
    }

    def "GroupKPI client validation with auto-generated groupKpiIdentifier"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        Thread.sleep(30000)
        GroupKpi groupKpi = new GroupKpi()
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> """{
                "groupKpiName": "DiskSpace_Test_1",
                "description": "Description of the KPI Group",
                "kpiType": "CORE",
                "discovery" : 0
               }"""

        def groupKpiRequest = groupKpi.clientValidation(request.body())
        groupKpi.serverValidation(groupKpiRequest, 1)

        when:
        def kpiId = groupKpi.processGroupKpi(groupKpiRequest, "userId", 1)

        then:
        List<MasterKpiGroupBean> groupBeanList = new KPIDataService().checkForGroupKpiUsingIdentifier(groupKpiRequest.getGroupKpiIdentifier(), null)
        groupBeanList.get(0).getId() == kpiId.id
        groupBeanList.get(0).getDescription() == "Description of the KPI Group"
        groupBeanList.get(0).getName() == "DiskSpace_Test_1"
        groupBeanList.get(0).getKpiTypeId() == 30
        groupBeanList.get(0).getIdentifier().startsWith("DiskSpace_Test_1-")
    }
}
