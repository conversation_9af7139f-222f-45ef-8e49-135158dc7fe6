package com.appnomic.appsone.controlcenter.businesslogic


import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.InstanceHealthDetails
import com.appnomic.appsone.controlcenter.util.ValidationUtils
import spock.lang.Shared
import spock.lang.Specification

class InstanceHealthDataServiceTest extends Specification {

    @Shared
    String oldH2URL

    def test_setup(String changableH2URL) {
        String H2URL = changableH2URL

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()

        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(10000)
    }

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        //the following line was added ONLY to ensure that the existing test cases are not impacted.
        MySQLConnectionManager.INSTANCE.getHandle()
    }


    /*def "valid user getInstanceHealthData"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-valid.sql'"
        test_setup(changableH2URL)
        InstanceHealthData instanceHealthData  = new InstanceHealthData()
        com.appnomic.appsone.controlcenter.pojo.Account account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-1")

        when:
        List<InstanceHealthDetails> output = instanceHealthData.getInstanceHealthData(account,"ef9f7600-0905-4bcb-9504-f724cdfb6b9c")
        then:
        output.size() > 0
        output.get(0).getHost() == "**************"
        output.get(0).id == 17
        output.get(0).instanceName == "RHEL_NB_Web_Host_154_Inst_1"
        output.get(1).instanceName == "RHEL_NB_Web_Host_171_Inst_1"
        ((Set<String>) output.get(0).getServices()).toArray()[0] == "NB-Web-Service"
        output.get(0).type == "RHEL"
    }*/

    /*def "invalid user getInstanceHealthData"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-valid.sql'"
        test_setup(changableH2URL)
        InstanceHealthData instanceHealthData  = new InstanceHealthData()
        com.appnomic.appsone.controlcenter.pojo.Account account = ValidationUtils.validAndGetAccount("d681ef13-d690-4917-jkhg-6c79b-1")

        when:
        List<InstanceHealthDetails> data = instanceHealthData.getInstanceHealthData(account,"def9f7600-0905-4bcb-9504-f724cdfb6b9c")

        then:
        data.isEmpty()
    }*/
    def "invalid account getInstanceHealthData"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-valid.sql'"
        test_setup(changableH2URL)
        InstanceHealthData instanceHealthData  = new InstanceHealthData()
        def account = ValidationUtils.validAndGetAccount("e573f852-5057-11e9-8fd2-b37b61e52317")
        when:
        List<InstanceHealthDetails> data = instanceHealthData.getInstanceHealthData(account,"ef9f7600-0905-4bcb-9504-f724cdfb6b9c")

        then:
        data.isEmpty()
    }

}
