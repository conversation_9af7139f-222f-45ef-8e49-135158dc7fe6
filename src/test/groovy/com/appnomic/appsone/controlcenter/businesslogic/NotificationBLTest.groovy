package com.appnomic.appsone.controlcenter.businesslogic


import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class NotificationBLTest extends Specification {
    Request request = Spy(Request.class)
    Response response = Spy(Response.class)

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/notificationTypeConfiguration/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/notificationTypeConfiguration/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        //the following line was added ONLY to ensure that the existing test cases are not impacted.
        MySQLConnectionManager.INSTANCE.getHandle()
    }

/*
    def "client validation failure"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "  \"address\": \"\",\n" +
                "  \"port\": 0,\n" +
                "  \"countryCode\": \"\",\n" +
                "  \"httpMethod\": \"\",\n" +
                "  \"httpRelativeUrl\": \"\",\n" +
                "  \"protocolName\": \"\",\n" +
                "  \"postData\": \"\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterName\": \"\",\n" +
                "      \"parameterValue\": \"\",\n" +
                "      \"parameterTypeId\": 0\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        SmsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "{SMS Port=Port is invalid. It should be an integer greater than 0, SMS Param Type Id=Parameter type id is invalid. It should be one of 165, 166 or 167, SMS protocol=Protocol is either NULL or empty. It should be one of HTTP, HTTPS or TCP, SMS country=Country code is either NULL, empty or its length is greater than 32 characters, SMS Param placeholder=SMS PARAMETER placeholder is NULL or EMPTY, SMS address=Address is either NULL, empty or its length is greater than 128 characters, SMS http URL=HTTP URL is either NULL, empty or its length is greater than 512 characters, SMS http method=HTTP method is either NULL or empty. It should be either GET or POST, SMS Param Name=Parameter name is either NULL, empty or its length is more than 128 characters., SMS Param Value=Parameter value is either NULL, empty or its length is more than 128 characters.}"

    }

    def "client validation failure :invalid body"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> null
        when:
        SmsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Invalid request body. Reason: Request body is either NULL or empty. Refer more details, refer application log file."
    }

    def "client validation failure :invalid json"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"ports\": 0,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"postData\": \"string\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterName\": \"{mobileNumber}\",\n" +
                "      \"parameterValue\": \"**********\",\n" +
                "      \"parameterTypeId\": 165,\n" +
                "      \"isPlaceholder\": true\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        SmsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        String[] split = e.getMessage().split("\\(");
        split[0] == "Invalid JSON. Reason: Unrecognized field \"ports\" "
    }

    def "client validation failure :duplicate sms parameter"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"postData\": \"string\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterName\": \"{mobileNumber}\",\n" +
                "      \"parameterValue\": \"**********\",\n" +
                "      \"parameterTypeId\": 165,\n" +
                "      \"isPlaceholder\": true\n" +
                "    } ,{" +
                "      \"parameterName\": \"{mobileNumber}\",\n" +
                "      \"parameterValue\": \"**********\",\n" +
                "      \"parameterTypeId\": 165,\n" +
                "      \"isPlaceholder\": true\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        SmsBL.addClientValidations(request)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Duplicate SMS parameter"
    }
    def "client validation success"() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "{\n" +
                "  \"address\": \"www.appnomic.com\",\n" +
                "  \"port\": 9090,\n" +
                "  \"countryCode\": \"IND\",\n" +
                "  \"httpMethod\": \"GET\",\n" +
                "  \"httpRelativeUrl\": \"/home/<USER>",\n" +
                "  \"protocolName\": \"HTTP\",\n" +
                "  \"postData\": \"string\",\n" +
                "  \"parameters\": [\n" +
                "    {\n" +
                "      \"parameterName\": \"{mobileNumber}\",\n" +
                "      \"parameterValue\": \"**********\",\n" +
                "      \"parameterTypeId\": 165,\n" +
                "      \"isPlaceholder\": true\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        when:
        SmsDetails smsDetails = SmsBL.addClientValidations(request)
        then:
        smsDetails.getAddress() == "www.appnomic.com"
        smsDetails.getError().size() == 0
        noExceptionThrown()
    }
    def "Server validation Failure :already available"() {
        given:
        def userAccount = new UserAccountBean()
        def sms = new SmsDetails()
        def smsparams = new ArrayList()
        def smsParam = new SmsParameter()
        def account = new com.appnomic.appsone.controlcenter.pojo.Account()
        smsParam.setParameterName("{mobileNumber}")
        smsParam.setParameterValue("**********")
        smsParam.setParameterTypeId(165)
        smsParam.setIsPlaceholder(true)
        sms.setPort(9090)
        sms.setAddress("www.appnomic.com")
        sms.setCountryCode("IND")
        sms.setHttpMethod("GET")
        sms.setHttpRelativeUrl("/home/<USER>")
        sms.setProtocolName("HTTP")
        smsparams.add(smsParam)
        sms.setParameters(smsparams)
        account.setAccountId(4)
        userAccount.setAccount(account)
        userAccount.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        SmsBL.addServerValidations(sms, userAccount)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "SMS details already available for account id: 4"
    }

    def "Server validation Failure:Invalid security"() {
        given:
        def userAccount = new UserAccountBean()
        def sms = new SmsDetails()
        def smsparams = new ArrayList()
        def smsParam = new SmsParameter()
        def account = new com.appnomic.appsone.controlcenter.pojo.Account()
        smsParam.setParameterName("{mobileNumber}")
        smsParam.setParameterValue("**********")
        smsParam.setParameterTypeId(165)
        smsParam.setIsPlaceholder(true)
        sms.setPort(9090)
        sms.setAddress("www.appnomic.com")
        sms.setCountryCode("IND")
        sms.setHttpMethod("GET")
        sms.setHttpRelativeUrl("/home/<USER>")
        sms.setProtocolName("HTTPST")
        smsparams.add(smsParam)
        sms.setParameters(smsparams)
        account.setAccountId(3)
        userAccount.setAccount(account)
        userAccount.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        SmsBL.addServerValidations(sms, userAccount)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "SMS protocol unavailable for protocol name:HTTPST"
    }
    def "Server validation Failure: Invalid HTTP method"() {
        given:
        def userAccount = new UserAccountBean()
        def sms = new SmsDetails()
        def smsparams = new ArrayList()
        def smsParam = new SmsParameter()
        def account = new com.appnomic.appsone.controlcenter.pojo.Account()
        smsParam.setParameterName("{mobileNumber}")
        smsParam.setParameterValue("**********")
        smsParam.setParameterTypeId(165)
        smsParam.setIsPlaceholder(true)
        sms.setPort(9090)
        sms.setAddress("www.appnomic.com")
        sms.setCountryCode("IND")
        sms.setHttpMethod("GETS")
        sms.setHttpRelativeUrl("/home/<USER>")
        sms.setProtocolName("HTTPS")
        smsparams.add(smsParam)
        sms.setParameters(smsparams)
        account.setAccountId(3)
        userAccount.setAccount(account)
        userAccount.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        SmsBL.addServerValidations(sms, userAccount)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "HTTP method type unavailable for http method:GETS"
    }
    def "Server validation Failure: Invalid Parameter Type"() {
        given:
        def userAccount = new UserAccountBean()
        def sms = new SmsDetails()
        def smsparams = new ArrayList()
        def smsParam = new SmsParameter()
        def account = new com.appnomic.appsone.controlcenter.pojo.Account()
        smsParam.setParameterName("{mobileNumber}")
        smsParam.setParameterValue("**********")
        smsParam.setParameterTypeId(600)
        smsParam.setIsPlaceholder(true)
        sms.setPort(9090)
        sms.setAddress("www.appnomic.com")
        sms.setCountryCode("IND")
        sms.setHttpMethod("GET")
        sms.setHttpRelativeUrl("/home/<USER>")
        sms.setProtocolName("HTTPS")
        smsparams.add(smsParam)
        sms.setParameters(smsparams)
        account.setAccountId(3)
        userAccount.setAccount(account)
        userAccount.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        SmsBL.addServerValidations(sms, userAccount)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Parameter type details unavailable for parameter type id:600"
    }
    def "Server validation Failure: Invalid Parameter name type"() {
        given:
        def userAccount = new UserAccountBean()
        def sms = new SmsDetails()
        def smsparams = new ArrayList()
        def smsParam = new SmsParameter()
        def account = new com.appnomic.appsone.controlcenter.pojo.Account()
        smsParam.setParameterName("{mobilesNumber}")
        smsParam.setParameterValue("**********")
        smsParam.setParameterTypeId(165)
        smsParam.setIsPlaceholder(true)
        sms.setPort(9090)
        sms.setAddress("www.appnomic.com")
        sms.setCountryCode("IND")
        sms.setHttpMethod("GET")
        sms.setHttpRelativeUrl("/home/<USER>")
        sms.setProtocolName("HTTPS")
        smsparams.add(smsParam)
        sms.setParameters(smsparams)
        account.setAccountId(3)
        userAccount.setAccount(account)
        userAccount.setUserId("7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        when:
        SmsBL.addServerValidations(sms, userAccount)
        then:
        final e = thrown(RequestException)
        e.getMessage() == "Sub type is not found for the given parameter name type: {mobilesNumber}"
    }
    def "add method success"(){
        given:
        def sms = new SmsDetails()
        def smsparams = new ArrayList()
        def smsParam = new SmsParameter()
        smsParam.setParameterName("{mobileNumber}")
        smsParam.setParameterValue("**********")
        smsParam.setParameterTypeId(165)
        smsParam.setIsPlaceholder(true)
        sms.setPort(9090)
        sms.setAddress("www.appnomic.com")
        sms.setCountryCode("IND")
        sms.setHttpMethod("GET")
        sms.setHttpRelativeUrl("/home/<USER>")
        sms.setProtocolName("HTTP")
        smsparams.add(smsParam)
        sms.setParameters(smsparams)
        when:
        SmsBL.add(sms,"7640123a-fbde-4fe5-9812-581cd1e3a9c1", 5)
        then:
        noExceptionThrown()
    }
    def "Delete method failure"() {
        when:
        SmsBL.updateSmsDetailsStatus(3)
        then:
        final e = thrown(ControlCenterException)
        e.getMessage() == "No SMS configured with account id: 3"
    }

    def "Delete method success"() {
        when:
        SmsBL.updateSmsDetailsStatus(2)
        then:
        noExceptionThrown()
    }
    def "Delete method success :inactive to active"() {
        when:
        SmsBL.updateSmsDetailsStatus(4)
        then:
        noExceptionThrown()
    }
*/

}
