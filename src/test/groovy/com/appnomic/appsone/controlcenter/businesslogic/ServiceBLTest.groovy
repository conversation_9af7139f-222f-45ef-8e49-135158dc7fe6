package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.exceptions.RequestException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spark.Request
import spock.lang.Specification

class ServiceBLTest extends  Specification{
    Request request = Spy(Request.class)
    def "clientValidation failure :request is null"() {
        given:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody(null)
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)
        when:
        new AddServiceBL().clientValidation(null)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "clientValidation failure :request body is null"() {
        given:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody(null)
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)
        when:
        new AddServiceBL().clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "clientValidation failure :Account Identifier should not be empty."() {
        given:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[" +
                "\n    " +
                "{" +
                "\n " +
                "       \"name\":\"GroovyService\"," +
                "\n        \"id\":\"4f51f8b0-b6c9-4375-8729-b21bc6d8mc899\"," +
                "\n        \"layer\": \"random-layer\"," +
                "\n        \"type\": \"kubernetes\"" +
                "\n        " +
                "}" +
                "\n  " +
                " ]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)
        when:
        new AddServiceBL().clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account identifier is invalid"
    }

    def "clientValidation failure :Authorization Token is empty"() {
        given:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[" +
                "\n    " +
                "{" +
                "\n " +
                "       \"name\":\"GroovyService\"," +
                "\n        \"id\":\"4f51f8b0-b6c9-4375-8729-b21bc6d8mc899\"," +
                "\n        \"layer\": \"random-layer\"," +
                "\n        \"type\": \"kubernetes\"" +
                "\n        " +
                "}" +
                "\n  " +
                " ]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)
        when:
        new AddServiceBL().clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "clientValidation failure :invalid JSON"() {
        given:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[" +
                "\n    " +
                "{" +
                "\n " +
                "       \"name\":\"GroovyService\"," +
                "\n        \"id\":\"4f51f8b0-b6c9-4375-8729-b21bc6d8mc899\"," +
                "\n        \"layer\": \"random-layer\"," +
                "\n        \"type\": \"kubernetes\"" +
                "\n        " +
                "}" +
                "\n  " +
                " ]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)
        when:
        new AddServiceBL().clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid JSON."
    }

    def "clientValidation failure :duplicate service"() {
        given:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[" +
                "\n    " +
                "{" +
                "\n " +
                "       \"name\":\"GroovyService\"," +
                "\n        \"identifier\":\"4f51f8b0-b6c9-4375-8729-b21bc6d8mc899\"," +
                "\n        \"layer\": \"random-layer\"," +
                "\n        \"type\": \"kubernetes\"" +
                "\n        " +
                "}," +
                "{" +
                "\n " +
                "       \"name\":\"GroovyService\"," +
                "\n        \"identifier\":\"4f51f8b0-b6c9-4375-8729-b21bc6d8mc899\"," +
                "\n        \"layer\": \"random-layer\"," +
                "\n        \"type\": \"kubernetes\"" +
                "\n        " +
                "}" +
                "\n  " +
                " ]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)
        when:
        new AddServiceBL().clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Duplicate Service name or identifier"
    }

    def "clientValidation failure :invalid Service name"() {
        given:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[" +
                "\n    " +
                "{" +
                "\n " +
                "       \"name\":\"\"," +
                "\n        \"identifier\":\"4f51f8b0-b6c9-4375-8729-b21bc6d8mc899\"," +
                "\n        \"layer\": \"random-layer\"," +
                "\n        \"type\": \"kubernetes\"" +
                "\n        " +
                "}" +
                "\n  " +
                " ]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)
        when:
        new AddServiceBL().clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : {Service name=Invalid Service name : Name can not be empty and it should have 1 - 128 characters only.}"
    }

    def "clientValidation failure :invalid Service Type"() {
        given:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[" +
                "\n    " +
                "{" +
                "\n " +
                "       \"name\":\"GroovyService\"," +
                "\n        \"identifier\":\"4f51f8b0-b6c9-4375-8729-b21bc6d8mc899\"," +
                "\n        \"layer\": \"random-layer\"," +
                "\n        \"type\": \"myService\"" +
                "\n        " +
                "}" +
                "\n  " +
                " ]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)
        when:
        new AddServiceBL().clientValidation(requestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : {Service Type=Invalid Service Type : It should be 'Kubernetes'.}"
    }
}
