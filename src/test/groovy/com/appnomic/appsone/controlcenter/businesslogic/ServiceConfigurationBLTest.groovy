package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.exceptions.RequestException
import com.appnomic.appsone.controlcenter.exceptions.ServiceConfigDetailsException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class ServiceConfigurationBLTest extends Specification {

    Request request = Spy(Request.class)
    Response response = Spy(Response.class)

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceConfigurations/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceConfigurations/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(10000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        //the following line was added ONLY to ensure that the existing test cases are not impacted.
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def test() {
        given:
        Request req = null

        when:
        ServiceConfigurationBL serviceConfigurationBL = new ServiceConfigurationBL()
        serviceConfigurationBL.requestValidation(req)

        then:
        final RequestException e = thrown()
        e.getMessage() == "Request should not be NULL"
    }

    def validateAccountAndServiceTest() {
        given:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-12"
        request.params(":serviceId") >> "2"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> ""

        when:
        ServiceConfigurationBL serviceConfigurationBL = new ServiceConfigurationBL()
        serviceConfigurationBL.validateAccountAndService(request)

        then:
        final ServiceConfigDetailsException e = thrown()
        e.getMessage() == "Invalid service id. Reason: Service id provided is unavailable."
    }
}
