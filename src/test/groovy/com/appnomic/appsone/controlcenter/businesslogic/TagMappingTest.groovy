package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.exceptions.KpiException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails
import com.appnomic.appsone.controlcenter.dao.mysql.TagsDataService
import spock.lang.Specification

class TagMappingTest extends Specification {

    def "addTagMappings invalid input"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)

        when:
        new TagMapping().addTagMappings(-1, -1, null, null, -1, -1, null)

        then:
        def error = thrown(KpiException)
        error.message == "KpiException : TagMapping data insertion failed. Reason: categoryIdentifier is invalid."
    }

    def "addTagMappings insertion failure"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)

        when:
        new TagMapping().addTagMappings(1, 1, "Category", "user-id", 3, 1, null)

        then:
        def error = thrown(KpiException)
        error.message == "KpiException : Error while adding TagMapping details."
    }

    def "addTagMappings insertion successful"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)

        when:
        def tagId = new TagMapping().addTagMappings(1, 1, "TestCategory", "user-id", 1, 3019, null)

        then:
        TagMappingDetails tagMappingDetails = TagsDataService.getTagDetails(null, tagId)
        if(null != tagMappingDetails) {
            tagMappingDetails.getUserDetailsId() == "user-id"
            tagMappingDetails.getTagKey() == "1"
            tagMappingDetails.getTagValue() == "TestCategory"
            tagMappingDetails.getAccountId() == 1
            tagMappingDetails.getTagId() == 1
            tagMappingDetails.getObjectId() == 3019
        }
    }
}
