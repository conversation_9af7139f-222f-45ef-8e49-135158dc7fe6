package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class TransactionCountBLTest extends Specification {
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return "eyJhbGciOiJSUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.eyJqdGkiOiI5MzU2Y2NiZC03YmNjLTRkMjktYjY5MS00ZmY3ZjNmZjIyNzUiLCJleHAiOjE1NjIzMzA3ODAsIm5iZiI6MCwiaWF0IjoxNTYyMzI5ODgwLCJpc3MiOiJodHRwOi8vMTkyLjE2OC4xMy4xNjc6OTA4MC9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiI3NjQwMTIzYS1mYmRlLTRmZTUtOTgxMi01ODFjZDFlM2E5YzEiLCJ0eXAiOiJCZWFyZXIiLCJhenAiOiJhZG1pbi1jbGkiLCJhdXRoX3RpbWUiOjAsInNlc3Npb25fc3RhdGUiOiJhOTFmNjYyMC1iYmU5LTQ5ZmUtYTZiYy0wNjIzNjc3YzU0YWIiLCJhY3IiOiIxIiwic2NvcGUiOiJwcm9maWxlIGVtYWlsIiwiZW1haWxfdmVyaWZpZWQiOmZhbHNlLCJwcmVmZXJyZWRfdXNlcm5hbWUiOiJhcHBzb25lYWRtaW4ifQ.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    def "client validation invalid account-identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "")
        parameters.put(":serviceId","2")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new TransactionCountBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "client validation null service id"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new TransactionCountBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid service id provided."
    }
    def "client validation not number service id"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","xyz")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new TransactionCountBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid service id provided."
    }
    def "client validation service id is 0"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","0")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new TransactionCountBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid service id provided."
    }
    def "client validation invalid auth token"() {
        given:
        InvalidDummyRequest invalidDummyRequest = Spy(InvalidDummyRequest.class)
        Set<String> headers = new HashSet<>()
        invalidDummyRequest.headers() >> headers.toSet()
        invalidDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","12")
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> ""
        when:
        def RequestObject = new RequestObject(invalidDummyRequest)
        new TransactionCountBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
    def "client validation success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","12")
        request.params() >> parameters
        request.body() >> ""
        when:
        def RequestObject = new RequestObject(request)
        new TransactionCountBL().clientValidation(RequestObject)
        then:
        noExceptionThrown()
    }
}
