package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UserAccountBean
import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean
import com.appnomic.appsone.controlcenter.exceptions.RequestException
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.pojo.TagRequestPojo
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class TransactionGroupingBLTest extends Specification {
    Response response = Spy(Response.class)
    Request request = Spy(Request.class)
    TransactionGroupingBL transactionGroupingBL = Spy(TransactionGroupingBL.class)
    String AUTH_TOKEN = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

    @Shared
    AccountBean account = new AccountBean()
    @Shared
    UserAccountBean accountBean = new UserAccountBean()

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    @Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/tagRequestConfiguration/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/tagRequestConfiguration/populate.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        account.id = 2
        account.identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        account.name= "INDIA"
        account.userIdDetails = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
        accountBean.setAccount(account)
        accountBean.setUserId("1")
        Thread.sleep(5000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "Client validation without exception"(){
        given:
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-12")

        request.params() >> parameters
        request.headers() >> header.toSet()
        request.body() >> ""
        request.queryMap() >> new HashMap<>()

        when:
        RequestObject requestObject = new RequestObject(request)
        UtilityBean<TagRequestPojo> utilityBean = transactionGroupingBL.clientValidation(requestObject)

        then:
        noExceptionThrown()
        utilityBean.getAccountIdentifier().equals("d681ef13-d690-4917-jkhg-6c79b-12")
    }

    def "Client validation exception"(){
        given:
        parameters.put(":identifier", null)

        request.params() >> parameters
        request.headers() >> header.toSet()
        request.body() >> ""
        request.queryMap() >> new HashMap<>()

        when:
        RequestObject requestObject = new RequestObject(request)
        transactionGroupingBL.clientValidation(requestObject)

        then:
        final e = thrown(RequestException)
        e.getSimpleMessage()== "RequestException :: Invalid parameter received. Parameter name: :identifier, value: null."
    }

    def "Server validation without exception"(){
        setup:
        TagRequestPojo tagRequest = new TagRequestPojo()
        tagRequest.setObjectIds(Arrays.asList(10))
        tagRequest.getAddDiscoveryTags().addAll(Arrays.asList("Tag6"))
        tagRequest.getRemoveDiscoveryTags().addAll(Arrays.asList("Tag2"))
        UtilityBean<TagRequestPojo> utilityBean = UtilityBean.<TagRequestPojo>builder().accountIdentifier(account.identifier).pojoObject(tagRequest).build()
        transactionGroupingBL.getCommonServerValidations(_ as String, _ as String) >> accountBean

        when:
        transactionGroupingBL.serverValidation(utilityBean, AUTH_TOKEN)

        then:
        noExceptionThrown()
    }

    def "Get transaction tags list: no exception"(){
        setup:
        TagRequestPojo tagRequest = new TagRequestPojo()
        tagRequest.setObjectIds(Arrays.asList(10))
        tagRequest.getAddDiscoveryTags().addAll(Arrays.asList("Tag6"))
        tagRequest.getRemoveDiscoveryTags().addAll(Arrays.asList("Tag2"))
        UtilityBean<TagRequestPojo> utilityBean = UtilityBean.<TagRequestPojo>builder().accountIdentifier(account.identifier).pojoObject(tagRequest).build()
        transactionGroupingBL.getCommonServerValidations(_ as String, _ as String) >> accountBean

        when:
        transactionGroupingBL.serverValidation(utilityBean, AUTH_TOKEN)
        List<String> data = transactionGroupingBL.getTagsName(utilityBean)

        then:
        noExceptionThrown()

        then:
        data.size()>0
    }
}
