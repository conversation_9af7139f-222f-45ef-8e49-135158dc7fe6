package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.AgentCommandPojo
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import spock.lang.Specification

class TriggerAgentCommandBLTest extends Specification{

    def "Client validation failure - NULL request object"() {
        when:
        new TriggerAgentCommandBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL request body"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        requestObject.setQueryParams(queryMapData)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "Client validation failure - request body garbage String"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("testing-with-dummy-body")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - NULL authorization token"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization token"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization key"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - Invalid datatype of request body keys"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"Strings not allowed here\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."
    }

    def "Client validation failure - request body 'physicalAgentId' key <= 0 value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"-10\",\n" +
                "        \"commandTypeId\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input validation failure"
    }

    def "Client validation failure - request body 'commandTypeId' key <= 0 value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"10\",\n" +
                "        \"commandTypeId\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": -9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input validation failure"
    }

    def "Client validation failure -  both request body 'physicalAgentId' & 'commandTypeId' key <= 0 value"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": -14,\n" +
                "        \"commandTypeId\": 0\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input validation failure"

    }

    def "Success Case - With distinct physicalAgentId and commandTypeId pair"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<AgentCommandPojo>> data = new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0).getPhysicalAgentId() == 13
        data.getPojoObject().get(0).getCommandTypeId() == 8

    }

    def "Success Case - duplicate physicalAgentId and commandTypeId pair"(){
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"13\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"13\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"13\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"13\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<AgentCommandPojo>> data = new TriggerAgentCommandBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0).getPhysicalAgentId() == 13
        data.getPojoObject().get(0).getCommandTypeId() == 8
    }

}
