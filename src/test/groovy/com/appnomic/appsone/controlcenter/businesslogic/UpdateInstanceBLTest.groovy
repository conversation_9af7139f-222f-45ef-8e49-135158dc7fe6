package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.beans.UtilityBean
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.exceptions.ClientException
import com.appnomic.appsone.controlcenter.pojo.RequestObject
import com.appnomic.appsone.controlcenter.pojo.UpdateInstancePojo
import spock.lang.Specification

class UpdateInstanceBLTest extends Specification{

    def "Client validation failure - NULL request object"() {
        when:
        new UpdateInstanceBL().clientValidation(null)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Request object is null or empty."
    }

    def "Client validation failure - NULL request body"() {
        setup:
        RequestObject requestObject = new RequestObject()

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        Map<String, String[]> queryMapData = new HashMap<>()
        requestObject.setQueryParams(queryMapData)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_REQUEST_BODY
    }

    def "Client validation failure - request body garbage String"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("testing-with-dummy-body")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.JSON_INVALID
    }

    def "Client validation failure - request body list empty"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : " + UIMessages.INVALID_REQUEST_BODY
    }

    def "Client validation failure - NULL accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", null)
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - Empty accountIdentifier"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid account identifier"
    }

    def "Client validation failure - NULL authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", null)
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization token"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - empty authorization key"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid authorization token"
    }

    def "Client validation failure - request body instance Id value <= 0"() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 0,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": -10,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)
        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."
    }

    def "Client validation failure - request body instance Identifier value empty or null"() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")
        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 10,\n" +
                "        \"instanceIdentifier\": null,\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": null,\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)
        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."
    }

    def "Client validation failure - request body new instance name empty"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \" \"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input Validation failure for Update Instance Details."
    }

    def "Client validation failure - request body new environment name empty"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"environment\": \"  \"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input Validation failure for Update Instance Details."
    }

    def "Client validation failure - request body application data garbage String value"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": \"Random String\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."

    }

    def "Client validation failure - request body application/id <=0 "() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": -19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 0,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)

        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Client validation failure - request body application/name empty or null "() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \" \",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 11,\n" +
                "                \"name\": null,\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)

        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Client validation failure - request body application/identifier empty or null "() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 11,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": null,\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)

        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Client validation failure - request body application/service null or empty array "() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": []\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 11,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": null\n" +
                "            }\n" +
                "        ],\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)

        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Client validation failure - request body application/service garbage String"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": \"Random String\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Invalid JSON."

    }

    def "Client validation failure - request body application/service/id <=0 "() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": -20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 11,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 0,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)

        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Client validation failure - request body application/service/name empty or null "() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \" \",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 11,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": null,\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)

        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Client validation failure - request body application/service/identifier empty or null "() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": null,\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 11,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \" \",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)

        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Client validation failure - request body application/service/action empty or null "() {
        setup:
        RequestObject requestObject1 = new RequestObject()
        requestObject1.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject1.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject1.setHeaders(headers)

        RequestObject requestObject2 = new RequestObject()
        requestObject2.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 11,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": null\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")
        requestObject2.setParams(params)
        requestObject2.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject1)

        then:
        final ClientException e1 = thrown()
        e1.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

        when:
        new UpdateInstanceBL().clientValidation(requestObject2)

        then:
        final ClientException e2 = thrown()
        e2.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Client validation failure - request body application/service/action value other than 'Add' or 'Remove' "() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 1,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Delete\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Integrate\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        new UpdateInstanceBL().clientValidation(requestObject)

        then:
        final ClientException e = thrown()
        e.getMessage() == "ClientException : Input Validation failure for Update Instance Details."

    }

    def "Success case - With Only Instance Name update"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 37,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<UpdateInstancePojo>> data = new UpdateInstanceBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 1
        data.getPojoObject().get(0).instanceId == 37
        data.getPojoObject().get(0).name == "WINDOWS_53_Host_New"

    }

    def "Success case - With Only Instance Environment update"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 37,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<UpdateInstancePojo>> data = new UpdateInstanceBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 1
        data.getPojoObject().get(0).instanceId == 37
        data.getPojoObject().get(0).environment == "STAND BY"

    }

    def "Success case - With Single Application"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 37,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<UpdateInstancePojo>> data = new UpdateInstanceBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 1
        data.getPojoObject().get(0).instanceId == 37
        data.getPojoObject().get(0).application.get(0).name == "MicroBanking"

    }

    def "Success case - With Multiple Applications"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 37,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 9,\n" +
                "                \"name\": \"LOS\",\n" +
                "                \"identifier\": \"los_2\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 10,\n" +
                "                        \"name\": \"LOS-DB-Service\",\n" +
                "                        \"identifier\": \"LOS-DB-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<UpdateInstancePojo>> data = new UpdateInstanceBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 1
        data.getPojoObject().get(0).instanceId == 37
        data.getPojoObject().get(0).application.get(0).name == "MicroBanking"
        data.getPojoObject().get(0).application.get(1).service.get(0).action == "Remove"

    }

    def "Success case - With All Fields Present"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 37,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 9,\n" +
                "                \"name\": \"LOS\",\n" +
                "                \"identifier\": \"los_2\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 10,\n" +
                "                        \"name\": \"LOS-DB-Service\",\n" +
                "                        \"identifier\": \"LOS-DB-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<UpdateInstancePojo>> data = new UpdateInstanceBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 1
        data.getPojoObject().get(0).instanceId == 37
        data.getPojoObject().get(0).name == "WINDOWS_53_Host_New"
        data.getPojoObject().get(0).application.get(0).name == "MicroBanking"
        data.getPojoObject().get(0).application.get(1).service.get(0).action == "Remove"
        data.getPojoObject().get(0).environment == "STAND BY"

    }

    def "Success case - With Multiple Instances"() {
        setup:
        RequestObject requestObject = new RequestObject()
        requestObject.setBody("[\n" +
                "    {\n" +
                "        \"instanceId\": 37,\n" +
                "        \"instanceIdentifier\": \"WINDOWS_53_Host\",\n" +
                "        \"name\": \"WINDOWS_53_Host_New\",\n" +
                "        \"application\": [\n" +
                "            {\n" +
                "                \"id\": 19,\n" +
                "                \"name\": \"MicroBanking\",\n" +
                "                \"identifier\": \"microbanking_1\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 20,\n" +
                "                        \"name\": \"IIS-Web-Service\",\n" +
                "                        \"identifier\": \"IIS-Web-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\": 22,\n" +
                "                        \"name\": \"MSSQL-DB-Service\",\n" +
                "                        \"identifier\": \"MSSQL-DB-Service\",\n" +
                "                        \"action\": \"Add\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 9,\n" +
                "                \"name\": \"LOS\",\n" +
                "                \"identifier\": \"los_2\",\n" +
                "                \"service\": [\n" +
                "                    {\n" +
                "                        \"id\": 10,\n" +
                "                        \"name\": \"LOS-DB-Service\",\n" +
                "                        \"identifier\": \"LOS-DB-Service\",\n" +
                "                        \"action\": \"Remove\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"environment\": \"STAND BY\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"instanceId\": 34,\n" +
                "        \"instanceIdentifier\": \"Window_Host_1\",\n" +
                "        \"environment\": \"PROD\"\n" +
                "    }\n" +
                "]")

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        requestObject.setParams(params)

        Map<String, String> headers = new TreeMap<>(String.CASE_INSENSITIVE_ORDER)
        headers.put("authorization", "testing-with-dummy-authorization-header")
        requestObject.setHeaders(headers)

        when:
        UtilityBean<List<UpdateInstancePojo>> data = new UpdateInstanceBL().clientValidation(requestObject)

        then:
        data.getAccount() == null
        data.getServiceId() == null
        data.getApplicationId() == null
        data.getBatchName() == null
        data.getAccountIdentifier() == "d681ef13-d690-4917-jkhg-6c79b-1"
        data.getAuthToken() == "testing-with-dummy-authorization-header"
        data.getPojoObject().size() == 2
        data.getPojoObject().get(0).instanceId == 37
        data.getPojoObject().get(0).name == "WINDOWS_53_Host_New"
        data.getPojoObject().get(0).application.get(0).name == "MicroBanking"
        data.getPojoObject().get(0).application.get(1).service.get(0).action == "Remove"
        data.getPojoObject().get(0).environment == "STAND BY"
        data.getPojoObject().get(1).instanceId == 34
        data.getPojoObject().get(1).environment == "PROD"

    }

}
