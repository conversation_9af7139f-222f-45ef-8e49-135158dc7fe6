package com.appnomic.appsone.controlcenter.businesslogic


import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class UpdateTransactionBLTest extends Specification{
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return "eyJhbGciOiJSUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.eyJqdGkiOiI5MzU2Y2NiZC03YmNjLTRkMjktYjY5MS00ZmY3ZjNmZjIyNzUiLCJleHAiOjE1NjIzMzA3ODAsIm5iZiI6MCwiaWF0IjoxNTYyMzI5ODgwLCJpc3MiOiJodHRwOi8vMTkyLjE2OC4xMy4xNjc6OTA4MC9hdXRoL3JlYWxtcy9tYXN0ZXIiLCJzdWIiOiI3NjQwMTIzYS1mYmRlLTRmZTUtOTgxMi01ODFjZDFlM2E5YzEiLCJ0eXAiOiJCZWFyZXIiLCJhenAiOiJhZG1pbi1jbGkiLCJhdXRoX3RpbWUiOjAsInNlc3Npb25fc3RhdGUiOiJhOTFmNjYyMC1iYmU5LTQ5ZmUtYTZiYy0wNjIzNjc3YzU0YWIiLCJhY3IiOiIxIiwic2NvcGUiOiJwcm9maWxlIGVtYWlsIiwiZW1haWxfdmVyaWZpZWQiOmZhbHNlLCJwcmVmZXJyZWRfdXNlcm5hbWUiOiJhcHBzb25lYWRtaW4ifQ.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()
  /*  def "Update transaction :client validation success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateTransactionsBL().clientValidation(RequestObject)
        then:
        noExceptionThrown()
    }
    def "Update transaction :client validation failure: invalid json"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabld\": false\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateTransactionsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Invalid JSON."
    }
    def "Update SMS: client validation failure:Invalid Account identifier"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateTransactionsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Account Identifier should not be empty."
    }
    def "Update SMS: client validation invalid auth token"() {
        given:

        request.headers() >> new HashSet<>()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateTransactionsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : Authorization Token is empty."
    }
    def "Update transaction :client validation failure: invalid body field value"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 0,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def RequestObject = new RequestObject(request)
        new UpdateTransactionsBL().clientValidation(RequestObject)
        then:
        final e = thrown(ClientException)
        e.getMessage() == "ClientException : {Transaction Id=Transaction id is 0, Transaction Name=Transaction name is null or length is lesser than 3 characters or length is greater than 256 characters}"
    }*/
}

