package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.UserProfilePojo
import spock.lang.Shared
import spock.lang.Specification

class UserProfileServiceTest extends Specification {

    @Shared
    String oldH2URL

    def test_setup(String changableH2URL) {
        String H2URL = changableH2URL

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()

        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(10000)
    }

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()
    }

    def cleanupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        //the following line was added ONLY to ensure that the existing test cases are not impacted.
        MySQLConnectionManager.INSTANCE.getHandle()
    }


    def "getUserProfile"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-user-profile.sql'"
        test_setup(changableH2URL)
        UserProfileBL userProfileBL  = new UserProfileBL()

        when:
        List<UserProfilePojo> output = userProfileBL.processRequestAndGetUserProfileList()
        then:
        output.size() > 0
        output.get(0).getUserProfileId() == 1
        output.get(0).getUserProfileName() == "Super Admin"
        output.get(0).getRole() == "Super Admin"
    }

    def "invalid user getProfileList"() {
        given:
        String changableH2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/serviceDetailsUseCases/populate-user-profile.sql'"
        test_setup(changableH2URL)
        UserProfileBL userProfileBL  = new UserProfileBL()
        when:
        List<UserProfilePojo> data = userProfileBL.processRequestAndGetUserProfileList()

        then:
        data.size() == 5
        data.get(0).role == "Super Admin"
        data.get(0).userProfileName == "Super Admin"
        data.get(0).userProfileId == 1
        data.get(0).accessibleFeatures.size() == 18

        data.get(1).role == "Admin"
        data.get(1).userProfileName == "Heal Admin"
        data.get(1).userProfileId == 2
        data.get(1).accessibleFeatures.size() == 16

        data.get(2).userProfileId == 3
        data.get(2).userProfileName == "Application Owner"
        data.get(2).role == "Admin"
        data.get(2).accessibleFeatures.size() == 14

        data.get(3).userProfileId == 4
        data.get(3).userProfileName == "User Manager"
        data.get(3).role == "User Manager"
        data.get(3).accessibleFeatures.size() == 3

        data.get(4).userProfileId == 5
        data.get(4).userProfileName == "Application User"
        data.get(4).role == "User"
        data.get(4).accessibleFeatures.size() == 7
    }
}
