package com.appnomic.appsone.controlcenter.businesslogic

import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.UserInfo
import com.appnomic.appsone.controlcenter.service.KeyCloakAuthService
import spock.lang.Specification

class UsersBLIT extends Specification {
    def "PopulateAccessDetails"() {
    }

    def "EditServerValidation"() {
    }

    def "getUserDetailsList"() {
        UsersBL usersBL = new UsersBL();
        when:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
        List<UserInfo> list = usersBL.getUserDetailsList()

        then:
        noExceptionThrown()
        list.size() > 0
        list.get(0).userName == 'appsoneadmin'
        list.get(0).status == 1
        list.get(0).roleId == 1
        list.get(0).profileId == 1
        list.get(0).accessDetails.get(0).accountId == "*"

    }

}
