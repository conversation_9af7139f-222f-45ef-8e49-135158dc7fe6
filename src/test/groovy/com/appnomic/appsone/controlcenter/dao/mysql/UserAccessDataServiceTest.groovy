package com.appnomic.appsone.controlcenter.dao.mysql


import spock.lang.Specification

class UserAccessDataServiceTest extends Specification {


    /*@Shared
    String oldH2URL

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setDbi(null)

        oldH2URL = MySQLConnectionManager.INSTANCE.getH2URL()

        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/userManagement/masterData.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/userManagement/testData.sql'"

        MySQLConnectionManager.INSTANCE.setH2URL(H2URL)
        println "H2URL: " + MySQLConnectionManager.INSTANCE.getH2URL()
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true)
        MySQLConnectionManager.INSTANCE.getHandle()
        Thread.sleep(10000)
    }

    def cleanupSpec() {
        DBTestCache.rollback()
        MySQLConnectionManager.INSTANCE.setDbi(null)
        MySQLConnectionManager.INSTANCE.setH2URL(oldH2URL)
        MySQLConnectionManager.INSTANCE.getHandle()
    }

    def "GetUserProfiles"() {
        when:
        List<UserProfileBean> list = UserAccessDataService.getUserProfiles()

        then:
        list.size() == 5
        list.get(0).name == 'Super Admin'
        list.get(2).name == 'Application Owner'
    }

    def "GetRoles"() {
        when:
        List<IdPojo> list = UserAccessDataService.getRoles()

        then:
        list.size() == 3
        list.get(0).name == 'Admin'
        list.get(1).name == 'User Manager'
        list.get(2).name == 'User'
    }*/
}
