package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AccountResponse
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class AccountListServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }
    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()


    def "get account api fail"() {
        given:
        header.add("")
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        request.params() >> parameters
        request.body() >> ""

        when:
        AccountResponse res = AccountService.getAccountList(request, response)

        then:
        response.getStatus() == 400
        res.getResponse_message() == "ClientException : Invalid Authorization Token."
    }

    def "get account api fail null request"() {
        when:
        AccountResponse res = AccountService.getAccountList(null, response)

        then:
        response.getStatus() == 400
        res.getResponse_message() == "ClientException : Invalid Authorization Token."
    }

    def "get account api success"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        req.headers() >> headers.toSet()
        req.queryMap() >> new HashMap<>()
        req.params() >> parameters
        req.body() >> " "
        when:
        AccountResponse res = AccountService.getAccountList(req, response)

        then:
        response.getStatus() == 200
        res.getData() != null
    }
}
