 package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.CategoryDetailBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class AddCategoryServiceIT extends Specification {

    AddCategoryService addCategoryService = new AddCategoryService()
    CategoryDataService categoryDataService = new CategoryDataService()

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1";

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def  remTestCategory(int id) {
        categoryDataService.deleteCategoryById(id,null)
    }

    def addTestCategory() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n," +
                "\"workLoad\": \"true\"\n" +
                "}"
        return addCategoryService.addCategory(request, response).getData() as IdPojo
    }

    def "validate add category valid"() {

        given:

        String name = "category-"+UUID.randomUUID().toString()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \""+name+"\"\n," +
                "\"workLoad\": \"true\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse<IdPojo> genericResponse = addCategoryService.addCategory(request, response)

        then:
        response.status == 200
        genericResponse.message == UIMessages.CATEGORY_ADD_SUCCESS

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<CategoryDetailBean> categories = categoryDataService.getCategoriesForAccount(2)
        categories.parallelStream().anyMatch({ c -> c.getName().equalsIgnoreCase(name) })

        cleanup:
        remTestCategory(genericResponse.getData().getId())
    }

    def "validate add category status inactive"() {

        given:

        String name = "category-"+UUID.randomUUID().toString()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \""+name+"\"\n," +
                "  \"description\": \"category-description\",\n" +
                "  \"subType\": \"Non-info\",\n" +
                "  \"status\": 0\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = addCategoryService.addCategory(request, response)

        then:
        response.status == 200
        genericResponse.message == UIMessages.CATEGORY_ADD_SUCCESS

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<CategoryDetailBean> categories = categoryDataService.getCategoriesForAccount(2)
        categories.parallelStream().anyMatch({ c ->
            (c.getName().equalsIgnoreCase(name)
                    && c.getStatus() == 0)
        })

        cleanup:
        remTestCategory(genericResponse.getData().getId())
    }

    def "validate add category status active"() {

        given:

        String name = "category-"+UUID.randomUUID().toString()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \""+name+"\"\n," +
                "  \"description\": \"category-description\",\n" +
                "  \"subType\": \"Workload\",\n" +
                "  \"status\": 1\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = addCategoryService.addCategory(request, response)

        then:
        response.status == 200
        genericResponse.message == UIMessages.CATEGORY_ADD_SUCCESS

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<CategoryDetailBean> categories = categoryDataService.getCategoriesForAccount(2)
        categories.parallelStream().anyMatch({ c ->
            (c.getName().equalsIgnoreCase(name)
                    && c.getStatus() == 1)
        })

        cleanup:
        remTestCategory(genericResponse.getData().getId())
    }

    def "validate add category info"() {

        given:

        String name = "category-"+UUID.randomUUID().toString()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \""+name+"\"\n," +
                "  \"description\": \"category-description\",\n" +
                "  \"subType\": \"info\",\n" +
                "  \"status\": 1,\n"+
                "\"workLoad\": \"true\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = addCategoryService.addCategory(request, response)

        then:
        response.status == 200
        genericResponse.message == UIMessages.CATEGORY_ADD_SUCCESS

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<CategoryDetailBean> categories = categoryDataService.getCategoriesForAccount(2)
        categories.parallelStream().anyMatch({ c ->
            (c.getName().equalsIgnoreCase(name)
                    && c.getStatus() == 1)
        })

        cleanup:
        remTestCategory(genericResponse.getData().getId())
    }

    def "add category validate name null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": null\n," +
                "\"identifier\": \"testCategory\"\n," +
                "\"workLoad\": \"true\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request. Kindly check the logs."
    }

    def "add category validate name empty"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \" \"\n," +
                "\"identifier\": \"testCategory\"\n," +
                "\"workLoad\": \"true\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request. Kindly check the logs."

    }

    def "add category invalid workload"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n," +
                "\"identifier\": \"testCategory\"\n," +
                "\"workLoad\": \"lala\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = addCategoryService.addCategory(request, response)

        then:
        response.status == 400

        then:
        genericResponse != null && genericResponse.data == null

        then:
        genericResponse.message == "ClientException : Invalid JSON."
        genericResponse.responseStatus == "FAILURE"
    }

    def "add category user invalid"() {

        given:

        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n," +
                "\"identifier\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "add category account invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690--1")
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n," +
                "\"identifier\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Account identifier is invalid"
    }

    def "add category account null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n," +
                "\"identifier\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Account identifier is null or empty."
    }

    def "add category request null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> null
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "add category request empty"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> " "
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
    }

    def "add category request parse error"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory1\"\n," +
                "\"identifier\"\"testCategory1\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid JSON."
    }

    def "validate add category invalid"() {

        given:

        IdPojo category = addTestCategory()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"" + "testCategory" + "\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Category name should be unique in the given account."

        cleanup:
        remTestCategory(category.getId())
    }

    def "validate add category invalid name"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"" + "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25za" +
                "gbhigbikhhoihohhhhkhiohiot7uyfuyfjhguiyoiujiojoih9oyh98ujimioj89tgtcfrsxersdxc" +
                "uygikhbikuyhuiyhihnuihyiyhuijnkGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0" + "\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ClientException : Invalid request. Kindly check the logs."
    }

    def "add category duplicate name"() {

        given:

        IdPojo category = addTestCategory()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = addCategoryService.addCategory(request, response)

        then:
        response.status == 400
        res.message == "ServerException : Category name should be unique in the given account."

        cleanup:
        remTestCategory(category.getId())
    }
}