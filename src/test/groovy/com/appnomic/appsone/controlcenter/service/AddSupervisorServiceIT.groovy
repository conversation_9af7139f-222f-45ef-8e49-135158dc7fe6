package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.SupervisorDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import javax.servlet.http.HttpServletRequest

class AddSupervisorServiceIT extends Specification {

    abstract class DummyHttpServletRequest implements HttpServletRequest {
        @Override
        Map<String, String[]> getParameterMap() {
            return new HashMap<String, String[]>()
        }

        @Override
        Enumeration<String> getHeaderNames() {
            return new Hashtable<String, String>().keys()
        }

    }

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "add supervisor success without identifier"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(request, response)
        then:
        response.status == Constants.SUCCESS_STATUS_CODE
        res.message == "Supervisor successfully added"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Test_20", null)
    }

    def "add supervisor success with identifier"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"supervisorId\": \"Test-Supervisor-Identifier\",\n" +
                "  \"name\": \"Test-Supervisor-2\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"192.168.13.678\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(request, response)
        then:
        response.status == Constants.SUCCESS_STATUS_CODE
        res.getMessage() == "Supervisor successfully added"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Test-Supervisor-2", null)
    }

    def "add supervisor failure - invalid authorization token"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> ""
        request.body() >> "{\n" +
                "  \"name\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ClientException : Invalid authorization token"
    }

    def "add supervisor failure - invalid account"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-11")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Test_20\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ClientException : Account identifier is invalid"
    }

    def "add supervisor failure - duplicate name"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Linux_10\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Supervisor with name Linux_10 already exists"
    }

    def "add supervisor failure - duplicate identifier"() {
        setup:
        Request request = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"supervisorId\": \"Test-Duplicate-SupervisorIdentifier\",\n" +
                "  \"name\": \"Test-Supervisor-Duplicate-SupervisorIdentifier\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(request, response)

        Request req = Spy(Request.class)
        Set<String> headers1 = new HashSet<>()
        headers1.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params1 = new HashMap<>()
        params1.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params1
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"supervisorId\": \"Test-Duplicate-SupervisorIdentifier\",\n" +
                "  \"name\": \"Test-Supervisor-Duplicate-SupervisorId\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        when:
        GenericResponse res1 = new AddSupervisorService().addSupervisor(req, response)
        then:
        response.status == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res1.getMessage() == "ServerException : Supervisor with identifier Test-Duplicate-SupervisorIdentifier already exists"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Test-Supervisor-Duplicate-SupervisorIdentifier", null)
    }

    def "add supervisor failure - duplicate host address"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Windows_584\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true\n" +
                "}"
        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(request, response)
        then:
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Supervisor already exists for host address **************"
    }

    def "add supervisor failure - invalid supervisorTypeName"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Windows_584\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor-Invalid\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL\"\n" +
                "}"
        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(request, response)
        then:
        response.status == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage() == "ServerException : Supervisor type UnixSupervisor-Invalid is invalid"
    }

    def "add supervisor failure - invalid mode"() {
        setup:
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        request.headers() >> headers
        request.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> params
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"name\": \"Windows_584\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"LOCAL-REMOTE\"\n" +
                "}"
        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(request, response)
        then:
        response.status == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage() == "ClientException : Input parameter(s) 'mode' invalid"
    }

    def "add supervisor failure - duplicate mode"() {
        setup:
        Request req = Spy(Request.class)
        Set<String> headers = new HashSet<>()
        headers.add("Authorization")
        req.headers() >> headers
        req.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params = new HashMap<>()
        params.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req.params() >> params
        req.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req.body() >> "{\n" +
                "  \"name\": \"Windows_584\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"REMOTE\"\n" +
                "}"
        new AddSupervisorService().addSupervisor(req, response)

        Request req2 = Spy(Request.class)
        Set<String> headers2 = new HashSet<>()
        headers2.add("Authorization")
        req2.headers() >> headers2
        req2.raw() >> Spy(DummyHttpServletRequest.class)

        Map<String, String> params2 = new HashMap<>()
        params2.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        req2.params() >> params2
        req2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        req2.body() >> "{\n" +
                "  \"name\": \"Windows_5841\",\n" +
                "  \"hostBoxName\": \"localhost\",\n" +
                "  \"supervisorTypeName\": \"UnixSupervisor\",\n" +
                "  \"version\": \"1.0\",\n" +
                "  \"hostAddress\": \"**************\",\n" +
                "  \"status\": true,\n" +
                "  \"mode\": \"REMOTE\"\n" +
                "}"

        when:
        GenericResponse res = new AddSupervisorService().addSupervisor(req2, response)
        then:
        response.status == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage() == "ServerException : Supervisor with mode REMOTE already exists"
        cleanup:
        new SupervisorDataService().deleteSupervisor("Windows_584", null)
    }
}
