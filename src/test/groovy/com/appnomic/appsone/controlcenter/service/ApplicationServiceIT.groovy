package com.appnomic.appsone.controlcenter.service


import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.ITCleanUpDataService
import com.appnomic.appsone.controlcenter.dao.mysql.MasterDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.Controller
import com.appnomic.appsone.controlcenter.pojo.GetApplication
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.controlcenter.pojo.TagMappingDetails
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import java.util.stream.Collectors

class ApplicationServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def addAppIT() {
        Request request = Spy(Request.class)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \""+UUID.toString().toString()+"\" ,\n" +
                "\"services\" : [\"NB-Web-Service\"],\n" +
                "\"timezone\" : \"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\"\n" +
                "}"
        GenericResponse genericResponse = ApplicationService.add(request, response)
        IdPojo data = genericResponse.data as IdPojo
        return data
    }

    def "get application list"() {
        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"

        request.queryParams("Page") >> ""
        request.queryParams("clusterDataRequired") >> "false"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.getApplications(request, response)

        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<GetApplication> apps = genericResponse.data as List<GetApplication>
        apps.size() > 0
    }

    def "get application list account invalid"() {
        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":identifier") >> "d681ef13-d690-17-jkhg-6c79b-1"

        request.queryParams("clusterDataRequired") >> "false"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.getApplications(request, response)

        then:
        response.status == 400

        then:
        genericResponse != null && genericResponse.data == null
    }

    def "get application list user invalid"() {
        setup:
        request.headers("Authorization") >> "xyz"
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.getApplications(request, response)

        then:
        response.status == 400

        then:
        genericResponse != null && genericResponse.data == null
    }

    def "get application list account empty"() {
        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":identifier") >> "  "
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.getApplications(request, response)

        then:
        response.status == 400

        then:
        genericResponse != null && genericResponse.data == null
    }

    def "get application list account null"() {
        setup:
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params(":identifier") >> null
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.getApplications(request, response)

        then:
        response.status == 400

        then:
        genericResponse != null && genericResponse.data == null
    }

    def "add application valid "() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"application_test\" ,\n" +
                "\"timezone\" : \"(GMT+10:00) Hobart\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        IdPojo data = genericResponse.data as IdPojo
        MasterDataService.getControllerList(2).parallelStream()
                .anyMatch({ c -> (c.getIdentifier() == data.identifier) })

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(genericResponse.data.id)
    }

    def "add application valid with identifier"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"application_test\" ,\n" +
                "\"identifier\": \"test_application\" ,\n" +
                "\"timezone\" : \"(GMT+10:00) Hobart\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        IdPojo data = genericResponse.data as IdPojo
        MasterDataService.getControllerList(2).parallelStream()
                .anyMatch({ c -> (c.getIdentifier() == data.identifier) })

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(genericResponse.data.id)
    }

    def "add application valid with services"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"application_test\" ,\n" +
                "\"services\" : [\"NB-Web-Service\",\"NB-User\"],\n" +
                "\"timezone\" : \"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null
        IdPojo data = genericResponse.data as IdPojo
        MasterDataService.getControllerList(2).parallelStream()
                .anyMatch({ c -> (c.getIdentifier() == data.identifier) })
        List<TagMappingDetails> list = MasterDataService.getTagMappingDetails(2)
                .stream().filter({ t -> t.objectId == data.id && t.objectRefTable == Constants.CONTROLLER })
                .collect(Collectors.toList())
        list.size() > 0

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(genericResponse.data.id)
    }

    def "add application request null"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> null
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null

    }

    def "add application invalid account identifier"() {
        setup:
        request.params(":identifier") >> "d681ef13-d0-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"services\" : [\"Core-DB-Service\"]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null
    }

    def "add application invalid name"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"\" ,\n" +
                "\"services\" : [\"Core-DB-Service\"]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null
    }

    def "add application invalid services null"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"services\" : [\"\"]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null
    }

    def "add application invalid identifier"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"identifier\": \"Core-User\" ,\n" +
                "\"services\" : [\"BrAnchTransactions  \",\"Core-DB-Service\"]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null
    }

    def "add application timezone invalid"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"identifier\": \"test_app_identifier\" ,\n" +
                "\"services\" : [\"Core-DB-Service\"],\n" +
                "\"timezone\" : \"(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null
    }

    def "add application user invalid"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUz"
        request.body() >> "{\n" +
                "\"name\": \"test_app\" ,\n" +
                "\"identifier\": \"test_app_identifier\" ,\n" +
                "\"services\" : [\"Core-DB-Service\"]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.add(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null
    }

    def "edit app success for edit application name"() {
        setup:
        IdPojo app = addAppIT()
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationIdentifier") >> app.getIdentifier()
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"edit-app-test\"\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        String appId = genericResponse.data as String
        List<Controller> list = MasterDataService.getControllerList(2)
        list.parallelStream()
                .anyMatch({ c -> (c.appId == (appId) && (c.name == ("edit-app-test"))) })

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(app.id)

    }

    def "edit app add service"() {
        setup:
        IdPojo app = addAppIT()
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationIdentifier") >> app.getIdentifier()
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"addServices\" : [\"NB-User\"]\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<TagMappingDetails> list = MasterDataService.getTagMappingDetails(2)
                .stream().filter({ t -> t.objectId == app.id && t.objectRefTable == Constants.CONTROLLER })
                .collect(Collectors.toList())
        list.size() == 3

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(app.id)

    }

    def "edit app delete service"() {
        setup:
        IdPojo app = addAppIT()
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationIdentifier") >> app.getIdentifier()
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"deleteServices\" : [\"NB-Web-Service\"]\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 200

        then:
        genericResponse != null && genericResponse.data != null

        then:
        List<TagMappingDetails> list = MasterDataService.getTagMappingDetails(2)
                .stream().filter({ t -> t.objectId == app.id && t.objectRefTable == Constants.CONTROLLER })
                .collect(Collectors.toList())
        list.size() == 1

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(app.id)

    }

    def "edit app delete service invalid"() {
        setup:
        IdPojo app = addAppIT()
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationIdentifier") >> app.getIdentifier()
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"deleteServices\" : [\"NB-User\"]\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 400

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(app.id)

    }

    def "edit app add service invalid"() {
        setup:
        IdPojo app = addAppIT()
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationIdentifier") >> app.getIdentifier()
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"addServices\" : [\"service-app\"]\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 400

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(app.id)

    }

    def "edit app invalid name"() {
        setup:
        IdPojo app = addAppIT()
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationIdentifier") >> app.getIdentifier()
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \" \"\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 400

        cleanup:
        ITCleanUpDataService.addApplicationITCleanUp(app.id)

    }

    def "edit app failure request null"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":controllerIdentifier") >> "7c1b3250-2299-4772-ab7c-98c5e8f19a54"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryParams("type") >> "Services"
        request.body() >> null
        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null

    }

    def "edit app failure invalid accountIdentifier"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-17-jkhg-6c79b-10"
        request.params(":applicationIdentifier") >> "7c1bklkl-4772-ab7c-98c4"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"Test_serv2\"\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null
        genericResponse.getMessage() == "Invalid Account Identifier."
    }

    def "edit app failure invalid applicationIdentifier"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationIdentifier") >> "7c1bklkl-4772-ab7c-98c4"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "\"name\": \"Test_serv2\"\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 400

    }

    def "edit app failure invalid userId"() {
        setup:
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":applicationIdentifier") >> "7c1b3250-2299-4772-ab7c-98c5e8f19a54"
        request.headers("Authorization") >> "xyz"
        request.body() >> "{\n" +
                "\"name\": \"Test_serv2\"\n" +
                "}"

        response.status(200)

        when:
        GenericResponse genericResponse = ApplicationService.editApplication(request, response)

        then:
        response.status == 400

        then:
        genericResponse.data == null
        genericResponse.getMessage() == "Validation failure. Invalid user."
    }
}
