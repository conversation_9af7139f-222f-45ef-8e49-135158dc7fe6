package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.AccountDataService
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.CompInstanceEnvDetailsPojo
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import org.apache.commons.lang.RandomStringUtils
import spark.Request
import spark.Response
import spock.lang.Specification

class ComponentInstanceEnvUpdateServiceIT extends Specification {
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)


    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 0\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-APP-HOST-Cluster\",\n" +
                "        \"env\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-DB-HOST-Cluster\",\n" +
                "        \"env\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"NB-Web-Cluster\",\n" +
                "        \"env\": 3\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new ComponentInstanceEnvUpdateService().updateComponentInstanceEnvDetails(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 0\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-APP-HOST-Cluster\",\n" +
                "        \"env\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-DB-HOST-Cluster\",\n" +
                "        \"env\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"NB-Web-Cluster\",\n" +
                "        \"env\": 3\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new ComponentInstanceEnvUpdateService().updateComponentInstanceEnvDetails(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid identifier"() {
        given:
        String identifier = RandomStringUtils.random(16, true, true)
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 0\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-APP-HOST-Cluster\",\n" +
                "        \"env\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"" + identifier + "\",\n" +
                "        \"env\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"NB-Web-Cluster\",\n" +
                "        \"env\": 3\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new ComponentInstanceEnvUpdateService().updateComponentInstanceEnvDetails(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Component Instance Identifier ["+identifier+"] is unavailable"
    }

    def "Server validation failure - invalid env id"() {
        given:
        int envId = 9999999999
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> new HashMap<String, String[]>()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 0\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-APP-HOST-Cluster\",\n" +
                "        \"env\": " + envId + "\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-DB-HOST-Cluster\",\n" +
                "        \"env\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"NB-Web-Cluster\",\n" +
                "        \"env\": 3\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new ComponentInstanceEnvUpdateService().updateComponentInstanceEnvDetails(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        int id = new CompInstanceDataService().getComponentInstanceEnvDetails("RHEL-APP-HOST-Cluster", null)
        id == 0
    }

    def "Success case"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> "[\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 1\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new ComponentInstanceEnvUpdateService().updateComponentInstanceEnvDetails(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getMessage() == UIMessages.COMP_INSTANCE_UPDATE_SUCCESS
        res.getData() == null

        cleanup:
        int accId = AccountDataService.getAccountByIdentifier("d681ef13-d690-4917-jkhg-6c79b-1", null);
        CompInstanceEnvDetailsPojo compInstanceEnvDetailsPojo = new CompInstanceEnvDetailsPojo("RHEL-WEB-HOST-Cluster", 0);
        new CompInstanceDataService().updateEnvDetails(compInstanceEnvDetailsPojo, accId, null);
    }

    def "Success case with Multiple identifier and env id"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> "[\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 0\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-APP-HOST-Cluster\",\n" +
                "        \"env\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-DB-HOST-Cluster\",\n" +
                "        \"env\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"NB-Web-Cluster\",\n" +
                "        \"env\": 3\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new ComponentInstanceEnvUpdateService().updateComponentInstanceEnvDetails(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getMessage() == UIMessages.COMP_INSTANCE_UPDATE_SUCCESS
        res.getData() == null

        cleanup:
        int accId = AccountDataService.getAccountByIdentifier("d681ef13-d690-4917-jkhg-6c79b-1", null);
        List<CompInstanceEnvDetailsPojo> compInstanceEnvDetailsPojoList = new ArrayList<>();
        compInstanceEnvDetailsPojoList.add(new CompInstanceEnvDetailsPojo("RHEL-WEB-HOST-Cluster", 0))
        compInstanceEnvDetailsPojoList.add(new CompInstanceEnvDetailsPojo("RHEL-APP-HOST-Cluster", 0))
        compInstanceEnvDetailsPojoList.add(new CompInstanceEnvDetailsPojo("RHEL-DB-HOST-Cluster", 0))
        compInstanceEnvDetailsPojoList.add(new CompInstanceEnvDetailsPojo("NB-Web-Cluster", 0))
        for(CompInstanceEnvDetailsPojo data : compInstanceEnvDetailsPojoList){
            new CompInstanceDataService().updateEnvDetails(data, accId, null);
        }
    }

    def "Success case with duplicate identifier and env id"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> "[\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 0\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-APP-HOST-Cluster\",\n" +
                "        \"env\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 0\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-WEB-HOST-Cluster\",\n" +
                "        \"env\": 0\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-DB-HOST-Cluster\",\n" +
                "        \"env\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-APP-HOST-Cluster\",\n" +
                "        \"env\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"NB-Web-Cluster\",\n" +
                "        \"env\": 3\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-DB-HOST-Cluster\",\n" +
                "        \"env\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"RHEL-DB-HOST-Cluster\",\n" +
                "        \"env\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"identifier\": \"NB-Web-Cluster\",\n" +
                "        \"env\": 3\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new ComponentInstanceEnvUpdateService().updateComponentInstanceEnvDetails(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getMessage() == UIMessages.COMP_INSTANCE_UPDATE_SUCCESS
        res.getData() == null

        cleanup:
        int accId = AccountDataService.getAccountByIdentifier("d681ef13-d690-4917-jkhg-6c79b-1", null);
        List<CompInstanceEnvDetailsPojo> compInstanceEnvDetailsPojoList = new ArrayList<>();
        compInstanceEnvDetailsPojoList.add(new CompInstanceEnvDetailsPojo("RHEL-WEB-HOST-Cluster", 0))
        compInstanceEnvDetailsPojoList.add(new CompInstanceEnvDetailsPojo("RHEL-APP-HOST-Cluster", 0))
        compInstanceEnvDetailsPojoList.add(new CompInstanceEnvDetailsPojo("RHEL-DB-HOST-Cluster", 0))
        compInstanceEnvDetailsPojoList.add(new CompInstanceEnvDetailsPojo("NB-Web-Cluster", 0))
        for(CompInstanceEnvDetailsPojo data : compInstanceEnvDetailsPojoList){
            new CompInstanceDataService().updateEnvDetails(data, accId, null);
        }
    }
}
