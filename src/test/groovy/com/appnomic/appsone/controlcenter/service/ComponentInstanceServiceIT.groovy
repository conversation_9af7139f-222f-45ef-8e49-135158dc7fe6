package com.appnomic.appsone.controlcenter.service


import spock.lang.Specification

class ComponentInstanceServiceIT extends Specification{
    /*class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def "Add Component Instance API success: add new Component Instance"() {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "[\n " +
                " {\n  " +
                "  \"name\": \"ContainerGroovy\"," +
                "\n    " +
                "\"identifier\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"AIX\"," +
                "\n    " +
                "\"componentVersion\": \"6.1\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"" +
                "\n    " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "}\n" +
                "]"
        when:
        ComponentInstanceService.addComponentInstances(request, response)
        then:
        noExceptionThrown()
    }

    def "Update Component Instance API success"() {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":compInstanceIdentifier") >> "4f51f8b0-b6c9-4375-8729-b21bc6d877777"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

        request.body() >> "{" +
                "\n" +
                "\t\"serviceIdentifiers\": [" +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"]," +
                "\n" +
                "\t\"attributes\":[\n" +
                "\t        \t{" +
                "\n" +
                "\t        \t\"name\":\"HostAddress\"," +
                "\n" +
                "\t        \t\"value\": \"**************\"" +
                "\n\t        \t}" +
                "\n\t" +
                "]" +
                "\n}"
        when:
        ComponentInstanceService.updateComponentInstance(request, response)
        then:
        noExceptionThrown()
    }

    def "Add Component Instance API failure: component Instance already exists"() {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        request.body() >> "[\n " +
                " {\n  " +
                "  \"name\": \"ContainerGroovy\"," +
                "\n    " +
                "\"identifier\": \"4f51f8b0-b6c9-4375-8729-b21bc6d877777\"," +
                "\n    " +
                "\"componentName\": \"AIX\"," +
                "\n    " +
                "\"componentVersion\": \"6.1\"," +
                "\n    " +
                "\"serviceIdentifiers\": [" +
                "\n      " +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"" +
                "\n    " +
                "]," +
                "\n   " +
                " \"agentIdentifiers\": [" +
                "\n      " +
                "\"e570de02-c585-4917-bbb7-5c97b35e-53\"" +
                "\n    " +
                "]," +
                "\n    " +
                "\"discovery\": 1," +
                "\n    " +
                "\"attributes\": [" +
                "\n      {" +
                "\n        " +
                "\"name\": \"HostAddress\"," +
                "\n        " +
                "\"value\": \"************\"" +
                "\n      }\n " +
                "   ]" +
                "\n  " +
                "}\n" +
                "]"
        when:
        def res =   ComponentInstanceService.addComponentInstances(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Component Instance identifier '4f51f8b0-b6c9-4375-8729-b21bc6d877777' already exists."
    }

    def "Delete Component Instance API success"() {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":compInstanceIdentifier") >> "4f51f8b0-b6c9-4375-8729-b21bc6d877777"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        when:
        ComponentInstanceService.remComponentInstance(request, response)
        then:
        noExceptionThrown()
    }

    def "Update Component Instance API failure"() {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":compInstanceIdentifier") >> "4f51f8b0-b6c9-4375-8729-b21bc6d877777"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"

        request.body() >> "{" +
                "\n" +
                "\t\"serviceIdentifiers\": [" +
                "\"4f51f8b0-b6c9-4375-8729-b21bc6d89999\"]," +
                "\n" +
                "\t\"attributes\":[\n" +
                "\t        \t{" +
                "\n" +
                "\t        \t\"name\":\"HostAddress\"," +
                "\n" +
                "\t        \t\"value\": \"**************\"" +
                "\n\t        \t}" +
                "\n\t" +
                "]" +
                "\n}"
        when:
        def res = ComponentInstanceService.updateComponentInstance(request, response)
        then:
        res.getMessage() == "Component Instance Identifier '4f51f8b0-b6c9-4375-8729-b21bc6d877777' does not exist."
        response.getStatus() == 400
    }

    def "Delete Component Instance API failure"() {
        setup:
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":compInstanceIdentifier") >> "4f51f8b0-b6c9-4375-8729-b21bc6d877777"
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCSjhzMnVhazFUUWd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Eesp4gr7sH3wZD1AA-OsWh_yLjaLmTEXmRmp8H6ff8rUCKgqJQBwwTJK6KVbY2YUy9RFPdQXac7ppoYdnA2qL92g8txJyfPy3YGTPkBwyYudHvf6-wzgkocR4UX-GkZKVi1kd1LUEY08VZE1zVg9L4g4lVS04ilBGVTJPSMM6xz1jhVwa3Vr-bylhtd6WzQiSmRMsvT8LIY3o57lM53JB9m-JpY-XVIiV3JLZ-o_n959n9AbIP9Oi-ROsIE-MHXwedJdllJ81bPemNHV5GccBvjxOE7y2lN12K_-4bCYssIkfPX7-iNN_2-Aic9k4rSywB9vtRSUNO84gwHHIdPFDA"
        when:
        def res =  ComponentInstanceService.remComponentInstance(request, response)
        then:
        res.getMessage() == "Component Instance Identifier '4f51f8b0-b6c9-4375-8729-b21bc6d877777' does not exist."
        response.getStatus() == 400
    }*/
}
