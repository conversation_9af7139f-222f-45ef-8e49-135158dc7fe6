package com.appnomic.appsone.controlcenter.service


import com.appnomic.appsone.controlcenter.beans.MasterCommonAttributeBean
import com.appnomic.appsone.controlcenter.beans.MasterComponentTypeBean
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AddComponentRequest
import com.appnomic.appsone.controlcenter.pojo.ComponentAttribute
import com.appnomic.appsone.controlcenter.pojo.ComponentParameters
import com.appnomic.appsone.controlcenter.dao.mysql.ComponentDataService
import org.skife.jdbi.v2.DBI
import org.skife.jdbi.v2.Handle
import spock.lang.Shared
import spock.lang.Specification

class CustomComponentSpec extends Specification{

    @Shared
    AddComponentRequest request = new AddComponentRequest()
    ComponentService service = new ComponentService()
    int accountId = 2
    String userId = "7640123a-fbde-4fe5-9812-581cd1e3a9c1"
    @Shared
    Handle conn = null


    def setupSpec(){
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true)
        DBI dbi = MySQLConnectionManager.getInstance().getHandle()
        Thread.sleep(30000)

        conn = dbi.open()
    }

    def setup(){
        request = getDummyRequest()
    }

    def "validate input params with correct inputs"(){
        when:
        boolean result = request.validate()

        then:
        result
    }

    def "validate input params with invalid component name"(){
        given:
        request.setComponentName("a very very long and invalid name")

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with null component name"(){
        given:
        request.setComponentName(null)

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with null component type"(){
        given:
        request.setType(null)

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid component type"(){
        given:
        request.setType("Invalid type")

        when:
        boolean result = service.validateAddComponentRequest(request, 2)

        then:
        !result
    }

    def "validate input params with null common version"(){
        given:
        request.setCommonVersion(null)

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid common version"(){
        given:
        request.setCommonVersion("the length of this string is greater than 32 characters")

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with null version"(){
        given:
        request.setVersion(null)

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid version"(){
        given:
        request.setVersion("the length of this string is greater than 128 characters " +
                "...............................................................................")

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with null description"(){
        given:
        request.setDescription(null)

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid description"(){
        given:
        request.setDescription("the length of this string is greater than 256 characters " +
                "............................................................................." +
                "............................................................................." +
                ".............................................................................")

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with null attribute name"(){
        given:
        request.getAttributes().get(0).setName(null)

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid attribute name"(){
        given:
        request.getAttributes().get(0).setName("the length of this string is greater than 64 " +
                "characters.............")
        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with null attribute type"(){
        given:
        request.getAttributes().get(0).setType(null)

        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid attribute type"(){
        given:
        request.getAttributes().get(0).setType("the length of this string is greater than 64 " +
                "characters.............")
        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid attribute default value"(){
        given:
        request.getAttributes().get(0).setDefaultValue("the length of this string is greater than 64 " +
                "characters.............")
        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid attribute maxLength"(){
        given:
        request.getAttributes().get(0).setMaxLength(-1)
        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid attribute minLength"(){
        given:
        request.getAttributes().get(0).setMinLength(-1)
        when:
        boolean result = request.validate()

        then:
        !result
    }

    def "validate input params with invalid attribute regex"(){
        given:
        request.getAttributes().get(0).setRegex("the length of this string is greater than 512 " +
                "characters .................................................................." +
                "............................................................................." +
                "............................................................................." +
                "............................................................................." +
                "............................................................................." +
                "............................................................................." +
                ".............................................................................")

        when:
        boolean result = request.validate()

        then:
        !result
    }

/*    def "validate add component to DB with correct input"(){
        when:
        int componentId = service.addComponentToDB(request, accountId, userId, conn)
        String componentName = request.getComponentName()
        MasterComponentBean componentBean = ComponentDataService.getComponent(componentName, conn)

        then:
        componentBean != null
        componentId > 0
    }*/

    def "validate add component to DB with invalid input"(){
        given:
        request.setComponentName("a very very very long component name which is grater than 32 chars")

        when:
        int componentId = service.addComponentToDB(request, accountId, userId, conn)

        then:
        componentId == -1
    }

    /*   def "validate add component type mapping with correct input"(){
           when:
           int componentId = service.addComponentToDB(request, accountId, userId, conn)
           service.addComponentTypeMapping(componentId, request.getType(), accountId, userId, conn)
           MasterComponentTypeBean typeBean = getComponentTypeBean(componentId, request.getType(), conn)

           then:
           typeBean != null
           notThrown(CustomComponentException)
       }*/

/*    def "validate get or add common version"(){
        when:
        int componentId = service.addComponentToDB(request, accountId, userId, conn)
        int commonVersionId = service.getOrAddCommonVersion("5.x", componentId, true,
                accountId, userId, conn)

        CommonVersionBean commonVersionBean = ComponentDataService.getCommonVersion(componentId,
                "5.x", accountId, conn)

        then:
        commonVersionId > 0
        commonVersionBean != null
    }*/

    /*def "validate add component version"(){
        when:
        int componentId = service.addComponentToDB(request, accountId, userId, conn)
        service.addComponentTypeMapping(componentId, request.getType(), accountId, userId, conn)InstanceHealthPageDetailsServiceTest
        int commonVersionId = service.getOrAddCommonVersion("5.x", componentId, true,
                accountId, userId, conn)
        service.addComponentVersion("5.1", commonVersionId, componentId, true,
                userId, accountId, conn)

        MasterComponentTypeBean typeBean = getComponentTypeBean(componentId, request.getType(),conn)
        ViewComponentBean componentBean = ComponentDataService.getComponentDetail(componentId,
                typeBean.getId(), request.getVersion(), conn)

        then:
        notThrown(CustomComponentException)
        componentBean != null
    }*/


    /*   def "validate add component attribute"(){
           when:
           int componentId = service.addComponentToDB(request, accountId, userId, conn)
           service.addComponentTypeMapping(componentId, request.getType(), accountId, userId, conn)
           int commonVersionId = service.getOrAddCommonVersion("5.x", componentId, true,
                   accountId, userId, conn)
           service.addComponentVersion("5.1", commonVersionId, componentId, true,
                   userId, accountId, conn)
           MasterComponentTypeBean typeBean = getComponentTypeBean(componentId,
                   request.getType(),conn)
           ComponentParameters params = new ComponentParameters()
           params.setComponentId(componentId)
           params.setCommonVersionId(commonVersionId)
           params.setComponentTypeId(typeBean.getId())
           params.setCustom(true)
           service.addComponentAttributes(request.getAttributes(), accountId, params, userId, conn)


           then:
           verifyAttributes(request, params, accountId, conn)
           notThrown(CustomComponentException)
       }*/

    static boolean verifyAttributes(AddComponentRequest req, ComponentParameters params,
                                    int accountId, Handle conn){

        if (req.getAttributes() == null){
            return true
        }

        for(ComponentAttribute attribute : req.getAttributes()){
            MasterCommonAttributeBean bean = ComponentDataService
                    .getCommonAttribute(attribute.getName(), accountId, conn)
            if (bean == null){
                return false
            }
            if (!ComponentDataService.isComponentAttributeExists(params.getComponentId(),
                    params.getComponentTypeId(), params.getCommonVersionId(), bean.getId(), conn)){
                return false
            }
        }
        return true
    }


    static MasterComponentTypeBean getComponentTypeBean(int componentId, String componentType,
                                                        Handle handle){
        List<MasterComponentTypeBean> typeBeans = ComponentDataService.getComponentType(
                componentId, handle)
        MasterComponentTypeBean typeBean = null
        for(MasterComponentTypeBean type : typeBeans){
            if (type.getName().equalsIgnoreCase(componentType)){
                typeBean = type
                break
            }
        }
        return typeBean
    }
    static AddComponentRequest getDummyRequest(){
        AddComponentRequest request = new AddComponentRequest()
        request.setComponentName("Test Component-" + new Date().getTime())
        request.setType("Host")
        request.setCommonVersion("5.x")
        request.setVersion("5.1")
        request.setDescription("Test Component")
        ComponentAttribute attribute1 = new ComponentAttribute()
        attribute1.setName("HostAddress")
        attribute1.setType("TextBox")
        attribute1.setMandatory(true)
        attribute1.setMaxLength(255)
        attribute1.setMinLength(1)
        ComponentAttribute attribute2 = new ComponentAttribute()
        attribute2.setName("SshPort")
        attribute2.setType("TextBox")
        attribute2.setMandatory(true)
        attribute2.setDefaultValue("22")
        attribute2.setMaxLength(5)
        attribute2.setMinLength(1)
        List<ComponentAttribute> attributes = new ArrayList<>()
        attributes.add(attribute1)
        attributes.add(attribute2)
        request.setAttributes(attributes)
        return request
    }

}
