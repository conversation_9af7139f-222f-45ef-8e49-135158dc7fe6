package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.entity.NotificationBean
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.util.DateTimeUtil
import com.appnomic.appsone.controlcenter.util.ValidationUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import org.apache.commons.lang.RandomStringUtils
import spark.Request
import spark.Response
import spock.lang.Specification

class DeleteApplicationsServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    String[] t1 = ["2e615f5b-29a9-419a-a4e6-88edb463eff0,deleteApITestApp1"]

    def addApplication(String accountIdentifier, String appName, String appIdentifier) {
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Request request1 = Spy(Request.class)
        DummyResponse response1 = Spy(DummyResponse.class)
        request1.headers() >> header
        request1.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request1.params(Constants.ACCOUNT_IDENTIFIER) >> accountIdentifier
        request1.queryMap() >> new HashMap<String, String[]>()
        request1.body() >> "{\n" +
                " \"name\": \"" + appName + "\",\n" +
                " \"identifier\": \"" + appIdentifier + "\",\n" +
                " \"services\": []\n" +
                "}"
        return ApplicationService.add(request1, response1)
    }

    def addApplicationUserNotification(int appId, int accountId, String authToken) {
        String userId = ValidationUtils.getUserId(authToken);
        List<NotificationBean> addNotificationBeanList = new ArrayList<>();
        Date startDate = DateTimeUtil.getCurrentTimestampInGMT();
        addNotificationBeanList.add(NotificationBean.builder()
                .accountId(accountId)
                .notificationTypeId(291)
                .severityTypeId(295)
                .signalTypeId(312)
                .applicationId(appId)
                .applicableUserId(userId)
                .userDetailsId(userId)
                .status(1)
                .createdTime(startDate)
                .updatedTime(startDate)
                .build())
        NotificationPreferencesDataService.addNotificationDetails(addNotificationBeanList, null);
    }

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t1)
        request.queryMap() >> queryMapData

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >> ""

        when:
        GenericResponse res = new DeleteApplicationsService().deleteApplications(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t1)
        request.queryMap() >> queryMapData

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse res = new DeleteApplicationsService().deleteApplications(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid 'applicationIdentifier'"() {
        given:
        String identifier = RandomStringUtils.random(6, true, true) + "identifier"
        String[] t2 = [identifier]

        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t2)
        request.queryMap() >> queryMapData

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse res = new DeleteApplicationsService().deleteApplications(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Application with Identifier '[" + identifier + "]' does not exist for current user and account."
    }

    def "Server validation failure - unmapped 'applicationIdentifier' from tag-mapping"() {
        given:
        String[] t3 = ["netbanking_1"]

        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t3)
        request.queryMap() >> queryMapData

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse res = new DeleteApplicationsService().deleteApplications(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Some services are still mapped to Application with Identifier '[" + t3[0] + "]'." +
                " Please remove the mapped services first."
    }

    def "Success Case - Without Entry in User-Notification Table"() {
        setup:
        String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        String appName = "test-application-delete-API1"
        String appIdentifier = "deleteApITestApp1"
        String[] t4 = [appIdentifier]

        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", accountIdentifier)
        request.params() >> parameters

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t4)
        request.queryMap() >> queryMapData

        request.body() >> ""

        GenericResponse res1 = addApplication(accountIdentifier, appName, appIdentifier)

        when:
        GenericResponse res2 = new DeleteApplicationsService().deleteApplications(request, response)

        then:
        res1.getResponseStatus() == "SUCCESS"
        res1.getMessage() == UIMessages.APPLICATION_ADD_SUCCESS
        res1.getData().name == appName
        res1.getData().identifier == appIdentifier

        response.getStatus() == 200
        res2.getResponseStatus() == "SUCCESS"
        res2.getMessage() == UIMessages.APPLICATION_REM_SUCCESS
        res2.getData() == null
    }

    def "Success Case"() {
        setup:
        String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        String appName = "test-application-delete-API1"
        String appIdentifier = "deleteApITestApp1"
        String[] t5 = [appIdentifier]

        String accessToken = KeycloakConnectionManager.getAccessToken()
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> accessToken

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", accountIdentifier)
        request.params() >> parameters

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t5)
        request.queryMap() >> queryMapData

        request.body() >> ""

        GenericResponse res1 = addApplication(accountIdentifier, appName, appIdentifier)
        addApplicationUserNotification(res1.data.id, ValidationUtils.validAndGetIdentifier(accountIdentifier), accessToken)

        when:
        GenericResponse res2 = new DeleteApplicationsService().deleteApplications(request, response)

        then:
        res1.getResponseStatus() == "SUCCESS"
        res1.getMessage() == UIMessages.APPLICATION_ADD_SUCCESS
        res1.getData().name == appName
        res1.getData().identifier == appIdentifier

        response.getStatus() == 200
        res2.getResponseStatus() == "SUCCESS"
        res2.getMessage() == UIMessages.APPLICATION_REM_SUCCESS
        res2.getData() == null
    }

    def "Success Case With Multiple applications"() {
        setup:
        String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        String appName1 = "test-application-delete-API1"
        String appIdentifier1 = "deleteApITestApp1"
        String appName2 = "test-application-delete-API2"
        String appIdentifier2 = "deleteApITestApp2"
        String appName3 = "test-application-delete-API3"
        String appIdentifier3 = "deleteApITestApp3"

        String[] t6 = [appIdentifier1 + "," + appIdentifier2 + "," + appIdentifier3]

        String accessToken = KeycloakConnectionManager.getAccessToken()
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> accessToken

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", accountIdentifier)
        request.params() >> parameters

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("appIdentifier", t6)
        request.queryMap() >> queryMapData

        request.body() >> ""

        GenericResponse res1 = addApplication(accountIdentifier, appName1, appIdentifier1)
        GenericResponse res2 = addApplication(accountIdentifier, appName2, appIdentifier2)
        GenericResponse res3 = addApplication(accountIdentifier, appName3, appIdentifier3)
        addApplicationUserNotification(res1.data.id, ValidationUtils.validAndGetIdentifier(accountIdentifier), accessToken)
        addApplicationUserNotification(res2.data.id, ValidationUtils.validAndGetIdentifier(accountIdentifier), accessToken)
        addApplicationUserNotification(res3.data.id, ValidationUtils.validAndGetIdentifier(accountIdentifier), accessToken)

        when:
        GenericResponse data = new DeleteApplicationsService().deleteApplications(request, response)

        then:
        res1.getResponseStatus() == "SUCCESS"
        res1.getMessage() == UIMessages.APPLICATION_ADD_SUCCESS
        res1.getData().name == appName1
        res1.getData().identifier == appIdentifier1

        res2.getResponseStatus() == "SUCCESS"
        res2.getMessage() == UIMessages.APPLICATION_ADD_SUCCESS
        res2.getData().name == appName2
        res2.getData().identifier == appIdentifier2

        res3.getResponseStatus() == "SUCCESS"
        res3.getMessage() == UIMessages.APPLICATION_ADD_SUCCESS
        res3.getData().name == appName3
        res3.getData().identifier == appIdentifier3

        response.getStatus() == 200
        data.getResponseStatus() == "SUCCESS"
        data.getMessage() == UIMessages.APPLICATION_REM_SUCCESS
        data.getData() == null
    }

}
