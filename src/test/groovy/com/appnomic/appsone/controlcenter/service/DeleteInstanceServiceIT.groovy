package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.ComponentInstanceBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.CompInstanceDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.util.DateTimeUtil
import com.appnomic.appsone.controlcenter.util.ValidationUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import org.apache.commons.lang.RandomStringUtils
import spark.Request
import spark.Response
import spock.lang.Specification

class DeleteInstanceServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    String[] t1 = ["2e615f5b-29a9-419a-a4e6-88edb463eff0,deleteApITestInstance1"]

    int addInstanceInCompInstanceOnly(int accId, String instanceName, String instanceIdentifier, String accessToken, int isCluster) {
        ComponentInstanceBean componentInstanceBean = ComponentInstanceBean.builder()
                .name(instanceName)
                .hostId(0)
                .isDR(1)
                .isCluster(isCluster)
                .mstComponentVersionId(1)
                .createdTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .updatedTime(DateTimeUtil.getTimeInGMT(System.currentTimeMillis()))
                .userDetailsId(KeyCloakAuthService.extractUserDetails(accessToken).getSub())
                .accountId(accId)
                .mstComponentId(1)
                .mstComponentTypeId(1)
                .discovery(0)
                .hostAddress("*************")
                .identifier(instanceIdentifier)
                .mstCommonVersionId(1)
                .parentId(0)
                .supervisorId(0)
                .build();
        return new CompInstanceDataService().addComponentInstance(componentInstanceBean, null);
    }

    boolean checkInstancePresence(int accId, String instanceName, String instanceIdentifier) {
        ComponentInstanceBean componentInstanceBean = new CompInstanceDataService().getComponentInstanceBean(instanceIdentifier, instanceName, accId, null);
        return componentInstanceBean != null;
    }

    void removeInstanceInCompInstanceOnly(int instanceId) {
        new CompInstanceDataService().deleteInstance(instanceId, null)
    }

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIdentifier", t1)
        request.queryMap() >> queryMapData

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >> ""

        when:
        GenericResponse res = new DeleteInstanceService().removeInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIdentifier", t1)
        request.queryMap() >> queryMapData

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse res = new DeleteInstanceService().removeInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid 'instanceIdentifier'"() {
        given:
        String identifier = RandomStringUtils.random(6, true, true) + "_InvalidIdentifier"
        String[] t2 = [identifier]

        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIdentifier", t2)
        request.queryMap() >> queryMapData

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        when:
        GenericResponse res = new DeleteInstanceService().removeInstance(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Component Instance Identifier '[" + identifier + "]' " + Constants.DOES_NOT_EXIST
    }

    def "Success Case"() {
        setup:
        String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        int accId = ValidationUtils.validAndGetIdentifier(accountIdentifier)
        String instanceName = "test-instance-delete-API1"
        String instanceIdentifier = "deleteApITestInstance1"
        String[] t5 = [instanceIdentifier]

        String accessToken = KeycloakConnectionManager.getAccessToken()
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> accessToken

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", accountIdentifier)
        request.params() >> parameters

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIdentifier", t5)
        request.queryMap() >> queryMapData

        request.body() >> ""

        int id = addInstanceInCompInstanceOnly(accId, instanceName, instanceIdentifier, accessToken, 0)

        when:
        GenericResponse res2 = new DeleteInstanceService().removeInstance(request, response)

        then:
        response.getStatus() == 200
        res2.getResponseStatus() == "SUCCESS"
        res2.getMessage() == UIMessages.COMP_INSTANCE_REM_SUCCESS
        res2.getData() == null

        !checkInstancePresence(accId, instanceName, instanceIdentifier)

        cleanup:
        removeInstanceInCompInstanceOnly(id)

    }

    def "Success Case With Multiple instance identifiers"() {
        setup:
        String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"
        int accId = ValidationUtils.validAndGetIdentifier(accountIdentifier)
        String instanceName1 = "test-instance-delete-API1"
        String instanceIdentifier1 = "deleteApITestInstance1"
        String instanceName2 = "test-instance-delete-API2"
        String instanceIdentifier2 = "deleteApITestInstance2"
        String instanceName3 = "test-instance-delete-API3"
        String instanceIdentifier3 = "deleteApITestInstance3"

        String[] t6 = [instanceIdentifier1 + "," + instanceIdentifier2 + "," + instanceIdentifier3]

        String accessToken = KeycloakConnectionManager.getAccessToken()
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> accessToken

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", accountIdentifier)
        request.params() >> parameters

        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIdentifier", t6)
        request.queryMap() >> queryMapData

        request.body() >> ""

        int id1 = addInstanceInCompInstanceOnly(accId, instanceName1, instanceIdentifier1, accessToken, 0)
        int id2 = addInstanceInCompInstanceOnly(accId, instanceName2, instanceIdentifier2, accessToken, 1)
        int id3 = addInstanceInCompInstanceOnly(accId, instanceName3, instanceIdentifier3, accessToken, 1)

        when:
        GenericResponse data = new DeleteInstanceService().removeInstance(request, response)

        then:
        response.getStatus() == 200
        data.getResponseStatus() == "SUCCESS"
        data.getMessage() == UIMessages.COMP_INSTANCE_REM_SUCCESS
        data.getData() == null
        !checkInstancePresence(accId, instanceName1, instanceIdentifier1)
        !checkInstancePresence(accId, instanceName2, instanceIdentifier2)
        !checkInstancePresence(accId, instanceName3, instanceIdentifier3)

        cleanup:
        removeInstanceInCompInstanceOnly(id1)
        removeInstanceInCompInstanceOnly(id2)
        removeInstanceInCompInstanceOnly(id3)
    }

}
