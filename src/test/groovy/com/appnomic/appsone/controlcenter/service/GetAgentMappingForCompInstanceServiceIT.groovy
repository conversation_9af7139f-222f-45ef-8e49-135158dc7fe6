package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AgentInstanceMappingDetails
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GetAgentMappingForCompInstanceServiceIT extends Specification {

    GetAgentMappingForCompInstanceService agentMappingForCompInstanceService = new GetAgentMappingForCompInstanceService()

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    String[] instanceIds = ["27,28"]
    String[] zeroInstanceId = ["0"]
    String[] emptyInstanceId = []
    String[] invalidInstanceId = ["9999999"]
    String[] duplicateInstanceIds = ["27,27,28"]
    def "GetAgentMappingForInstance"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIds", instanceIds)
        request.queryMap() >> queryMapData
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse genericResponse = agentMappingForCompInstanceService.getAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == UIMessages.AGENT_INST_MAP_GET_SUCCESS

        then:
        genericResponse != null && genericResponse.data != null
        Set<AgentInstanceMappingDetails> agentMapping = genericResponse.data
        agentMapping.parallelStream().anyMatch({ a -> a.getAgentTypeId() == 1 })
        agentMapping.parallelStream().anyMatch({ a -> a.getAgentId() == 52 })
    }

    def "Get agent mapping: user invalid"() {

        given:

        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIds", instanceIds)
        request.queryMap() >> queryMapData
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.getAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "Get agent mapping: account invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIds", instanceIds)
        request.queryMap() >> queryMapData
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690--1")
        request.params() >> parameters
        request.body() >> ""
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.getAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID)
    }

    def "Get agent mapping: instance Id less than 1"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIds", zeroInstanceId)
        request.queryMap() >> queryMapData
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.getAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage() == "ClientException : InstanceIds cannot be less than 1."
    }

    def "Get agent mapping: instance Id empty"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIds", emptyInstanceId)
        request.queryMap() >> queryMapData
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.getAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage() == "ClientException : InstanceIds is null or empty."
    }

    def "Get agent mapping: instance Id duplicate"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIds", duplicateInstanceIds)
        request.queryMap() >> queryMapData
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.getAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("ClientException : List of instance Ids provided has duplicate Ids")
    }

    def "Get agent mapping: instance Id invalid"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String, String[]> queryMapData = new HashMap<>()
        queryMapData.put("instanceIds", invalidInstanceId)
        request.queryMap() >> queryMapData
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.getAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("ServerException : Invalid instance Ids")
    }
}
