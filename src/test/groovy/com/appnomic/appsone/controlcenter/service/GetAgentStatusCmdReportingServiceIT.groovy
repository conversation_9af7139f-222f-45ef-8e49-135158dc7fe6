package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.AgentStatusDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class GetAgentStatusCmdReportingServiceIT extends Specification{
    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        DummyRequest request = Spy(DummyRequest.class)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String[]> queryMapData = new HashMap<>()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new GetAgentStatusCmdReportingService().getCommandTriggerStatus(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        DummyRequest request = Spy(DummyRequest.class)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"
        Map<String, String[]> queryMapData = new HashMap<>()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new GetAgentStatusCmdReportingService().getCommandTriggerStatus(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid physicalAgentId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        DummyRequest request = Spy(DummyRequest.class)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String[]> queryMapData = new HashMap<>()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 10000,\n" +
                "        \"commandJobId\": \"c40b4a22-d54b-4a98-9ca4-5e10b9d9f69e\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"df4c6052-8c33-4cce-b461-e79ddfb758b0\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new GetAgentStatusCmdReportingService().getCommandTriggerStatus(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid physicalAgentId. Reason: physicalAgentId not mapped to given service."
    }

    def "Server validation failure - invalid commandJobId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        DummyRequest request = Spy(DummyRequest.class)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String[]> queryMapData = new HashMap<>()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 13,\n" +
                "        \"commandJobId\": \"775a5a27-d43a-42b7-a0af-aee3619e9227\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 48,\n" +
                "        \"commandJobId\": \"dummy-command-job-id\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new GetAgentStatusCmdReportingService().getCommandTriggerStatus(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid commandJobId. For more details, refer to application log file."
    }

    /*def "Success Case"() {
        given:
        DummyRequest request = Spy(DummyRequest.class)
        DummyResponse response1 = Spy(DummyResponse.class)
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String[]> queryMapData = new HashMap<>()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    }\n" +
                "]"
        GenericResponse res1 = new TriggerAgentCommandService().triggerAgentCommand(request, response1)
        String commandJobId = res1.getData().get(0).getCommandJobId()

        DummyRequest request2 = Spy(DummyRequest.class)
        DummyResponse response2 = Spy(DummyResponse.class)
        request2.headers() >> header
        request2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request2.queryMap() >> queryMapData
        request2.params() >> parameters
        request2.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 81,\n" +
                "        \"commandJobId\": \""+commandJobId+"\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res2 = new GetAgentStatusCmdReportingService().getCommandTriggerStatus(request2, response2)

        then:
        response2.getStatus() == 200
        res2.getResponseStatus() == "SUCCESS"
        res2.getData().get(0).getAgentId() == 81
        res2.getData().get(0).getAgentTypeId().toString() == "[1]"
        res2.getData().get(0).getIsCommandComplete() == 0
        res2.getData().get(0).getStatusMessage() == "Start action is in progress"
        res2.getData().get(0).getCurrentStatus() == null
        res2.getData().get(0).getDesiredStatus() == "Running"
        res2.getData().get(0).getLastCommandName() == "Start"

        cleanup:
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(81,null)
        AgentStatusDataService.remCommandTrigger(81,commandJobId,null)

    }

    def "Success Case with Batch Jobs"() {
        given:
        DummyRequest request1 = Spy(DummyRequest.class)
        DummyResponse response1 = Spy(DummyResponse.class)
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request1.headers() >> header
        request1.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String[]> queryMapData = new HashMap<>()
        request1.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request1.params() >> parameters
        request1.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"31\",\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]"
        GenericResponse res1 = new TriggerAgentCommandService().triggerAgentCommand(request1, response1)
        String commandJobId1 = res1.getData().get(0).getCommandJobId()
        String commandJobId2 = res1.getData().get(1).getCommandJobId()

        DummyRequest request2 = Spy(DummyRequest.class)
        DummyResponse response2 = Spy(DummyResponse.class)
        request2.headers() >> header
        request2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request2.queryMap() >> queryMapData
        request2.params() >> parameters
        request2.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 81,\n" +
                "        \"commandJobId\": \""+commandJobId1+"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 31,\n" +
                "        \"commandJobId\": \""+commandJobId2+"\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res2 = new GetAgentStatusCmdReportingService().getCommandTriggerStatus(request2, response2)

        then:
        response2.getStatus() == 200
        res2.getResponseStatus() == "SUCCESS"
        res2.getData().get(0).getAgentId() == 81
        res2.getData().get(0).getAgentTypeId().toString() == "[1]"
        res2.getData().get(0).getIsCommandComplete() == 0
        res2.getData().get(0).getStatusMessage() == "Start action is in progress"
        res2.getData().get(0).getCurrentStatus() == null
        res2.getData().get(0).getDesiredStatus() == "Running"
        res2.getData().get(0).getLastCommandName() == "Start"

        println(res2)
        res2.getData().get(1).getAgentId() == 31
        res2.getData().get(1).getAgentTypeId().toString() == "[3]"
        res2.getData().get(1).getIsCommandComplete() == 0
        res2.getData().get(1).getStatusMessage() == "Stop action is in progress"
        res2.getData().get(1).getCurrentStatus() == null
        res2.getData().get(1).getDesiredStatus() == "Stopped"
        res2.getData().get(1).getLastCommandName() == "Stop"

        cleanup:
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(81,null)
        AgentStatusDataService.remCommandTrigger(81,commandJobId1,null)
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(31,null)
        AgentStatusDataService.remCommandTrigger(31,commandJobId2,null)

    }

    def "Success Case with Batch Jobs and Duplicates"() {
        given:
        DummyRequest request1 = Spy(DummyRequest.class)
        DummyResponse response1 = Spy(DummyResponse.class)
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request1.headers() >> header
        request1.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        Map<String, String[]> queryMapData = new HashMap<>()
        request1.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request1.params() >> parameters
        request1.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"31\",\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"31\",\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"31\",\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"31\",\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    }\n" +
                "]"
        GenericResponse res1 = new TriggerAgentCommandService().triggerAgentCommand(request1, response1)
        String commandJobId1 = res1.getData().get(0).getCommandJobId()
        String commandJobId2 = res1.getData().get(1).getCommandJobId()

        DummyRequest request2 = Spy(DummyRequest.class)
        DummyResponse response2 = Spy(DummyResponse.class)
        request2.headers() >> header
        request2.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request2.queryMap() >> queryMapData
        request2.params() >> parameters
        request2.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": 81,\n" +
                "        \"commandJobId\": \""+commandJobId1+"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 31,\n" +
                "        \"commandJobId\": \""+commandJobId2+"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 31,\n" +
                "        \"commandJobId\": \""+commandJobId2+"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 31,\n" +
                "        \"commandJobId\": \""+commandJobId2+"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 81,\n" +
                "        \"commandJobId\": \""+commandJobId1+"\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 81,\n" +
                "        \"commandJobId\": \""+commandJobId1+"\"\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res2 = new GetAgentStatusCmdReportingService().getCommandTriggerStatus(request2, response2)

        then:
        response2.getStatus() == 200
        res2.getResponseStatus() == "SUCCESS"
        res2.getData().get(0).getAgentId() == 81
        res2.getData().get(0).getAgentTypeId().toString() == "[1]"
        res2.getData().get(0).getIsCommandComplete() == 0
        res2.getData().get(0).getStatusMessage() == "Start action is in progress"
        res2.getData().get(0).getCurrentStatus() == null
        res2.getData().get(0).getDesiredStatus() == "Running"
        res2.getData().get(0).getLastCommandName() == "Start"

        println(res2)
        res2.getData().get(1).getAgentId() == 31
        res2.getData().get(1).getAgentTypeId().toString() == "[3]"
        res2.getData().get(1).getIsCommandComplete() == 0
        res2.getData().get(1).getStatusMessage() == "Stop action is in progress"
        res2.getData().get(1).getCurrentStatus() == null
        res2.getData().get(1).getDesiredStatus() == "Stopped"
        res2.getData().get(1).getLastCommandName() == "Stop"

        cleanup:
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(81,null)
        AgentStatusDataService.remCommandTrigger(81,commandJobId1,null)
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(31,null)
        AgentStatusDataService.remCommandTrigger(31,commandJobId2,null)

    }*/

}
