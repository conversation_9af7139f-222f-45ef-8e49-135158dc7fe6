package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.businesslogic.GetAppPercentilesBL
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.ApplicationPercentilesDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.ApplicationPercentiles
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.ACCOUNT_IDENTIFIER
import static com.appnomic.appsone.controlcenter.common.Constants.APPLICATION_ID
import static com.appnomic.appsone.controlcenter.common.Constants.AUTHORIZATION
import static com.appnomic.appsone.controlcenter.common.Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
import static com.appnomic.appsone.controlcenter.common.Constants.SUCCESS_STATUS_CODE

class GetAppPercentilesServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class EmptyUserDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    def dataService = new ApplicationPercentilesDataService()
    def service = new GetAppPercentilesService()


    def "GetAppPercentiles"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(APPLICATION_ID,"1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SUCCESS_STATUS_CODE
        ApplicationPercentiles ap = genericResponse.getData()
        ap.getDefaultPercentile() == GetAppPercentilesBL.getDefaultPercentiles()
        ap.getPercentile().size() == 5

    }

    def "GetAppPercentiles invalid auth token"() {

        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(APPLICATION_ID,"1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid Authorization Token.")

    }

    def "GetAppPercentiles null auth token"() {

        EmptyUserDummyRequest request = Spy(EmptyUserDummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(APPLICATION_ID,"1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid Authorization Token."

    }

    def "GetAppPercentiles null identifier"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,null)
        parameters.put(APPLICATION_ID,"1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Account identifier is null or empty."

    }

    def "GetAppPercentiles empty identifier"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,"")
        parameters.put(APPLICATION_ID,"1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Account identifier is null or empty."

    }

    def "GetAppPercentiles invalid identifier"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,"invalid-identifier")
        parameters.put(APPLICATION_ID,"1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid Account Identifier.")

    }

    def "GetAppPercentiles null applicationId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(APPLICATION_ID,null)
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid path parameter 'applicationId'. Reason: It is either is NULL or empty.")

    }

    def "GetAppPercentiles empty applicationId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(APPLICATION_ID,"")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid path parameter 'applicationId'. Reason: It is either is NULL or empty.")

    }

    def "GetAppPercentiles invalid applicationId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(APPLICATION_ID,"invalid-id")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("'applicationId' is not a positive integer.")

    }

    def "GetAppPercentiles negative applicationId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(APPLICATION_ID,"-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("'applicationId' is not a positive integer.")

    }

    def "GetAppPercentiles invalid application"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER,identifier)
        parameters.put(APPLICATION_ID,"2")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.getAppPercentiles(request,response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Application('applicationId') is not present for the specified account.")

    }

}
