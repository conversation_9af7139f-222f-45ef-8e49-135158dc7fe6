package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.CategoryDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.CategoryDetails
import com.appnomic.appsone.controlcenter.pojo.GetCategory
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GetCategoriesServiceIT extends Specification {

    GetCategoriesService getCategoriesService = new GetCategoriesService()

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "GetCategories"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getCategories(request, response)

        then:
        response.status == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == UIMessages.CATEGORY_GET_SUCCESS

        then:
        genericResponse != null && genericResponse.data != null
        List<CategoryDetails> categories = genericResponse.data
        categories.size() == new CategoryDataService().getCategoriesForAccount(2).size()
        categories.parallelStream().anyMatch({ c -> c.getName() == "CPU" })
        categories.parallelStream().anyMatch({ c -> c.getName() == "DBAvailability" })

    }

    def "Get categories user invalid"() {

        given:

        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse res = getCategoriesService.getCategories(request, response)

        then:
        response.status == 400
        res.getMessage() == "ServerException : Invalid Authorization Token."
    }

    def "Get categories account invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690--1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse res = getCategoriesService.getCategories(request, response)

        then:
        response.status == 400
        res.getMessage().contains(UIMessages.INVALID_ACCOUNT_MESSAGE)
    }

    def "Get categories account null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> "{\n" +
                "\"name\": \"testCategory\"\n," +
                "\"identifier\": \"testCategory\"\n" +
                "}"
        response.status(200)

        when:
        GenericResponse res = getCategoriesService.getCategories(request, response)

        then:
        response.status == 400
        res.getMessage() == "ClientException : Account identifier is null or empty."
    }

    def "Get categories request null"() {

        given:

        response.status(200)

        when:
        getCategoriesService.getCategories(null, response)

        then:
        response.status == 400
    }

    def "GetCategories availability"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String,String[]> queryParams = new HashMap<>()
        String[] type = new String[1]
        type[0] = "Availability"
        queryParams.put("kpiType",type)
        request.queryMap() >> queryParams
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getCategories(request, response)

        then:
        response.status == 200
        genericResponse.getMessage() == "Categories fetched successfully for the account."
        List<CategoryDetails> categories = genericResponse.data
        categories.size() > 0
        categories.parallelStream().noneMatch({ c -> c.getName() == "CPU" })
    }

    def "GetCategories core"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String,String[]> queryParams = new HashMap<>()
        String[] type = new String[1]
        type[0] = "core"
        queryParams.put("kpiType",type)
        request.queryMap() >> queryParams
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getCategories(request, response)

        then:
        response.status == 200
        genericResponse.getMessage() == "Categories fetched successfully for the account."
        List<CategoryDetails> categories = genericResponse.data
        categories.size() > 0
        categories.parallelStream().anyMatch({ c -> c.getName() == "CPU" })

    }

    def "GetCategories forensic"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String,String[]> queryParams = new HashMap<>()
        String[] type = new String[1]
        type[0] = "FORENSIC"
        queryParams.put("kpiType",type)
        request.queryMap() >> queryParams
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getCategories(request, response)

        then:
        response.status == 200
        genericResponse.getMessage() == "Categories fetched successfully for the account."
        List<CategoryDetails> categories = genericResponse.data
        categories.size() > 0

    }

    def "GetCategories ConfigWatch"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String,String[]> queryParams = new HashMap<>()
        String[] type = new String[1]
        type[0] = "ConfigWatch"
        queryParams.put("kpiType",type)
        request.queryMap() >> queryParams
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getCategories(request, response)

        then:
        response.status == 200
        genericResponse.getMessage() == "Categories fetched successfully for the account."
        List<CategoryDetails> categories = genericResponse.data
        categories.size() > 0
        categories.parallelStream().anyMatch({ c -> c.getName() == "Config" })
    }

    def "GetCategories FileWatch"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String,String[]> queryParams = new HashMap<>()
        String[] type = new String[1]
        type[0] = "FileWatch"
        queryParams.put("kpiType",type)
        request.queryMap() >> queryParams
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getCategories(request, response)

        then:
        response.status == 200
        genericResponse.getMessage() == "Categories fetched successfully for the account."
        List<CategoryDetails> categories = genericResponse.data
        categories.size() > 0
        categories.parallelStream().anyMatch({ c -> c.getName() == "Config" })
    }

    def "GetCategories invalid kpi type"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        Map<String,String[]> queryParams = new HashMap<>()
        String[] type = new String[1]
        type[0] = "invalid-kpi-type"
        queryParams.put("kpiType",type)
        request.queryMap() >> queryParams
        parameters.put(Constants.ACCOUNT_IDENTIFIER,"d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = getCategoriesService.getCategories(request, response)

        then:
        response.status == 400
        genericResponse.getMessage().contains(UIMessages.INVALID_KPI_TYPE)
    }
}
