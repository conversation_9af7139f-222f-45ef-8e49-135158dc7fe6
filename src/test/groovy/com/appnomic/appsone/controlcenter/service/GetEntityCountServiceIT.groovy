package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class GetEntityCountServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Map<String, String[]> queryMapData = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    String[] t1 = ["SERVICE"]
    String[] t2 = ["HOST"]
    String[] t3 = ["APPLICATION"]
    String[] t4 = ["APPLICATION_DB"]
    String[] t5 = [""]
    String[] t6 = ["METRICES"]
    String[] t7 = ["CATEGORY"]

    def "get Instances Count api fail"() {
        given:

        String auth_token = ""

        header.add(auth_token)

        request.headers() >> header.toSet()

        queryMapData.put("type", t1)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        request.params() >> parameters

        request.body() >> ""

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.getStatus() == 400

        res.message == "ClientException : Authorization Token is empty."

    }


    def "get Instances Count empty identifier"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t1)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "")

        request.params() >> parameters

        request.body() >> ""

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.getStatus() == 400

        res.message == "ClientException : Account Identifier should not be empty."

    }


    def "get Instances Count empty type"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t5)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        request.params() >> parameters

        request.body() >> ""

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.getStatus() == 400

        res.message == "ClientException : Invalid type name."

    }


    def "get Instances Count api success"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t1)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        request.params() >> parameters

        request.body() >> ""


        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.status == 200

        res.getData() != null


    }


    def "get Instances Count api empty request"() {

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(null, response)


        then:

        response.getStatus() == 400

        res.message == "ClientException : Authorization Token is empty."

    }


    def "get Instances Count api fail invalid author"() {

        given:

        request.headers(Constants.AUTHORIZATION) >> "absbsn"

        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"

        request.queryParams("type") >> "SERVICE"

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.getStatus() == 500

        res.message == "Internal server error, Kindly contact the Administrator."

    }


    def "get Instances Count api success for HOST"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t2)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        request.params() >> parameters

        request.body() >> ""

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.status == 200

        res.getData() != null

    }


    def "get Instances Count api success for Application"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t3)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        request.params() >> parameters

        request.body() >> ""

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.status == 200

        res.getData() != null

    }


    def "get Instances Count api fail invalid identifier"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t3)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-11")

        request.params() >> parameters

        request.body() >> ""

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.getStatus() == 400

        res.message == "ServerException : Invalid account id provided."

    }


    def "get Instances Count api fail invalid type"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t4)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        request.params() >> parameters

        request.body() >> ""

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.getStatus() == 400

        res.message == "ServerException : Invalid type name."

    }

    def "get Instances Count api success for KPI count"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t6)

        request.queryMap() >> queryMapData

        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")

        request.params() >> parameters

        request.body() >> ""


        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.status == 200

        res.getData() != null


    }


    def "get Instances Count api success for Categories"() {

        given:

        header.add(Constants.AUTHORIZATION)

        request.headers() >> header.toSet()

        queryMapData.put("type", t7)

        request.queryMap() >> queryMapData

        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")

        request.params() >> parameters

        request.body() >> ""

        when:

        GetEntityCountService setupService = new GetEntityCountService()

        GenericResponse res = setupService.getEntityCount(request, response)


        then:

        response.status == 200

        res.getData().get("total") > 0

    }


}
