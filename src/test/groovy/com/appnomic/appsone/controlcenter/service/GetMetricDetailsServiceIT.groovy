package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class GetMetricDetailsServiceIT extends Specification{

    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }
    def "service Validation: Internal Server Error"(){
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","123")
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.params() >> parameters
        request.body() >> "{\"instanceIds\":[1,2,3]}"
        when:
        GenericResponse res = new GetMetricDetailsService().getMetricDetailsForService(request,response)
        then:
        response.getStatus() == 500
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "Internal server error, Kindly contact the Administrator."
        res.getData()==null

    }
    def "Service Validation: Error While Fetching Data"(){
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "heal_health")
        parameters.put(":serviceId","1")
        request.headers() >> header
        request.headers("Authorization") >>"eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hIYgmCtAbXr9TTUx1JLbvfTzzISWiOTJWMBeN-b5OjAjAbl4ouhrfAd173KB_9fN1oNjB3snKeTnUShhzAj_ZvHhW-d491UZ22uZT9LpKtnY5X8fMVfKiNnOZfDkYu-XDxzxXz4Yd1UWv4ix_UNvmF86ar7usPJj_M-f-liCJ-0p81bQAlRLyrEiQHQdf2fuZ0zGCKnKsoxPDs2f3sifGqAavN5BVLB_gDuj7-o8irkG7kyBODRwrtYmhQiT-7MgWb5n4pu4HbbvOJfDthJIK9NgPRIwnZ--dTEYjQ_oKIAN4oW7NH7VoNhzlHX42cxSyphyKAV2qUW1CU9EqFHZMA"
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()

        request.body() >> "{\"instanceIds\":[97]}"

        when:
        GenericResponse res = new GetMetricDetailsService().getMetricDetailsForService(request,response)
        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.getMessage() =="ServerException : Invalid service Id. Reason: Service Id specified is unavailable for the account."


    }
    def "Service Validation: Success"(){
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "heal_health")
        parameters.put(":serviceId","49")
        request.headers() >> header
        request.headers("Authorization") >>"eyJhbGciOiJSUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3TWFqem5objVlcXM4ZThfMW80djZ5cXVDZFdUQUdKWVNpRE1hejk5dTFvIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hIYgmCtAbXr9TTUx1JLbvfTzzISWiOTJWMBeN-b5OjAjAbl4ouhrfAd173KB_9fN1oNjB3snKeTnUShhzAj_ZvHhW-d491UZ22uZT9LpKtnY5X8fMVfKiNnOZfDkYu-XDxzxXz4Yd1UWv4ix_UNvmF86ar7usPJj_M-f-liCJ-0p81bQAlRLyrEiQHQdf2fuZ0zGCKnKsoxPDs2f3sifGqAavN5BVLB_gDuj7-o8irkG7kyBODRwrtYmhQiT-7MgWb5n4pu4HbbvOJfDthJIK9NgPRIwnZ--dTEYjQ_oKIAN4oW7NH7VoNhzlHX42cxSyphyKAV2qUW1CU9EqFHZMA"
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()

        request.body() >> "{\"instanceIds\":[97]}"
        response.status(200)
        when:
        GenericResponse res = new GetMetricDetailsService().getMetricDetailsForService(request,response)
        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getMessage() == "Metric details fetched successfully"

    }
}
