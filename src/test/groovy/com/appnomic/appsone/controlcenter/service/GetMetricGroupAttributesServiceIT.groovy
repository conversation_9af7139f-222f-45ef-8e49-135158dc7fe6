package com.appnomic.appsone.controlcenter.service


import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.util.StringUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class GetMetricGroupAttributesServiceIT extends Specification {


    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            String authToken = KeycloakConnectionManager.getAccessToken()
            if (StringUtils.isEmpty(authToken))
                return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCS" +
                        "jhzMnVhazFUUWd3In0.eyJleHAiOjE2MjU4MjM1MjEsImlhdCI6MTYyNTgyMDgyMSwianRpIjoiNmFlNGVjMDEtZDI5N" +
                        "i00ZTQ4LWI2NTQtNzY4Y2YxY2M0NGQxIiwiaXNzIjoiaHR0cHM6Ly8xOTIuMTY4LjEzLjQ0Ojg0NDMvYXV0aC9yZWFsb" +
                        "XMvbWFzdGVyIiwic3ViIjoiNzY0MDEyM2EtZmJkZS00ZmU1LTk4MTItNTgxY2QxZTNhOWMxIiwidHlwIjoiQmVhcmVyI" +
                        "iwiYXpwIjoiYWRtaW4tY2xpIiwic2Vzc2lvbl9zdGF0ZSI6IjkxNjBiOWQyLWI5NjctNGZlOS1hZWFmLTI5N2Q5YTBjO" +
                        "GRlNyIsImFjciI6IjEiLCJhbGxvd2VkLW9yaWdpbnMiOlsiKiJdLCJzY29wZSI6InByb2ZpbGUgZW1haWwiLCJlbWFpb" +
                        "F92ZXJpZmllZCI6ZmFsc2UsInByZWZlcnJlZF91c2VybmFtZSI6ImFwcHNvbmVhZG1pbiIsImVtYWlsIjoiYXBwc29uZW" +
                        "FkbWluLmtAYXBwbm9taWMuY29tIn0.FmNEY7oOraBuukI4ORBzK4TOkdRI9LBKQMiSzJhjhFQ7LIOih5SyoioHKfOI_Kh" +
                        "PaklnCTQSTlBSO7teYTo1M48I0AEOjCf7MMGzIxwm78UrPcJlg_VuNcoHIM2XaPiZxUcKZHVIsC33OuX0uPBf2TKt8GyC" +
                        "nQQoC9CiKHT3HKVG1koDbL-wRCL4hG2_LHQq5z3LqO9u_Cq5QUbJvFE2N2fTt6j8bkKIWK0FtC4CsNCKTtB5fX8m7YhfD" +
                        "2pXy0I-7i-B1fZFnM7DE0uFRScvp4CoPQyHZ11UWAdhPdOewffO9FDpEXfqhYyX195NJSDVfqkrR1Kx1XU-VVirxegD8Q"
            return authToken
        }
    }

    class InvalidTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class NullTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    Map<String, String[]> queryMap = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    GetMetricGroupAttributesService service = new GetMetricGroupAttributesService()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    String[] instanceIds = ["103,104"]


    /*def "GetMetricGroupAttributes"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 8 as String)
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        genericResponse.getData().size() > 0
    }*/

    def "GetMetricGroupAttributes : Auth token NULL"() {
        setup:
        NullTokenDummyRequest nullDummyRequest = Spy(NullTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        nullDummyRequest.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        nullDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        nullDummyRequest.params() >> parameters
        nullDummyRequest.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(nullDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid Authorization Token.")
    }

    def "GetMetricGroupAttributes : Auth token Empty"() {
        setup:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        emptyDummyRequest.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        emptyDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(emptyDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid Authorization Token.")
    }

    def "GetMetricGroupAttributes : Account Identifier NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Account identifier is null or empty.")
    }

    def "GetMetricGroupAttributes : Account Identifier Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, "")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Account identifier is null or empty.")
    }

    def "GetMetricGroupAttributes : Group Id NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, null)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("groupId is null or empty.")
    }

    def "GetMetricGroupAttributes : Group Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, "")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("groupId is null or empty.")
    }

    def "GetMetricGroupAttributes : Instance Ids Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", new String[0])
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("InstanceIds are not specified in queryParams")
    }

    def "GetMetricGroupAttributes : Instance Ids string"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        String[] ids = ["abc"]
        queryMap.put("instanceIds", ids)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("InstanceIds should be positive integers.")
    }

    def "GetMetricGroupAttributes : Instance Ids negative"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        String[] ids = ["-7"]
        queryMap.put("instanceIds", ids)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("InstanceIds cannot be less than 1.")
    }

    def "GetMetricGroupAttributes : Auth token Invalid"() {
        setup:
        InvalidTokenDummyRequest invalidDummyRequest = Spy(InvalidTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        invalidDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "GetMetricGroupAttributes : Account Identifier Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        parameters.put(GROUP_ID, "20")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID)
    }

    /*def "GetMetricGroupAttributes : Group Id Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        queryMap.put("instanceIds", instanceIds)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, "99999")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid groupId.")
    }*/

    def "GetMetricGroupAttributes : Instance Ids invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        String[] ids = ["99999"]
        queryMap.put("instanceIds", ids)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 16 as String)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid instance Id")
    }

    def "GetMetricGroupAttributes : Instance Ids different component"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        String[] ids = ["1,10"]
        queryMap.put("instanceIds", ids)
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.getMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("InstanceIds specified are mapped to different components.")
    }

    /* def "updateMetricGroupAttributes"() {

         given:
         header.add(Constants.AUTHORIZATION)
         request.headers() >> header.toSet()
         queryMap.put("instanceIds", instanceIds)
         request.queryMap() >> queryMap
         parameters.put(Constants.ACCOUNT_IDENTIFIER, "qa-d681ef13-d690-4917-jkhg-6c79b-1")
         parameters.put(Constants.GROUP_ID, 20 as String)
         request.params() >> parameters
         request.body() >> "{\"instanceIds\":[101,102],\"groupAttributes\":[{\"value\":\"check1\",\"oldValue\":null,\"action\":\"ADD\"}]}"
         response.status(200)

         when:
         GenericResponse genericResponse = new UpdateMetricGroupAttributesService().updateMetricGroupAttributes(request, response)

         then:
         response.status == Constants.SUCCESS_STATUS_CODE
     }*/
}

