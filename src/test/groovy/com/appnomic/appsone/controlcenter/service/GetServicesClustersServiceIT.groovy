package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class GetServicesClustersServiceIT extends Specification{

    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    String[] appId1=["1"]
    String[] appId2=["*"]
    String[] appId3=["100000"]

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters

        Map<String, String[]> queryMap = new HashMap()
        queryMap.put("applicationId",appId1)
        request.queryMap() >> queryMap

        request.body() >> ""
        when:
        GenericResponse res = new GetServicesClustersService().getServicesClustersMap(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters

        Map<String, String[]> queryMap = new HashMap()
        queryMap.put("applicationId",appId1)
        request.queryMap() >> queryMap

        request.body() >> ""
        when:
        GenericResponse res = new GetServicesClustersService().getServicesClustersMap(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - queryParameter applicationId as NonExistent Application id"() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters

        Map<String, String[]> queryMap = new HashMap()
        queryMap.put("applicationId",appId3)
        request.queryMap() >> queryMap

        request.body() >> ""

        when:
        GenericResponse res = new GetServicesClustersService().getServicesClustersMap(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Application id ["+appId3[0]+"] is unavailable"
    }

    def "Success Case with queryParameter applicationId as integer"() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters

        Map<String, String[]> queryMap = new HashMap()
        queryMap.put("applicationId",appId1)
        request.queryMap() >> queryMap

        request.body() >> ""

        when:
        GenericResponse res = new GetServicesClustersService().getServicesClustersMap(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getMessage() == UIMessages.SERVICE_CLUSTER_MAP_FETCH_SUCCESS
        res.getData().size() >= 0
    }

    def "Success Case with queryParameter applicationId as '*'"() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters

        Map<String, String[]> queryMap = new HashMap()
        queryMap.put("applicationId",appId2)
        request.queryMap() >> queryMap

        request.body() >> ""

        when:
        GenericResponse res = new GetServicesClustersService().getServicesClustersMap(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getMessage() == UIMessages.SERVICE_CLUSTER_MAP_FETCH_SUCCESS
        res.getData().size() >= 0
    }

}
