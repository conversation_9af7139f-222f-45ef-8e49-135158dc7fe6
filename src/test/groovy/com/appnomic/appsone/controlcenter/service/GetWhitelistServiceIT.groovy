package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class GetWhitelistServiceIT extends Specification{
    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "Invalid Auth-Token"() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "heal_health")
        request.headers() >> header
        request.headers("Authorization") >> "Auth-token"
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> ""

        when:
        GenericResponse res =WhitelistService.getWhitelist(request,response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Invalid Authorization Token."
    }

    def "Invalid Account Id "() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "identifier")
        request.headers() >> header
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw"
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> ""

        when:
        GenericResponse res =WhitelistService.getWhitelist(request,response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == "ServerException : Invalid Account Identifier."
    }

    def "Internal Server Error "() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "heal_health")
        request.headers() >> header
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw"
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()

        when:
        GenericResponse res =WhitelistService.getWhitelist(request,response)

        then:
        response.getStatus() == 500
        res.getResponseStatus() == "FAILURE"
        res.getMessage() == null
    }

    def "SUCCESS Case"() {
        setup:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)

        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "heal_health")
        request.headers() >> header
        request.headers("Authorization") >> "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJybTNrekphSjlJUDFJUFZOT2d2ZmxRQWVud1g2d2MwZTRxQkZOQTdRNzNFIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VLDjRPegxBt7vRAZXBycWBGnzj1YGIr9kbjqiX1Zuvnet4ZbvZdP0a11qx-22v5CsDZBR3vbd85IizzoSkRi3oxXA6uLIXd7BIelkZIvsEHahWbaMiY9qMpkOBZ80wsQ24qNKhkjPegn1cS_w2MDE1QmJzxZgj8SBDFguwinh4Fu6ZHUkGqfEs3u3B1p0W1l81PVLrOZCP6iDTmybdp-msnTseRaDgdHQarmx0A8CWx_xmYm0GJCSp3pKiTObN0vjdr_FxQV5v1DlG-24tTXEfCUoHhyom6oZv5BnpCmTKHUScGU9qY8ezJDckUA97v5r8O3V3uyvXrhZgc-5ZwTlw"
        request.params() >> parameters
        request.queryMap() >> new HashMap<String, String[]>()
        request.body() >> ""

        when:
        GenericResponse res =WhitelistService.getWhitelist(request,response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
    }
}
