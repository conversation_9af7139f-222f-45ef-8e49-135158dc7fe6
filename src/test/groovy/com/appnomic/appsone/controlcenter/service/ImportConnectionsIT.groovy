package com.appnomic.appsone.controlcenter.service


import spock.lang.Specification

class ImportConnectionsIT extends Specification {

/*
    Logger logger = Logger.getLogger("ImportCsvProcessIT")
    Request request = Spy(Request.class)
    ImportFileService service = Spy(ImportFileService.class)
    Response response = new DummyResponse()

    def "validate Import Connection Csv File"() {
        setup:
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        FileProcessDao fileProcessDao = MySQLConnectionManager.getInstance().open(FileProcessDao.class)
        String fileName = "ConnectionsDetails.csv"
        File f1=new File(fileName)
        String file=f1.absolutePath
        request.params(":identifier") >> "d681ef13-d690-4917-jkhg-6c79b-1"
        service.getUserId(request) >> "test-user1"

        request.raw() >> MultiPartUploadHelper.getHttpRequest(file)

        when:
        GenericResponse genericResponse = service.importUploadConnectionFile(request, response)

        then:
        response.status() == 200

        then:
        genericResponse != null && genericResponse.data != null


        String fromService="C24-Service_505"
        String toService="cluster920"
        int accountId=2
        int serviceType=192
        when:

        int sourceId= fileProcessDao.getController(fromService,serviceType,accountId)
        int destId= fileProcessDao.getController(toService,serviceType,accountId)
         then:
        int id=fileProcessDao.getConnectionDetails(sourceId,destId,accountId)
          id>0

        cleanup:

         fileProcessDao.deleteConnection(sourceId,destId,accountId)


    }
    class DummyResponse extends Response {
        int status;

        void status(int i) {
            status = i;
        }

        public int status() {
            return status;
        }
    }
*/
}