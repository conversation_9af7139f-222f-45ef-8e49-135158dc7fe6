package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.StatusResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

class NotificationServiceIT extends Specification {


    class DummyResponse extends Response {
        int status

        @Override
        void status(int i) {
            status = i
        }

        @Override
        int status() {
            return this.status
        }
    }
    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def cleanupSpec() {
        DBTestCache.rollback()
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def"getNotification details"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "appsoneadmin"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = NotificationService.getNotifications(request, response)
        then:
        noExceptionThrown()
        genericResponse.data!=null
    }

    def"getNotification details null request"() {
        when:
        def genericResponse = NotificationService.getNotifications(null, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "Request or request body cannot be NULL or empty."
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def"getNotification details null account"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> ""
        request.params(":userId")>> "appsoneadmin"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = NotificationService.getNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == UIMessages.INVALID_ACCOUNT_MESSAGE
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def"getNotification details invalid token"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "appsoneadmin"
        request.headers("Authorization") >> "123456-1234567"
        when:
        def genericResponse = NotificationService.getNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "Invalid user identifier."
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }

    def"getNotification details invalid userId"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> ""
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = NotificationService.getNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "user should not be empty or NULL"
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def"getNotification details unauthorized userId"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "sumitkumar"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        when:
        def genericResponse = NotificationService.getNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "user does not have access to given account please contact admin."
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def "success save notification api"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId") >> "appsoneadmin"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"preferences\": [\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 297,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 293\n" +
                "    },\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 295,\n" +
                "      \"notificationTypeId\": 292\n" +
                "    }\n" +
                "    ,\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 294\n" +
                "    }\n" +
                "  ],\n" +
                "  \"emailNotification\": true,\n" +
                "  \"smsNotification\": true\n" +
                "}"
        when:
        def genericResponse  = NotificationService.saveNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.SUCCESS.name()
        genericResponse.getMessage() == "Notification Preferences saved Successfully"
        response.getStatus() == Constants.SUCCESS_STATUS_CODE
    }

    def"saveNotification null body"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "appsoneadmin"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> null
        when:
        def genericResponse = NotificationService.saveNotifications(null, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "Request or request body cannot be NULL or empty."
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def"saveNotification null request"() {
        when:
        def genericResponse = NotificationService.saveNotifications(null, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "Request or request body cannot be NULL or empty."
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def "save notification api invalid fieldName -> notificationTypeIds"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "appsoneadmin"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"preferences\": [\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 297,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeIds\": 293\n" +
                "    },\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 295,\n" +
                "      \"notificationTypeId\": 292\n" +
                "    }\n" +
                "    ,\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 294\n" +
                "    }\n" +
                "  ],\n" +
                "  \"emailNotification\": true,\n" +
                "  \"smsNotification\": true\n" +
                "}"
        when:
        def genericResponse  = NotificationService.saveNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == UIMessages.INVALID_REQUEST_BODY_FIELDS
        response.getStatus() == Constants.INTERNAL_SERVER_ERROR_STATUS_CODE
    }
    def "save notification api invalid fieldValue -> notificationTypeIds"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "appsoneadmin"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"preferences\": [\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 297,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": \"null\" \n" +
                "    },\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 295,\n" +
                "      \"notificationTypeId\": 292\n" +
                "    }\n" +
                "    ,\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 294\n" +
                "    }\n" +
                "  ],\n" +
                "  \"emailNotification\": true,\n" +
                "  \"smsNotification\": true\n" +
                "}"
        when:
        def genericResponse  = NotificationService.saveNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "Error while updating user notification mapping from DB"
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }

    def"saveNotification details null account"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> ""
        request.params(":userId")>> "appsoneadmin"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"preferences\": [\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 297,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 293\n" +
                "    },\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 295,\n" +
                "      \"notificationTypeId\": 292\n" +
                "    }\n" +
                "    ,\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 294\n" +
                "    }\n" +
                "  ],\n" +
                "  \"emailNotification\": true,\n" +
                "  \"smsNotification\": true\n" +
                "}"
        when:
        def genericResponse = NotificationService.saveNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == UIMessages.INVALID_ACCOUNT_MESSAGE
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def"saveNotification details invalid token"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "appsoneadmin"
        request.headers("Authorization") >> "123456-1234567"
        request.body() >> "{\n" +
                "  \"preferences\": [\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 297,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 293\n" +
                "    },\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 295,\n" +
                "      \"notificationTypeId\": 292\n" +
                "    }\n" +
                "    ,\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 294\n" +
                "    }\n" +
                "  ],\n" +
                "  \"emailNotification\": true,\n" +
                "  \"smsNotification\": true\n" +
                "}"
        when:
        def genericResponse = NotificationService.saveNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "Invalid user identifier."
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }

    def"saveNotification details invalid userId"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> ""
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"preferences\": [\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 297,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 293\n" +
                "    },\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 295,\n" +
                "      \"notificationTypeId\": 292\n" +
                "    }\n" +
                "    ,\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 294\n" +
                "    }\n" +
                "  ],\n" +
                "  \"emailNotification\": true,\n" +
                "  \"smsNotification\": true\n" +
                "}"
        when:
        def genericResponse = NotificationService.saveNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "user should not be empty or NULL"
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def"saveNotification details unauthorized userId"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "sumitkumar"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"preferences\": [\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 297,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 293\n" +
                "    },\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 295,\n" +
                "      \"notificationTypeId\": 292\n" +
                "    }\n" +
                "    ,\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 294\n" +
                "    }\n" +
                "  ],\n" +
                "  \"emailNotification\": true,\n" +
                "  \"smsNotification\": true\n" +
                "}"
        when:
        def genericResponse = NotificationService.saveNotifications(request, response)
        then:
        genericResponse.getResponseStatus() == StatusResponse.FAILURE.name()
        genericResponse.getMessage() == "user does not have access to given account please contact admin."
        response.getStatus() == Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
    }
    def "update save notification api"() {
        given:
        request.params(Constants.ACCOUNT_IDENTIFIER) >> "d681ef13-d690-4917-jkhg-6c79b-1"
        request.params(":userId")>> "ravi"
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.body() >> "{\n" +
                "  \"preferences\": [\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 297,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 293\n" +
                "    },\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 295,\n" +
                "      \"notificationTypeId\": 292\n" +
                "    }\n" +
                "    ,\n" +
                "    {\n" +
                "      \"applicationId\": 1,\n" +
                "      \"signalTypeId\": 298,\n" +
                "      \"severityTypeId\": 296,\n" +
                "      \"notificationTypeId\": 294\n" +
                "    }\n" +
                "  ],\n" +
                "  \"emailNotification\": true,\n" +
                "  \"smsNotification\": true\n" +
                "}"
        when:
        NotificationService.saveNotifications(request, response)
        then:
        noExceptionThrown()
    }



}