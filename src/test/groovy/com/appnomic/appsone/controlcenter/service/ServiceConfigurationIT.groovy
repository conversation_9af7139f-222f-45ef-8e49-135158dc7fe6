package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.businesslogic.ServiceConfigurationBL
import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.ServiceDataService
import com.appnomic.appsone.controlcenter.dao.mysql.entity.AccountBean
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AccountServiceKey
import com.appnomic.appsone.controlcenter.pojo.ServiceConfigDetails
import com.appnomic.appsone.controlcenter.util.ValidationUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class ServiceConfigurationIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }

        int status() {
            return status
        }
    }

    Request request = Spy(Request.class)
    Response response = new DummyResponse()

    ServiceConfiguration service = Spy(ServiceConfiguration.class)
    ServiceConfigurationBL bl = Spy(ServiceConfigurationBL.class)

    @Shared
    String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    String authToken = KeycloakConnectionManager.getAccessToken()
    String serviceId = "2"
    AccountBean account = ValidationUtils.validAndGetAccount(accountIdentifier)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def cleanupSpec() {
        DBTestCache.rollback()
    }

    def deleteServiceConfigurations(int serviceId) {
        int accountId = ValidationUtils.validAndGetAccount(accountIdentifier).getId()
        new ServiceDataService().deleteServiceConfigurations(accountId, serviceId,null)
    }

    def getUpdateRequest() {
        return bl.getServiceConfigurationDetails(new AccountServiceKey(account, "test-user", Integer.parseInt(serviceId)))
    }

    private static String convertToString(Object object) {
        Gson gson = new GsonBuilder().create()
        return gson.toJson(object)
    }


    def "GetServiceConfigurationDetails request null"() {

        setup:
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(null, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Request should not be NULL"
    }

    def "GetServiceConfigurationDetails auth token empty"() {

        setup:
        request.headers(AUTHORIZATION) >> ""
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == UIMessages.AUTH_KEY_EMPTY
    }

    def "GetServiceConfigurationDetails acc identifier empty"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> ""
        request.params(SERVICE_ID) >> serviceId
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == UIMessages.ACCOUNT_EMPTY
    }

    def "GetServiceConfigurationDetails service id empty"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Service id should not be empty"
    }

    def "GetServiceConfigurationDetails service id negative"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> "-3"
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Service id should be greater than 0."
    }

    def "GetServiceConfigurationDetails service id not an integer"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> "abc"
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Service Id should be a positive integer."
    }

    def "GetServiceConfigurationDetails user invalid"() {

        setup:
        request.headers(AUTHORIZATION) >> "invalid-user-token"
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Invalid authorization token."
    }

    def "GetServiceConfigurationDetails account invalid"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> "invalid-account-identifier"
        request.params(SERVICE_ID) >> serviceId
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Invalid account identifier. Reason: Account identifier provided is unavailable."
    }

    def "GetServiceConfigurationDetails service invalid"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> "9999"
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Invalid service id. Reason: Service id provided is unavailable."
    }

    def "GetServiceConfigurationDetails"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 200

        Map<String, ServiceConfigDetails> map = genericResponse.getData()
        map.size() == 2
        map.containsKey(OPERATOR_LESS_THAN) && map.containsKey(OPERATOR_GREATER_THAN)

        ServiceConfigDetails detailsLT = map.get(OPERATOR_LESS_THAN)
        detailsLT.getServiceConfigId() > 0
        detailsLT.getSorPersistence().toString() == SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR
        detailsLT.getSorSuppression().toString() == SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR
        detailsLT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR
        detailsLT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR

        ServiceConfigDetails detailsGT = map.get(OPERATOR_GREATER_THAN)
        detailsGT.getServiceConfigId() > 0
        detailsGT.getSorPersistence().toString() == SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR
        detailsGT.getSorSuppression().toString() == SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR
        detailsGT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR
        detailsGT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR

    }

    def "GetServiceConfigurationDetails service not configured - default configurations will be added"() {

        setup:
        deleteServiceConfigurations(Integer.parseInt(serviceId))
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServiceConfigurationDetails(request, response)

        then:
        response.status() == 200

        Map<String, ServiceConfigDetails> map = genericResponse.getData()
        map.size() == 2
        map.containsKey(OPERATOR_LESS_THAN) && map.containsKey(OPERATOR_GREATER_THAN)

        ServiceConfigDetails detailsLT = map.get(OPERATOR_LESS_THAN)
        detailsLT.getServiceConfigId() > 0
        detailsLT.getSorPersistence().toString() == SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR
        detailsLT.getSorSuppression().toString() == SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR
        detailsLT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR
        detailsLT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR

        ServiceConfigDetails detailsGT = map.get(OPERATOR_GREATER_THAN)
        detailsGT.getServiceConfigId() > 0
        detailsGT.getSorPersistence().toString() == SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR
        detailsGT.getSorSuppression().toString() == SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR
        detailsGT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR
        detailsGT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR

    }

    def "UpdatePersistenceSuppression request null"() {

        setup:
        request.headers(AUTHORIZATION) >> ""
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == UIMessages.AUTH_KEY_EMPTY
    }

    def "UpdatePersistenceSuppression acc identifier empty"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> ""
        request.params(SERVICE_ID) >> serviceId
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == UIMessages.ACCOUNT_EMPTY
    }

    def "UpdatePersistenceSuppression service id empty"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Service id should not be empty"
    }

    def "UpdatePersistenceSuppression auth token empty"() {

        setup:
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(null, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Request should not be NULL"
    }

    def "UpdatePersistenceSuppression service id negative"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> "-3"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Service id should be greater than 0."
    }

    def "UpdatePersistenceSuppression service id not an integer"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> "abc"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Service Id should be a positive integer."
    }

    def "UpdatePersistenceSuppression user invalid"() {

        setup:
        request.headers(AUTHORIZATION) >> "invalid-user-token"
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        request.body() >> convertToString(getUpdateRequest())
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Invalid authorization token."
    }

    def "UpdatePersistenceSuppression account invalid"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> "invalid-account-identifier"
        request.params(SERVICE_ID) >> serviceId
        request.body() >> convertToString(getUpdateRequest())
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Invalid account identifier. Reason: Account identifier provided is unavailable."
    }

    def "UpdatePersistenceSuppression service invalid"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> "9999"
        request.body() >> convertToString(getUpdateRequest())
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Invalid service id. Reason: Service id provided is unavailable."
    }

    def "UpdatePersistenceSuppression request body null"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        request.body() >> null
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Request body should not be NULL"
    }

    def "UpdatePersistenceSuppression json invalid"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        request.body() >> "{" +
                    "\"lt\":{" +
                        "\"serviceConfigId\":159," +
                        "\"sorPersistence\":5," +
                        "\"sorSuppression\":10," +
                        "\"norPersistence\":2," +
                        "\"norSuppression\":5" +
                    "}," +
                    "\"gte\":{" +
                        "\"serviceConfigId\":160," +
                        "\"sorPersistence\":2," +
                        "\"sorSuppression\":5," +
                        "\"norPersistence\":2," +
                        "\"norSuppression\":5" +
                    "}," +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Request body parsing failure"
    }

    def "UpdatePersistenceSuppression json empty"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        request.body() >> "{}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage() == "Request object should contain service configuration details."
    }

    def "UpdatePersistenceSuppression sor within an hour"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        Map<String,ServiceConfigDetails> obj = getUpdateRequest()
        obj.get(OPERATOR_LESS_THAN).setSorPersistence(9)
        obj.get(OPERATOR_LESS_THAN).setSorSuppression(3)
        request.body() >> convertToString(obj)
        response.status(200)

        when:
        service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 200

        then:
        Map<String,ServiceConfigDetails> map = bl.getServiceConfigurationDetails(new
                AccountServiceKey(account, "test-user", Integer.parseInt(serviceId)))

        map.size() == 2
        map.containsKey(OPERATOR_LESS_THAN) && map.containsKey(OPERATOR_GREATER_THAN)

        ServiceConfigDetails detailsLT = map.get(OPERATOR_LESS_THAN)
        detailsLT.getServiceConfigId() > 0
        detailsLT.getSorPersistence() == 9
        detailsLT.getSorSuppression() == 3
        detailsLT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR
        detailsLT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR

        ServiceConfigDetails detailsGT = map.get(OPERATOR_GREATER_THAN)
        detailsGT.getServiceConfigId() > 0
        detailsGT.getSorPersistence().toString() == SERVICE_SOR_PERSISTENCE_MORE_THAN_AN_HOUR
        detailsGT.getSorSuppression().toString() == SERVICE_SOR_SUPPRESSION_MORE_THAN_AN_HOUR
        detailsGT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR
        detailsGT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR

        cleanup:
        deleteServiceConfigurations(Integer.parseInt(serviceId))
    }

    def "UpdatePersistenceSuppression configurations unavailable"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        Map<String,ServiceConfigDetails> obj = getUpdateRequest()
        obj.get(OPERATOR_GREATER_THAN).setSorPersistence(8)
        obj.get(OPERATOR_GREATER_THAN).setSorSuppression(4)
        request.body() >> convertToString(obj)
        deleteServiceConfigurations(Integer.parseInt(serviceId))
        response.status(200)

        when:
        GenericResponse genericResponse = service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 400
        genericResponse.getMessage().contains("Invalid ServiceConfigId")

        cleanup:
        bl.getServiceConfigurationDetails(new
                AccountServiceKey(account, "test-user", Integer.parseInt(serviceId)))

    }

    def "UpdatePersistenceSuppression sor more than an hour"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        Map<String,ServiceConfigDetails> obj = getUpdateRequest()
        obj.get(OPERATOR_GREATER_THAN).setSorPersistence(8)
        obj.get(OPERATOR_GREATER_THAN).setSorSuppression(4)
        request.body() >> convertToString(obj)
        response.status(200)

        when:
        service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 200

        then:
        Map<String,ServiceConfigDetails> map = bl.getServiceConfigurationDetails(new
                AccountServiceKey(account, "test-user", Integer.parseInt(serviceId)))

        map.size() == 2
        map.containsKey(OPERATOR_LESS_THAN) && map.containsKey(OPERATOR_GREATER_THAN)

        ServiceConfigDetails detailsLT = map.get(OPERATOR_LESS_THAN)
        detailsLT.getServiceConfigId() > 0
        detailsLT.getSorPersistence().toString() == SERVICE_SOR_PERSISTENCE_WITHIN_AN_HOUR
        detailsLT.getSorSuppression().toString() == SERVICE_SOR_SUPPRESSION_WITHIN_AN_HOUR
        detailsLT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR
        detailsLT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR

        ServiceConfigDetails detailsGT = map.get(OPERATOR_GREATER_THAN)
        detailsGT.getServiceConfigId() > 0
        detailsGT.getSorPersistence() == 8
        detailsGT.getSorSuppression() == 4
        detailsGT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR
        detailsGT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR

        cleanup:
        deleteServiceConfigurations(Integer.parseInt(serviceId))
    }

    def "UpdatePersistenceSuppression"() {

        setup:
        request.headers(AUTHORIZATION) >> authToken
        request.params(ACCOUNT_IDENTIFIER) >> accountIdentifier
        request.params(SERVICE_ID) >> serviceId
        Map<String,ServiceConfigDetails> obj = getUpdateRequest()
        obj.get(OPERATOR_LESS_THAN).setSorPersistence(9)
        obj.get(OPERATOR_LESS_THAN).setSorSuppression(3)
        obj.get(OPERATOR_GREATER_THAN).setSorPersistence(8)
        obj.get(OPERATOR_GREATER_THAN).setSorSuppression(4)
        request.body() >> convertToString(obj)
        response.status(200)

        when:
        service.updatePersistenceSuppression(request, response)

        then:
        response.status() == 200

        then:
        Map<String,ServiceConfigDetails> map = bl.getServiceConfigurationDetails(new
                AccountServiceKey(account, "test-user", Integer.parseInt(serviceId)))

        map.size() == 2
        map.containsKey(OPERATOR_LESS_THAN) && map.containsKey(OPERATOR_GREATER_THAN)

        ServiceConfigDetails detailsLT = map.get(OPERATOR_LESS_THAN)
        detailsLT.getServiceConfigId() > 0
        detailsLT.getSorPersistence() == 9
        detailsLT.getSorSuppression() == 3
        detailsLT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_WITHIN_AN_HOUR
        detailsLT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_WITHIN_AN_HOUR

        ServiceConfigDetails detailsGT = map.get(OPERATOR_GREATER_THAN)
        detailsGT.getServiceConfigId() > 0
        detailsGT.getSorPersistence() == 8
        detailsGT.getSorSuppression() == 4
        detailsGT.getNorPersistence().toString() == SERVICE_NOR_PERSISTENCE_MORE_THAN_AN_HOUR
        detailsGT.getNorSuppression().toString() == SERVICE_NOR_SUPPRESSION_MORE_THAN_AN_HOUR

        cleanup:
        deleteServiceConfigurations(Integer.parseInt(serviceId))
    }

}
