package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.ServicePageAttribute
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class ServicePageAttributesServiceIT extends Specification {


    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    ServicePageAttributesService service = new ServicePageAttributesService()

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "GetServicePageAttributes add service"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(SERVICE_ID, "0")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE

        then:
        genericResponse != null && genericResponse.getData() != null

        then:
        List<ServicePageAttribute> list = genericResponse.getData()
        list.size() == 6

    }

    def "GetServicePageAttributes update service"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(SERVICE_ID, "2")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE

        then:
        genericResponse != null && genericResponse.getData() != null

        then:
        List<ServicePageAttribute> list = genericResponse.getData()
        list.size() == 5

    }

    def "GetServicePageAttributes request null"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(SERVICE_ID, "0")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(null, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE

    }

    def "GetServicePageAttributes token empty"() {

        given:
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(SERVICE_ID, "0")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid Authorization Token.")

    }

    def "GetServicePageAttributes identifier null"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, null)
        parameters.put(SERVICE_ID, "0")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Account identifier is null or empty")

    }

    def "GetServicePageAttributes serviceId null"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("serviceId is null or empty.")

    }

    def "GetServicePageAttributes identifier empty"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, " ")
        parameters.put(SERVICE_ID, "0")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Account identifier is null or empty")

    }

    def "GetServicePageAttributes serviceId empty"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(SERVICE_ID, " ")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("serviceId is null or empty.")

    }

    def "GetServicePageAttributes service identifier not valid"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(SERVICE_ID, "service-identifier")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Service Id is not an integer")

    }

    def "GetServicePageAttributes token invalid"() {

        given:
        InvalidDummyRequest invalidDummyRequest = Spy(InvalidDummyRequest.class)
        header.add(AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(SERVICE_ID, "0")
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid Authorization Token.")

    }

    def "GetServicePageAttributes identifier invalid"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        parameters.put(SERVICE_ID, "0")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains(UIMessages.INVALID_ACCOUNT_MESSAGE)

    }

    def "GetServicePageAttributes serviceId invalid"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, accountIdentifier)
        parameters.put(SERVICE_ID, "1")
        request.params() >> parameters
        request.body() >> ""
        response.status(200)

        when:
        GenericResponse genericResponse = service.getServicePageAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid service id provided")

    }


}
