package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class TransactionCountServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()
    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }
    def cleanup(){
        DBTestCache.rollback()
    }
    def "Transaction count api Failure: Account identifier invalid"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-10")
        parameters.put(":serviceId","14")
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new TransactionCountService().transactionCount(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching transaction count"
    }
    def "Transaction count api Failure: User token identifier invalid"() {
        given:
        InvalidDummyRequest invalidRequest = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        invalidRequest.headers() >> header.toSet()
        invalidRequest.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","14")
        invalidRequest.params() >> parameters
        invalidRequest.body() >> ""
        when:
        def res = new TransactionCountService().transactionCount(invalidRequest, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching transaction count"
    }
    def "Transaction count api Success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "c681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","44")
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new TransactionCountService().transactionCount(request, response)
        then:
        response.getStatus() == 200
        res.getMessage() == "Service summary fetched successfully"
    }
    def "Transaction count api failure due to non associated service id"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "c681ef13-d690-4917-jkhg-6c79b-1")
        parameters.put(":serviceId","440")
        request.params() >> parameters
        request.body() >> ""
        when:
        def res = new TransactionCountService().transactionCount(request, response)
        then:
        response.getStatus() == 400
        res.getMessage() == "Error while fetching transaction count"
    }
}
