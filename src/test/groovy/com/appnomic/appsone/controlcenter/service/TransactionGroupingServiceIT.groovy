package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class TransactionGroupingServiceIT extends Specification{
    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }
    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    def "getTransactionTags: no exception"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690-4917-jkhg-6c79b-1")
        req.headers() >> headers.toSet()
        req.queryMap() >> new HashMap<>()
        req.params() >> parameters
        req.body() >> " "
        when:
        GenericResponse res = TransactionGroupingService.getTransactionTags(req, response)

        then:
        response.getStatus() == 200
        res.getData() != null
    }

    def "getTransactionTags: Request exception"() {
        given:
        Request req = Spy(DummyRequest.class)
        Set<String> headers = new HashSet<>()
        headers.add(Constants.AUTHORIZATION)
        parameters.put(Constants.ACCOUNT_IDENTIFIER, null)
        req.headers() >> headers.toSet()
        req.queryMap() >> new HashMap<>()
        req.params() >> parameters
        req.body() >> " "
        when:
        GenericResponse res = TransactionGroupingService.getTransactionTags(req, response)

        then:
        res.getMessage() == "Internal server error, Kindly contact the Administrator."
    }

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }
}
