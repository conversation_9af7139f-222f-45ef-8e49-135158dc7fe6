package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.cache.DBTestCache
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class TransactionUpdateServiceIT extends Specification {


    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Map<String, String> parameters = new HashMap<>()
    @Shared
    Set<String> header = new HashSet<>()

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(false)
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def cleanup() {
        DBTestCache.rollback()
    }

   /* def "Update Transaction Success"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "c681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[{\"id\":23,\"addAuditDetails\":[{\"displayName\":\"name 3\",\"variable\":\"value-3\"}],\"modifyAuditDetails\":[{\"id\":8,\"displayName\":\"display 1\"}],\"deleteAuditDetails\":[7],\"monitorEnabled\":1,\"auditEnabled\":1}]"

        when:
        def genericResponse = new UpdateTransactionService().update(request, response)
        then:
        response.status == 200
        genericResponse.getMessage() == "Records updated successfully"
        then:
        new UpdateTransactionService().update(request, response)

    }*/

    /*def "Update Transaction Success revert"() {
        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "c681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": true\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": true\n" +
                "  }\n" +
                "]"

        when:
        def genericResponse = new UpdateTransactionService().update(request, response)
        then:
        response.status == 200
        genericResponse.getMessage() == "Records updated successfully"
        then:
        new UpdateTransactionService().update(request, response)

    }

    def "Update Transaction api failure due to account not exists"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-5")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def genericResponse = new UpdateTransactionService().update(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating transaction configuration"
    }

    def "Update Transaction api failure due to user not exists"() {
        setup:
        InvalidDummyRequest invalidRequest = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        invalidRequest.headers() >> header.toSet()
        invalidRequest.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        invalidRequest.params() >> parameters
        invalidRequest.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def genericResponse = new UpdateTransactionService().update(invalidRequest, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating transaction configuration"
    }

    def "Update Transaction api failure due to txn id not exists"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "c681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 101,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def genericResponse = new UpdateTransactionService().update(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating transaction configuration"
    }

    def "Update Transaction api failure due to txn combination of id and monitor enabled exists"() {
        setup:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(":identifier", "c681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb10\",\n" +
                "    \"monitorEnabled\": true\n" +
                "  },\n" +
                "  {\n" +
                "    \"id\": 2,\n" +
                "    \"name\": \"POST#/netbank/control/nbWeb1019\",\n" +
                "    \"monitorEnabled\": false\n" +
                "  }\n" +
                "]"
        when:
        def genericResponse = new UpdateTransactionService().update(request, response)
        then:
        response.getStatus() == 400
        genericResponse.getMessage() == "Error while updating transaction configuration"
    }*/
}
