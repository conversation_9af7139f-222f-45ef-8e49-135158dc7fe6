package com.appnomic.appsone.controlcenter.service


import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService
import com.appnomic.appsone.controlcenter.dao.mysql.AgentStatusDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class TriggerAgentCommandServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    Request request = Spy(Request.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.INSTANCE.setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def deleteAgentIT(int agentId) {
        AgentDataService.remAgentIT(agentId)
    }

    @Shared
    Map<String, String[]> queryMapData = new HashMap<>()

    def "Server validation failure - invalid accountIdentifier"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "dummy-account-identifier")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"13\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new TriggerAgentCommandService().triggerAgentCommand(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Account identifier is invalid"
    }

    def "Server validation failure - invalid userId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> "Dummy-auth-token"
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"13\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new TriggerAgentCommandService().triggerAgentCommand(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Error while extracting user details from authorization token"
    }

    def "Server validation failure - invalid commandTypeId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"13\",\n" +
                "        \"commandTypeId\": 1000\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 14,\n" +
                "        \"commandTypeId\": 6000\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new TriggerAgentCommandService().triggerAgentCommand(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid commandTypeId provided. Reason: It can be either 'Start', 'Stop', or 'Restart'"
    }

    def "Server validation failure - invalid physicalAgentId"() {
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"10000\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": 20000,\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]"
        when:
        GenericResponse res = new TriggerAgentCommandService().triggerAgentCommand(request, response)

        then:
        response.getStatus() == 400
        res.getResponseStatus() == "FAILURE"
        res.message == "ServerException : Invalid physicalAgentId. Reason: physicalAgentId not mapped to given service."
    }

    def "Success Case"(){
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    }\n" +
                "]"

        when:
        GenericResponse res = new TriggerAgentCommandService().triggerAgentCommand(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getData().get(0).getAgentId() == 81
        res.getData().get(0).getAgentTypeId().toString() == "[1]"
        res.getData().get(0).getCommandJobId().length() > 0

        cleanup:
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(81,null)
        AgentStatusDataService.remCommandTrigger(81,res.getData().get(0).getCommandJobId(),null)

    }

    def "Success Case with Batch Jobs"(){
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"31\",\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]"

        when:
        GenericResponse res = new TriggerAgentCommandService().triggerAgentCommand(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getData().get(0).getAgentId() == 81
        res.getData().get(0).getAgentTypeId().toString() == "[1]"
        res.getData().get(0).getCommandJobId().length() > 0
        res.getData().get(1).getAgentId() == 31
        res.getData().get(1).getAgentTypeId().toString() == "[3]"
        res.getData().get(1).getCommandJobId().length() > 0

        cleanup:
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(81,null)
        AgentStatusDataService.remCommandTrigger(81,res.getData().get(0).getCommandJobId(),null)
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(31,null)
        AgentStatusDataService.remCommandTrigger(31,res.getData().get(1).getCommandJobId(),null)
    }

    def "Success Case with Batch Jobs and Duplicate keys"(){
        given:
        Set<String> header = new HashSet<>()
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header
        request.headers("Authorization") >> KeycloakConnectionManager.getAccessToken()
        request.queryMap() >> queryMapData
        Map<String, String> parameters = new HashMap<>()
        parameters.put(":identifier", "d681ef13-d690-4917-jkhg-6c79b-1")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"31\",\n" +
                "        \"commandTypeId\": 9\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"81\",\n" +
                "        \"commandTypeId\": 8\n" +
                "    },\n" +
                "    {\n" +
                "        \"physicalAgentId\": \"31\",\n" +
                "        \"commandTypeId\": 9\n" +
                "    }\n" +
                "]"

        when:
        GenericResponse res = new TriggerAgentCommandService().triggerAgentCommand(request, response)

        then:
        response.getStatus() == 200
        res.getResponseStatus() == "SUCCESS"
        res.getData().get(0).getAgentId() == 81
        res.getData().get(0).getAgentTypeId().toString() == "[1]"
        res.getData().get(0).getCommandJobId().length() > 0
        res.getData().get(1).getAgentId() == 31
        res.getData().get(1).getAgentTypeId().toString() == "[3]"
        res.getData().get(1).getCommandJobId().length() > 0

        cleanup:
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(81,null)
        AgentStatusDataService.remCommandTrigger(81,res.getData().get(0).getCommandJobId(),null)
        AgentStatusDataService.updatePhysicalAgentJobStatusCommandExecNULL(31,null)
        AgentStatusDataService.remCommandTrigger(31,res.getData().get(1).getCommandJobId(),null)
    }

}



