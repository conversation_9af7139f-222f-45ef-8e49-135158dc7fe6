package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.AgentCompInstMappingBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.AgentDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.AgentInstanceMappingDetails
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class UpdateAgentMappingForCompInstanceServiceIT extends Specification {

    UpdateAgentMappingForCompInstanceService agentMappingForCompInstanceService = new UpdateAgentMappingForCompInstanceService()

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String accountIdentifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "UpdateAgentMappingForInstance: map agent"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[{\"instanceId\":27,\"addedAgentIds\":[98],\"deletedAgentIds\":[]}]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse genericResponse = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == UIMessages.AGENT_INST_MAP_UPDATE_SUCCESS

        then:
        List<AgentCompInstMappingBean> agentCompInstMappingBean = AgentDataService.getAgentMappingByCompInstanceId(27)
        agentCompInstMappingBean.parallelStream().anyMatch({ a -> a.getAgentId() == 98})
    }

    def "UpdateAgentMappingForInstance: agent already mapped"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[{\"instanceId\":27,\"addedAgentIds\":[98],\"deletedAgentIds\":[]}]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse genericResponse = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : Error in configuring agent/instance mapping details"
    }

    def "UpdateAgentMappingForInstance: unmap agent"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[{\"instanceId\":27,\"addedAgentIds\":[],\"deletedAgentIds\":[98]}]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse genericResponse = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.SUCCESS_STATUS_CODE
        genericResponse.getMessage() == UIMessages.AGENT_INST_MAP_UPDATE_SUCCESS
    }

    def "UpdateAgentMappingForInstance: unmap non-mapped agent"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[{\"instanceId\":27,\"addedAgentIds\":[],\"deletedAgentIds\":[98]}]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse genericResponse = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ServerException : Error in configuring agent/instance mapping details"
    }

    def "UpdateAgentMappingForInstance: empty agent details"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[{\"instanceId\":27,\"addedAgentIds\":[],\"deletedAgentIds\":[]}]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse genericResponse = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Request validation failure. Kindly check the logs."
    }

    def "UpdateAgentMappingForInstance: empty request body"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> ""
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse genericResponse = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Request body is empty."
    }

    def "Update agent mapping: user invalid"() {

        given:

        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "Update agent mapping: account invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "d681ef13-d690--1")
        request.params() >> parameters
        request.body() >> "[]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID)
    }

    def "Update agent mapping: instance Id invalid"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[{\"instanceId\":9999999,\"addedAgentIds\":[98],\"deletedAgentIds\":[]}]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("ServerException : Invalid instance Id")
    }

    def "Update agent mapping: agent Id to be mapped is invalid"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[{\"instanceId\":27,\"addedAgentIds\":[9999999],\"deletedAgentIds\":[]}]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("ServerException : Agent to be added has invalid Id")
    }

    def "Update agent mapping: agent Id to be unmapped is invalid"() {

        given:

        DummyRequest request = Spy(DummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier)
        request.params() >> parameters
        request.body() >> "[{\"instanceId\":27,\"addedAgentIds\":[],\"deletedAgentIds\":[9999999]}]"
        response.status(Constants.SUCCESS_STATUS_CODE)

        when:
        GenericResponse res = agentMappingForCompInstanceService.updateAgentMappingForCompInstance(request, response)

        then:
        response.status == Constants.VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("ServerException : Agent to be unmapped has invalid Id")
    }
}
