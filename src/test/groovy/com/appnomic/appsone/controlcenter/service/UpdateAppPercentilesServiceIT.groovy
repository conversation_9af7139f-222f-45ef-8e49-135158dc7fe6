package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.businesslogic.ControllerBL
import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.Operation
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.ApplicationPercentilesDataService
import com.appnomic.appsone.controlcenter.dao.mysql.BindInDataService
import com.appnomic.appsone.controlcenter.dao.mysql.entity.ApplicationPercentilesBean
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.ApplicationPercentiles
import com.appnomic.appsone.controlcenter.pojo.Percentile
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import com.google.gson.GsonBuilder
import spark.Request
import spark.Response
import spock.lang.Specification

import java.util.stream.Collectors

import static com.appnomic.appsone.controlcenter.common.Constants.*

class UpdateAppPercentilesServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class EmptyUserDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    def dataService = new ApplicationPercentilesDataService()
    def bindInDataService = new BindInDataService()
    def service = new UpdateAppPercentilesService()
    def str = getRequestObject()

    def rollbackAppPercentiles(List<Integer> percentileIds) {
        bindInDataService.deleteApplicationPercentiles(percentileIds, null)
        List<ApplicationPercentilesBean> beanList = ControllerBL.createApplicationPercentiles(2, 1,
                "7640123a-fbde-4fe5-9812-581cd1e3a9c1")
        dataService.addApplicationPercentiles(beanList, null)
    }

    def getRequestObject() {
        DummyRequest dummyRequest = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        dummyRequest.headers() >> header.toSet()
        dummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "1")
        dummyRequest.params() >> parameters
        dummyRequest.body() >> ""
        ApplicationPercentiles res = new GetAppPercentilesService().getAppPercentiles(dummyRequest, response).getData()
        res.getPercentile().get(0).setOperation(Operation.UPDATE)
        res.getPercentile().get(0).setName("Percentile 55")
        res.getPercentile().get(0).setValue(55)
        res.getPercentile().get(1).setOperation(Operation.DELETE)
        res.getPercentile().get(3).setOperation(Operation.DELETE)
        res.getPercentile().get(4).setOperation(Operation.DELETE)
        res.getPercentile().remove(2)
        res.getPercentile().add(Percentile.builder().name("Percentile 92").value(92).operation(Operation.ADD).build())
        String str = new GsonBuilder().create().toJson(res.getPercentile())
        return str
    }

    def "UpdateAppPercentiles"() {
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        service.updateAppPercentiles(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        List<ApplicationPercentilesBean> applicationPercentiles = dataService.getApplicationPercentiles(2, 1)
        applicationPercentiles.size() == 3
        applicationPercentiles.get(0).getKpiIdentifier().contains("55");
        applicationPercentiles.get(1).getKpiIdentifier().contains("90");
        applicationPercentiles.get(2).getKpiIdentifier().contains("92");


        cleanup:
        rollbackAppPercentiles(applicationPercentiles.parallelStream()
                .map({ p -> p.getId() }).collect(Collectors.toList()))
    }

    def "UpdateAppPercentiles invalid auth token"() {

        InvalidDummyRequest request = Spy(InvalidDummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid Authorization Token.")

    }

    def "UpdateAppPercentiles null auth token"() {

        EmptyUserDummyRequest request = Spy(EmptyUserDummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Invalid Authorization Token."

    }

    def "UpdateAppPercentiles null identifier"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, null)
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Account identifier is null or empty."

    }

    def "UpdateAppPercentiles empty identifier"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "")
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage() == "ClientException : Account identifier is null or empty."

    }

    def "UpdateAppPercentiles invalid identifier"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-identifier")
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid Account Identifier.")

    }

    def "UpdateAppPercentiles null applicationId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, null)
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid path parameter 'applicationId'. Reason: It is either is NULL or empty.")

    }

    def "UpdateAppPercentiles empty applicationId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid path parameter 'applicationId'. Reason: It is either is NULL or empty.")

    }

    def "UpdateAppPercentiles invalid applicationId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "invalid-id")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("'applicationId' is not a positive integer.")

    }

    def "UpdateAppPercentiles negative applicationId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "-1")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("'applicationId' is not a positive integer.")

    }

    def "UpdateAppPercentiles invalid application"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "2")
        request.params() >> parameters
        request.body() >> str
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Application('applicationId') is not present for the specified account.")

    }

    def "UpdateAppPercentiles invalid percentileId"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> "[{\"id\":6,\"name\":\"Percentile 55\",\"value\":55,\"operation\":\"UPDATE\"}," +
                "{\"id\":8,\"name\":\"75\",\"value\":75,\"operation\":\"DELETE\"}," +
                "{\"id\":11,\"name\":\"95\",\"value\":95,\"operation\":\"DELETE\"}," +
                "{\"id\":299,\"name\":\"99\",\"value\":99,\"operation\":\"DELETE\"}," +
                "{\"id\":0,\"name\":\"Percentile 92\",\"value\":92,\"operation\":\"ADD\"}]"
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("not available for the specified application.")
    }

    def "UpdateAppPercentiles invalid percentile value"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> "[{\"id\":6,\"name\":\"Percentile 55\",\"value\":55,\"operation\":\"UPDATE\"}," +
                "{\"id\":8,\"name\":\"75\",\"value\":75,\"operation\":\"DELETE\"}," +
                "{\"id\":11,\"name\":\"95\",\"value\":95,\"operation\":\"DELETE\"}," +
                "{\"id\":299,\"name\":\"99\",\"value\":99,\"operation\":\"DELETE\"}," +
                "{\"id\":0,\"name\":\"Percentile 192\",\"value\":192,\"operation\":\"ADD\"}]"
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("Invalid request. Kindly check the logs.")
    }

    def "UpdateAppPercentiles percentile count more than 5"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> "[{\"id\":0,\"name\":\"Percentile 40\",\"value\":40,\"operation\":\"ADD\"}]"
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("An application should have at least 1 percentile and not more than 5 percentiles.")
    }

    def "UpdateAppPercentiles invalid JSON"() {

        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(APPLICATION_ID, "1")
        request.params() >> parameters
        request.body() >> "[\"id\":6,\"name\":\"Percentile 55\",\"value\":55,\"operation\":\"UPDATE\"}," +
                "{\"id\":8,\"name\":\"75\",\"value\":75,\"operation\":\"DELETE\"}," +
                "{\"id\":11,\"name\":\"95\",\"value\":95,\"operation\":\"DELETE\"}," +
                "{\"id\":299,\"name\":\"99\",\"value\":99,\"operation\":\"DELETE\"}," +
                "{\"id\":0,\"name\":\"Percentile 192\",\"value\":192,\"operation\":\"ADD\"}]"
        response.status(200)

        when:
        GenericResponse<ApplicationPercentiles> genericResponse = service.updateAppPercentiles(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains(UIMessages.JSON_INVALID)
    }


}
