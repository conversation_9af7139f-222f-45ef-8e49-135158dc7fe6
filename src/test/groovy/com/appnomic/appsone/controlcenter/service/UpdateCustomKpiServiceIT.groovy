package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.keys.AccountKPIKey
import com.appnomic.appsone.controlcenter.businesslogic.DeleteKPI
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.dao.mysql.ITCleanUpDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.pojo.IdPojo
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class UpdateCustomKpiServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {

            String token = KeycloakConnectionManager.getAccessToken()
            if (token == null || token.isEmpty()) {
                return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCS" +
                        "jhzMnVhazFUUWd3In0.eyJleHAiOjE2MjE5NDkxNjEsImlhdCI6MTYyMTk0NjQ2MSwianRpIjoiZjJjNTI3ZDktM2E5Y" +
                        "S00ZTFlLThhNWYtYWY1MjViYzY2OThjIiwiaXNzIjoiaHR0cHM6Ly8xOTIuMTY4LjEzLjQ0Ojg0NDMvYXV0aC9yZWFsb" +
                        "XMvbWFzdGVyIiwic3ViIjoiNzY0MDEyM2EtZmJkZS00ZmU1LTk4MTItNTgxY2QxZTNhOWMxIiwidHlwIjoiQmVhcmVyI" +
                        "iwiYXpwIjoiYWRtaW4tY2xpIiwic2Vzc2lvbl9zdGF0ZSI6ImExZGI4YWVlLTcyNjQtNDUxNi04Y2E5LTFhNTg1MzUxO" +
                        "DVlMyIsImFjciI6IjEiLCJhbGxvd2VkLW9yaWdpbnMiOlsiKiJdLCJzY29wZSI6InByb2ZpbGUgZW1haWwiLCJlbWFpb" +
                        "F92ZXJpZmllZCI6ZmFsc2UsInByZWZlcnJlZF91c2VybmFtZSI6ImFwcHNvbmVhZG1pbiIsImVtYWlsIjoiYXBwc29uZ" +
                        "WFkbWluLmtAYXBwbm9taWMuY29tIn0.V6dPBnjwGQbj99NGlXFg7rqvcqLxPpMr96qc7N5poqnITNXWL8XJA8VZ0KF0" +
                        "igqpMYQdL1b3SWJ12YXGY6d85AYH3VN4bmOFbtjxfysej2GghMuy_FWqZYDTD7UEhNgG0EoGN-Bnu_FA7zuNuItqVlzc" +
                        "DuYX5OS3Ygf7-6m2-iFMU5JWL0tDaEdAtu5hN4zq8mqQEODGuKT15Q0Y9KSKaCyCjSuGPlFad_AAZEZ6AcDYGxRVNOt0" +
                        "Hfqh_kqLxLVCEouZHsocaLfo0pziKvb8uZ9cQn0jR1ovCfg2CQFuLlt1pVEC_3cg6VXyj8jM6v4kckPw3o3FvM4h0vA" +
                        "qnrY5Gw"
            }
            return token

        }
    }

    class InvalidTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class NullTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    UpdateCustomKpiService service = new UpdateCustomKpiService()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def addGroupKPI() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"id\": -1,\n" +
                "  \"componentTypeId\": 1,\n" +
                "  \"componentId\": 6,\n" +
                "  \"componentCommonVersionId\": 6,\n" +
                "  \"name\": \"test-custom-group-KPI\",\n" +
                "  \"groupKpiDetails\": {\n" +
                "    \"description\": \"KPI Group to collect the Procuess utilization details\",\n" +
                "    \"discovery\": 0,\n" +
                "    \"kpiType\": \"CORE\",\n" +
                "    \"id\": 4,\n" +
                "    \"groupKpiName\": \"Process Utilization\"\n" +
                "  },\n" +
                "  \"kpiCategoryDetails\": {\n" +
                "    \"id\": 5,\n" +
                "    \"name\": \"Network Utilization\",\n" +
                "    \"workLoad\": 0\n" +
                "  },\n" +
                "  \"kpiType\": \"CORE\",\n" +
                "  \"description\": \"test custom KPI\",\n" +
                "  \"computedKpiDetails\": null,\n" +
                "  \"status\": 1,\n" +
                "  \"kpiUnit\": \"Percentage\",\n" +
                "  \"clusterOperation\": \"Average\",\n" +
                "  \"rollupOperation\": \"Max\",\n" +
                "  \"collectionInterval\": 120,\n" +
                "  \"availableForAnalytics\": 1,\n" +
                "  \"dataType\": \"Integer\",\n" +
                "  \"valueType\": \"SNAPSHOT\",\n" +
                "  \"clusterAggregation\": \"SingleValue\",\n" +
                "  \"instanceAggregation\": \"SingleValue\",\n" +
                "  \"identifier\": \"test-custom-group-KPI\"\n" +
                "}"

        return new AddCustomKPIService().addCustomKPI(request, response).getData() as IdPojo
    }

    def addNonGroupKPI() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"id\": -1,\n" +
                "  \"componentTypeId\": 1,\n" +
                "  \"componentId\": 6,\n" +
                "  \"componentCommonVersionId\": 6,\n" +
                "  \"name\": \"test-custom-non-group-KPI\",\n" +
                "  \"groupKpiDetails\": null,\n" +
                "  \"kpiCategoryDetails\": {\n" +
                "    \"id\": 5,\n" +
                "    \"name\": \"Network Utilization\",\n" +
                "    \"workLoad\": 0\n" +
                "  },\n" +
                "  \"kpiType\": \"CORE\",\n" +
                "  \"description\": \"test custom KPI\",\n" +
                "  \"computedKpiDetails\": null,\n" +
                "  \"status\": 1,\n" +
                "  \"kpiUnit\": \"Percentage\",\n" +
                "  \"clusterOperation\": \"Average\",\n" +
                "  \"rollupOperation\": \"Max\",\n" +
                "  \"collectionInterval\": 120,\n" +
                "  \"availableForAnalytics\": 1,\n" +
                "  \"dataType\": \"Integer\",\n" +
                "  \"valueType\": \"SNAPSHOT\",\n" +
                "  \"clusterAggregation\": \"SingleValue\",\n" +
                "  \"instanceAggregation\": \"SingleValue\",\n" +
                "  \"identifier\": \"test-custom-non-group-KPI\"\n" +
                "}"

        return new AddCustomKPIService().addCustomKPI(request, response).getData() as IdPojo
    }

    def mapComponentToKpi(IdPojo kpi) {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(KPI_IDENTIFIER_PATH_PARAMETER, kpi.getIdentifier())
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "      \"id\": " + kpi.getId() + ",\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"

        return new UpdateCustomKpiService().updateCustomKPI(request, response).getData() as IdPojo
    }

    def addComputedKPI() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"id\": -1,\n" +
                "  \"componentTypeId\": 1,\n" +
                "  \"componentId\": 6,\n" +
                "  \"componentCommonVersionId\": 6,\n" +
                "  \"name\": \"test-computed-KPI-123\",\n" +
                "  \"groupKpiDetails\": null,\n" +
                "  \"kpiCategoryDetails\": {\n" +
                "    \"id\": 53,\n" +
                "    \"name\": \"DBAvailability\",\n" +
                "    \"workLoad\": 0\n" +
                "  },\n" +
                "  \"kpiType\": \"AVAILABILITY\",\n" +
                "  \"description\": \"test computed KPI\",\n" +
                "  \"computedKpiDetails\": {\n" +
                "    \"kpisUsed\": [\n" +
                "      1\n" +
                "    ],\n" +
                "    \"displayFormula\": \"CPU Util > 90.00\",\n" +
                "    \"formula\": \"kpi1 > 90.00\"\n" +
                "  },\n" +
                "  \"status\": 1,\n" +
                "  \"kpiUnit\": \"Count\",\n" +
                "  \"clusterOperation\": \"None\",\n" +
                "  \"rollupOperation\": \"None\",\n" +
                "  \"collectionInterval\": 60,\n" +
                "  \"availableForAnalytics\": 1,\n" +
                "  \"dataType\": \"Integer\",\n" +
                "  \"valueType\": \"SNAPSHOT\",\n" +
                "  \"clusterAggregation\": \"MultiValue\",\n" +
                "  \"instanceAggregation\": \"MultiValue\",\n" +
                "  \"identifier\": \"test-computed-KPI-123\"\n" +
                "}"

        return new AddCustomKPIService().addCustomKPI(request, response).getData() as IdPojo
    }

    def "UpdateCustomKPI"() {
    }

    def "UpdateCustomKPI : Request body NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> null

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.INVALID_REQUEST_BODY)
    }

    def "UpdateCustomKPI : Request body Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> ""

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.INVALID_REQUEST_BODY)
    }

    def "UpdateCustomKPI : Auth token NULL"() {
        setup:
        NullTokenDummyRequest nullDummyRequest = Spy(NullTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        nullDummyRequest.headers() >> header.toSet()
        nullDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        nullDummyRequest.params() >> parameters
        nullDummyRequest.body() >> "[\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"


        when:
        def res = service.updateCustomKPI(nullDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_EMPTY)
    }

    def "UpdateCustomKPI : Auth token Empty"() {
        setup:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        emptyDummyRequest.headers() >> header.toSet()
        emptyDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> "[\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"


        when:
        def res = service.updateCustomKPI(emptyDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_EMPTY)
    }

    def "UpdateCustomKPI : Account Identifier NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"


        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_EMPTY)
    }

    def "UpdateCustomKPI : Account Identifier Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"


        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_EMPTY)
    }

    def "UpdateCustomKPI : JSON Parse Exception"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"status\": 1\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.JSON_INVALID)
    }

    def "UpdateCustomKPI : client validation failure edit KPI details"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"test-custom-KPI-update-111111111111111111111111111111111111111111111111111111111111" +
                "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111" +
                "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111" +
                "1111111111111111111111111111111111111111111111111111111111111111111111111\",\n" +
                "    \"description\": \"  \",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec-11111111111111111111111111111111111111111111111111111111111111111\",\n" +
                "    \"dataType\": \"\",\n" +
                "    \"clusterOperation\": \"\",\n" +
                "    \"rollupOperation\": \"\",\n" +
                "    \"collectionInterval\": 185,\n" +
                "    \"clusterAggregation\": \"\",\n" +
                "    \"instanceAggregation\": \"\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 0,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Validation failure of the details provided")
    }

    def "UpdateCustomKPI : client validation failure edit computed KPI"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1161,\n" +
                "    \"status\": 1,\n" +
                "    \"computedKpiDetails\": {\n" +
                "      \"kpisUsed\": [],\n" +
                "      \"formula\": \"\",\n" +
                "      \"displayFormula\": \"Memory Util > 80.00\"\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Validation failure of the details provided")
    }

    def "UpdateCustomKPI : client validation failure edit component KPI"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 2314,\n" +
                "    \"components\": [\n" +
                "      {\n" +
                "        \"id\": 2,\n" +
                "        \"status\": 2,\n" +
                "        \"unmap\": -1\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Validation failure of the details provided")
    }

    def "UpdateCustomKPI : Auth token Invalid"() {
        setup:
        InvalidTokenDummyRequest invalidDummyRequest = Spy(InvalidTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> "[\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"


        when:
        def res = service.updateCustomKPI(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "UpdateCustomKPI : Account Identifier Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "      \"id\": 1,\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 22,\n" +
                "      \"componentCommonVersionId\": 24\n" +
                "    }\n" +
                "  ]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID)
    }

    def "UpdateCustomKPI : KPI Id Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": -1151,\n" +
                "    \"name\": \"test-custom-KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid kpiId")
    }

    def "UpdateCustomKPI : KPI doesn't exist"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 9999,\n" +
                "    \"name\": \"test-custom-KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("KPI with ID does not exist for the provided account")
    }

    def "UpdateCustomKPI : standard KPI"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"test-custom-KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("KPI is not allowed to be modified as it is not a custom KPI.")
    }

    def "UpdateCustomKPI : computed KPI - supported KPIs invalid"() {
        setup:
        IdPojo kpi = addComputedKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"status\": 1,\n" +
                "    \"computedKpiDetails\": {\n" +
                "      \"kpisUsed\": [9999],\n" +
                "      \"formula\": \"Memory Util > 80.00\",\n" +
                "      \"displayFormula\": \"Memory Util > 80.00\"\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Some or all the KPIs used in the computation do not exist for the provided account")

        cleanup:
        ITCleanUpDataService.deleteComputedKPI(kpi.getId())
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : duplicate KPI component mapping"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "    {\n" +
                "      \"id\": " + kpi.getId() + ",\n" +
                "      \"status\": 1,\n" +
                "      \"componentTypeId\": 1,\n" +
                "      \"componentId\": 6,\n" +
                "      \"componentCommonVersionId\": 6\n" +
                "    }\n" +
                "  ]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Duplicate KPI-component mapping")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : KPI name already exists"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"name\": \"CPU UTIL\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("KPI with provided name already exists")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : KPI category details Invalid"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"name\": \"test custom KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 53,\n" +
                "      \"name\": \"DBAvailability\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid category details.")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : KPI clusterOperation Invalid"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"name\": \"test custom KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum11\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("clusterOperation of the KPI is invalid")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : KPI rollupOperation Invalid"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"name\": \"test custom KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum11\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("rollupOperation of the KPI is invalid")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : KPI clusterAggregation Invalid"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"name\": \"test custom KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue11\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("clusterAggregation of the KPI is invalid")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : KPI instanceAggregation Invalid"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"name\": \"test custom KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue11\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("instanceAggregation of the KPI is invalid")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : KPI dataType Invalid"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"name\": \"test custom KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float11111111\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 5,\n" +
                "      \"name\": \"Network Utilization\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("dataType of the KPI is invalid")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : KPI valueType invalid"() {
        setup:
        IdPojo kpi = addComputedKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\"\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("valueType of the KPI is invalid")

        cleanup:
        ITCleanUpDataService.deleteComputedKPI(kpi.getId())
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : custom KPI mapped to computed KPI"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"id\": -1,\n" +
                "  \"componentTypeId\": 1,\n" +
                "  \"componentId\": 6,\n" +
                "  \"componentCommonVersionId\": 6,\n" +
                "  \"name\": \"test-computed-KPI-123\",\n" +
                "  \"groupKpiDetails\": null,\n" +
                "  \"kpiCategoryDetails\": {\n" +
                "    \"id\": 53,\n" +
                "    \"name\": \"DBAvailability\",\n" +
                "    \"workLoad\": 0\n" +
                "  },\n" +
                "  \"kpiType\": \"AVAILABILITY\",\n" +
                "  \"description\": \"test computed KPI\",\n" +
                "  \"computedKpiDetails\": {\n" +
                "    \"kpisUsed\": [" + kpi.getId() + "],\n" +
                "    \"displayFormula\": \"test-custom-non-group-KPI > 90.00\",\n" +
                "    \"formula\": \"test-custom-non-group-KPI > 90.00\"\n" +
                "  },\n" +
                "  \"status\": 1,\n" +
                "  \"kpiUnit\": \"Count\",\n" +
                "  \"clusterOperation\": \"None\",\n" +
                "  \"rollupOperation\": \"None\",\n" +
                "  \"collectionInterval\": 60,\n" +
                "  \"availableForAnalytics\": 1,\n" +
                "  \"dataType\": \"Integer\",\n" +
                "  \"valueType\": \"SNAPSHOT\",\n" +
                "  \"clusterAggregation\": \"MultiValue\",\n" +
                "  \"instanceAggregation\": \"MultiValue\",\n" +
                "  \"identifier\": \"test-computed-KPI-123\"\n" +
                "}"
        IdPojo computedKPI = new AddCustomKPIService().addCustomKPI(request, response).getData() as IdPojo
        DummyRequest dummyRequest = Spy(DummyRequest.class)
        dummyRequest.headers() >> header.toSet()
        dummyRequest.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        dummyRequest.params() >> parameters
        dummyRequest.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"components\": [\n" +
                "      {\n" +
                "        \"id\": 6,\n" +
                "        \"status\": 1,\n" +
                "        \"unmap\": 1\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(dummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("The custom KPI - component mapping can't be modified as it mapped to computed KPI.")

        cleanup:
        ITCleanUpDataService.deleteComputedKPI(computedKPI.getId())
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(computedKPI.getId()).groupKpi(false).build())
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : modify component - not mapped to KPI"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"components\": [\n" +
                "      {\n" +
                "        \"id\": 2,\n" +
                "        \"status\": 1,\n" +
                "        \"unmap\": 1\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("not mapped to the KPI")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI : modify component - active instances mapped to component"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"components\": [\n" +
                "      {\n" +
                "        \"id\": 6,\n" +
                "        \"status\": 1,\n" +
                "        \"unmap\": 1\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("has active instances hence the mapping with the KPI can't be removed")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI SUCCESS"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"name\": \"test-custom-KPI-update\",\n" +
                "    \"description\": \"test custom KPI-update\",\n" +
                "    \"status\": 1,\n" +
                "    \"valueType\": \"DELTA\",\n" +
                "    \"kpiUnit\": \"ReqPerSec\",\n" +
                "    \"dataType\": \"Float\",\n" +
                "    \"clusterOperation\": \"Sum\",\n" +
                "    \"rollupOperation\": \"Sum\",\n" +
                "    \"collectionInterval\": 180,\n" +
                "    \"clusterAggregation\": \"MultiValue\",\n" +
                "    \"instanceAggregation\": \"MultiValue\",\n" +
                "    \"kpiCategoryDetails\": {\n" +
                "      \"id\": 1,\n" +
                "      \"name\": \"CPU\",\n" +
                "      \"workLoad\": 0\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        res.getMessage().contains("Custom KPI(s) updated successfully")

        cleanup:
        ITCleanUpDataService.deleteComputedKPI(kpi.getId())
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI SUCCESS : computed KPI Details"() {
        setup:
        IdPojo kpi = addComputedKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"status\": 1,\n" +
                "    \"computedKpiDetails\": {\n" +
                "      \"kpisUsed\": [\n" +
                "        2\n" +
                "      ],\n" +
                "      \"formula\": \"MEM_UTIL > 80.00\",\n" +
                "      \"displayFormula\": \"Memory Util > 80.00\"\n" +
                "    }\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        res.getMessage().contains("Custom KPI(s) updated successfully")

        cleanup:
        ITCleanUpDataService.deleteComputedKPI(kpi.getId())
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

    def "UpdateCustomKPI SUCCESS : unmap KPI component mapping"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        mapComponentToKpi(kpi)
        ITCleanUpDataService.updateCompInstanceStatusByComponentId(6, 0)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"components\": [\n" +
                "      {\n" +
                "        \"id\": 6,\n" +
                "        \"status\": 1,\n" +
                "        \"unmap\": 1\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        res.getMessage().contains("Custom KPI(s) updated successfully")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
        ITCleanUpDataService.updateCompInstanceStatusByComponentId(6, 1)
    }

    def "UpdateCustomKPI SUCCESS : modify status KPI component mapping"() {
        setup:
        IdPojo kpi = addNonGroupKPI()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "[\n" +
                "  {\n" +
                "    \"id\": " + kpi.getId() + ",\n" +
                "    \"components\": [\n" +
                "      {\n" +
                "        \"id\": 6,\n" +
                "        \"status\": 0,\n" +
                "        \"unmap\": 0\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]"

        when:
        def res = service.updateCustomKPI(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        res.getMessage().contains("Custom KPI(s) updated successfully")

        cleanup:
        new DeleteKPI().process(AccountKPIKey.builder().accountId(2).kpiId(kpi.getId()).groupKpi(false).build())
    }

}
