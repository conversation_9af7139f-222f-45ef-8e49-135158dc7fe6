package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.KpiMaintenanceStatusBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.dao.mysql.KPIDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Shared
import spock.lang.Specification

class UpdateKpiMaintenanceStatusServiceIT extends Specification {
    UpdateKpiMaintenanceStatusService service = new UpdateKpiMaintenanceStatusService()
    KPIDataService kpiDataService = new KPIDataService()
    String requestBody = "{" +
            "\"instances\":[1]," +
            "\"kpiMaintenanceStatusDetails\":" +
            "[{\"kpiId\":1," +
            "\"groupKpiId\":0," +
            "\"isMaintenanceExcluded\":0}" +
            "]}"

    class DummyResponse extends Response {
        int status
        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            return KeycloakConnectionManager.getAccessToken()
        }
    }

    class InvalidDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    @Shared
    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def "updateKpiMaintenanceStatus"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> requestBody
        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.getStatus() == 200
        result.getData() != null

        then:
        List<KpiMaintenanceStatusBean> kpiMaintenanceStatusBeans = kpiDataService.fetchCompInstanceKpiMaintenanceStatus(1, 2)
        kpiMaintenanceStatusBeans.parallelStream().anyMatch({ c -> c.getIsMaintenanceExcluded() == 0 })
    }

    def "updateKpiMaintenanceStatus: request null"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> null

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: request empty"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> " "

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ClientException : Invalid request body. Reason: Request body is either NULL or empty."
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: invalid status"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >>"{" +
                "\"instances\":[1]," +
                "\"kpiMaintenanceStatusDetails\":" +
                "[{\"kpiId\":1," +
                "\"groupKpiId\":0," +
                "\"isMaintenanceExcluded\":123}" +
                "]}"

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ClientException : invalid input data"
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: invalid JSON"() {

        given:
        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{" +
                "\"instances\":[1]," +
                "\"kpiMaintenanceStatusDetails\":" +
                "[{\"kpiId\":1," +
                "\"groupKpiId\":0," +
                "\"isMaintenanceIncluded\":0}" +
                "]}"

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ClientException : Invalid JSON."
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: Auth token Empty"() {
        given:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        emptyDummyRequest.headers() >> header.toSet()
        emptyDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> requestBody

        when:
        def result = service.updateKpiMaintenanceStatus(emptyDummyRequest, response)

        then:
        response.status == 400

        then:
        result.message == "ClientException : Invalid Authorization Token."
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: Auth token invalid"() {
        given:
        InvalidDummyRequest invalidDummyRequest = Spy(InvalidDummyRequest.class)
        header.add(Constants.AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> requestBody

        when:
        def result = service.updateKpiMaintenanceStatus(invalidDummyRequest, response)

        then:
        response.status == 400

        then:
        result.message == "ServerException : Error while extracting user details from authorization token"
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: account invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, "invalid_identifier")
        request.params() >> parameters
        request.body() >> requestBody

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ServerException : Account identifier is invalid"
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: instanceId invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{" +
                "\"instances\":[999999]," +
                "\"kpiMaintenanceStatusDetails\":" +
                "[{\"kpiId\":1," +
                "\"groupKpiId\":0," +
                "\"isMaintenanceExcluded\":0}" +
                "]}"

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ServerException : Invalid instanceId provided"
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: kpiId invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{" +
                "\"instances\":[1]," +
                "\"kpiMaintenanceStatusDetails\":" +
                "[{\"kpiId\":999999," +
                "\"groupKpiId\":0," +
                "\"isMaintenanceExcluded\":0}" +
                "]}"

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ServerException : KPI with ID [999999] is unavailable"
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: groupId - kpiId mappping invalid"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{" +
                "\"instances\":[1]," +
                "\"kpiMaintenanceStatusDetails\":" +
                "[{\"kpiId\":1," +
                "\"groupKpiId\":999999," +
                "\"isMaintenanceExcluded\":0}" +
                "]}"

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ServerException : Group KPI with ID [999999] is not mapped to KPI with ID [1]"
        result.responseStatus == "FAILURE"
    }

    def "updateKpiMaintenanceStatus: groupId - duplicate data"() {

        given:

        header.add(Constants.AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> new HashMap<>()
        parameters.put(Constants.ACCOUNT_IDENTIFIER, identifier)
        request.params() >> parameters
        request.body() >> "{" +
                "\"instances\":[1]," +
                "\"kpiMaintenanceStatusDetails\":" +
                "[{\"kpiId\":1," +
                "\"groupKpiId\":0," +
                "\"isMaintenanceExcluded\":0}," +
                "{\"kpiId\":1," +
                "\"groupKpiId\":0," +
                "\"isMaintenanceExcluded\":0}" +
                "]}"

        when:
        def result = service.updateKpiMaintenanceStatus(request, response)

        then:
        response.status == 400

        then:
        result.message == "ServerException : Duplicate entries in the input"
        result.responseStatus == "FAILURE"
    }
}
