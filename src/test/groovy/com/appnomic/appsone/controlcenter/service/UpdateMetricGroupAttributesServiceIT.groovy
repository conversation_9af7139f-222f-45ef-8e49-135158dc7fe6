package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.common.GenericResponse
import com.appnomic.appsone.controlcenter.common.UIMessages
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.util.StringUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import spark.Request
import spark.Response
import spock.lang.Specification

import static com.appnomic.appsone.controlcenter.common.Constants.*

class UpdateMetricGroupAttributesServiceIT extends Specification {

    class DummyResponse extends Response {
        int status

        void status(int i) {
            status = i
        }
    }

    class DummyRequest extends Request {

        @Override
        String headers(String header) {
            String authToken = KeycloakConnectionManager.getAccessToken()
            if (StringUtils.isEmpty(authToken))
                return "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJYeldqTWZBV25zaGpGUHFWcDdiRWNjMkZ2b0d4TDBCS" +
                        "jhzMnVhazFUUWd3In0.eyJleHAiOjE2MjU4MjM1MjEsImlhdCI6MTYyNTgyMDgyMSwianRpIjoiNmFlNGVjMDEtZDI5N" +
                        "i00ZTQ4LWI2NTQtNzY4Y2YxY2M0NGQxIiwiaXNzIjoiaHR0cHM6Ly8xOTIuMTY4LjEzLjQ0Ojg0NDMvYXV0aC9yZWFsb" +
                        "XMvbWFzdGVyIiwic3ViIjoiNzY0MDEyM2EtZmJkZS00ZmU1LTk4MTItNTgxY2QxZTNhOWMxIiwidHlwIjoiQmVhcmVyI" +
                        "iwiYXpwIjoiYWRtaW4tY2xpIiwic2Vzc2lvbl9zdGF0ZSI6IjkxNjBiOWQyLWI5NjctNGZlOS1hZWFmLTI5N2Q5YTBjO" +
                        "GRlNyIsImFjciI6IjEiLCJhbGxvd2VkLW9yaWdpbnMiOlsiKiJdLCJzY29wZSI6InByb2ZpbGUgZW1haWwiLCJlbWFpb" +
                        "F92ZXJpZmllZCI6ZmFsc2UsInByZWZlcnJlZF91c2VybmFtZSI6ImFwcHNvbmVhZG1pbiIsImVtYWlsIjoiYXBwc29uZW" +
                        "FkbWluLmtAYXBwbm9taWMuY29tIn0.FmNEY7oOraBuukI4ORBzK4TOkdRI9LBKQMiSzJhjhFQ7LIOih5SyoioHKfOI_Kh" +
                        "PaklnCTQSTlBSO7teYTo1M48I0AEOjCf7MMGzIxwm78UrPcJlg_VuNcoHIM2XaPiZxUcKZHVIsC33OuX0uPBf2TKt8GyC" +
                        "nQQoC9CiKHT3HKVG1koDbL-wRCL4hG2_LHQq5z3LqO9u_Cq5QUbJvFE2N2fTt6j8bkKIWK0FtC4CsNCKTtB5fX8m7YhfD" +
                        "2pXy0I-7i-B1fZFnM7DE0uFRScvp4CoPQyHZ11UWAdhPdOewffO9FDpEXfqhYyX195NJSDVfqkrR1Kx1XU-VVirxegD8Q"
            return authToken
        }
    }

    class InvalidTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return "Invalid-Token"
        }
    }

    class NullTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return null
        }
    }

    class EmptyTokenDummyRequest extends Request {

        @Override
        String headers(String header) {
            return ""
        }
    }

    DummyRequest request = Spy(DummyRequest.class)
    DummyResponse response = Spy(DummyResponse.class)

    Set<String> header = new HashSet<>()
    Map<String, String> parameters = new HashMap<>()
    Map<String, String[]> queryMap = new HashMap<>()
    String identifier = "d681ef13-d690-4917-jkhg-6c79b-1"
    UpdateMetricGroupAttributesService service = new UpdateMetricGroupAttributesService()

    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def getBody() {
        return "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"user input value\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" + "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"oldValue\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"new user input value\",\n" +
                "      \"oldValue\" : \"old value\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"
    }

    def addTestAttributes() {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute1\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute2\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute3\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        service.updateMetricGroupAttributes(request, response)
    }

    def deleteTestAttributes(String attribute) {
        DummyRequest request = Spy(DummyRequest.class)
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"" + attribute + "\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        service.updateMetricGroupAttributes(request, response)
    }

    def "UpdateMetricGroupAttributes : Auth token NULL"() {
        setup:
        NullTokenDummyRequest nullDummyRequest = Spy(NullTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        nullDummyRequest.headers() >> header.toSet()
        nullDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        nullDummyRequest.params() >> parameters
        nullDummyRequest.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(nullDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid Authorization Token.")
    }

    def "UpdateMetricGroupAttributes : Auth token Empty"() {
        setup:
        EmptyTokenDummyRequest emptyDummyRequest = Spy(EmptyTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        emptyDummyRequest.headers() >> header.toSet()
        emptyDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        emptyDummyRequest.params() >> parameters
        emptyDummyRequest.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(emptyDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid Authorization Token.")
    }

    def "UpdateMetricGroupAttributes : Account Identifier NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, null)
        request.params() >> parameters
        request.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Account identifier is null or empty.")
    }

    def "UpdateMetricGroupAttributes : Account Identifier Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, "")
        request.params() >> parameters
        request.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Account identifier is null or empty.")
    }

    def "UpdateMetricGroupAttributes : Group Id NULL"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, null)
        request.params() >> parameters
        request.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("groupId is null or empty.")
    }

    def "UpdateMetricGroupAttributes : Group Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, "")
        request.params() >> parameters
        request.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("groupId is null or empty.")
    }

    def "UpdateMetricGroupAttributes : Group Id not an integer"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, "a1")
        request.params() >> parameters
        request.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("groupId is not an integer.")
    }

    def "UpdateMetricGroupAttributes : Group ID negative"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, "-20")
        request.params() >> parameters
        request.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure.")
    }

    def "UpdateMetricGroupAttributes : Request body empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, "20")
        request.params() >> parameters
        request.body() >> ""


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("InstanceIds are not specified in the request body.")
    }

    def "UpdateMetricGroupAttributes : Request body null"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, "20")
        request.params() >> parameters
        request.body() >> null


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("InstanceIds are not specified in the request body.")
    }

    def "UpdateMetricGroupAttributes : Invalid JSON"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31a,\n" +
                "    30b\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"user input value\",\n" +
                "      \"oldValue\" : null\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"oldValue\"\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"new user input value\",\n" +
                "      \"oldValue\" : \"old value\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid JSON.")
    }

    def "UpdateMetricGroupAttributes : Instance Ids Empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"user input value\",\n" +
                "      \"oldValue\" : null\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"oldValue\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"new user input value\",\n" +
                "      \"oldValue\" : \"old value\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure.")
    }

    def "UpdateMetricGroupAttributes : Instance Ids string"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31a,\n" +
                "    30b\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"user input value\",\n" +
                "      \"oldValue\" : null\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"oldValue\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"new user input value\",\n" +
                "      \"oldValue\" : \"old value\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid JSON.")
    }

    def "UpdateMetricGroupAttributes : Instance Ids negative"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    -31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"user input value\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"oldValue\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"new user input value\",\n" +
                "      \"oldValue\" : \"old value\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure")
    }

    def "UpdateMetricGroupAttributes : Invalid Action"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    -31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"CREATE\",\n" +
                "      \"value\": \"user input value\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"REMOVE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"oldValue\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"EDIT\",\n" +
                "      \"value\": \"new user input value\",\n" +
                "      \"oldValue\" : \"old value\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid JSON.")
    }

    def "UpdateMetricGroupAttributes : Attributes null"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": null\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure")
    }

    def "UpdateMetricGroupAttributes : Attributes empty"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": []\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Request validation failure")
    }

    def "UpdateMetricGroupAttributes : Auth token Invalid"() {
        setup:
        InvalidTokenDummyRequest invalidDummyRequest = Spy(InvalidTokenDummyRequest.class)
        header.add(AUTHORIZATION)
        invalidDummyRequest.headers() >> header.toSet()
        invalidDummyRequest.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        invalidDummyRequest.params() >> parameters
        invalidDummyRequest.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(invalidDummyRequest, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.AUTH_KEY_INVALID)
    }

    def "UpdateMetricGroupAttributes : Account Identifier Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, "invalid-account-identifier")
        parameters.put(GROUP_ID, "20")
        request.params() >> parameters
        request.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains(UIMessages.ACCOUNT_IDENTIFIER_INVALID)
    }

    def "UpdateMetricGroupAttributes : Group Id Invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, "99999")
        request.params() >> parameters
        request.body() >> getBody()


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid groupId.")
    }

    def "UpdateMetricGroupAttributes : Instance Ids invalid"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    60\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"user input value\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"oldValue\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"new user input value\",\n" +
                "      \"oldValue\" : \"old value\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("Invalid instance Id")
    }

    def "UpdateMetricGroupAttributes : Instance Ids different component"() {
        setup:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    1,\n" +
                "    10\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"user input value\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" + "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"oldValue\",\n" + "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"new user input value\",\n" +
                "      \"oldValue\" : \"old value\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"


        when:
        def res = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE
        res.getMessage().contains("InstanceIds specified are mapped to different components.")
    }

    def "updateMetricGroupAttributes : Add Attributes"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute1\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute2\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute3\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        genericResponse.getMessage().contains("Metric group attributes updated successfully")

        cleanup:
        deleteTestAttributes("testAttribute1")
        deleteTestAttributes("testAttribute2")
        deleteTestAttributes("testAttribute3")
    }

    def "updateMetricGroupAttributes : Add duplicate attributes"() {

        given:
        addTestAttributes()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute1\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute2\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"ADD\",\n" +
                "      \"value\": \"testAttribute3\",\n" +
                "      \"oldValue\" : null,\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("attributes specified are already present for the group hence it can't be added")

        cleanup:
        deleteTestAttributes("testAttribute1")
        deleteTestAttributes("testAttribute2")
        deleteTestAttributes("testAttribute3")
    }

    def "updateMetricGroupAttributes : Edit attribute name"() {

        given:
        addTestAttributes()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"testAttribute1-update\",\n" +
                "      \"oldValue\" : \"testAttribute1\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"testAttribute2-update\",\n" +
                "      \"oldValue\" : \"testAttribute2\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"<FE>testAttribute3-update\",\n" +
                "      \"oldValue\" : \"testAttribute3\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        genericResponse.getMessage().contains("Metric group attributes updated successfully")

        cleanup:
        deleteTestAttributes("testAttribute1-update")
        deleteTestAttributes("testAttribute2-update")
        deleteTestAttributes("<FE>testAttribute3-update")
    }

    def "updateMetricGroupAttributes : Edit attribute status"() {

        given:
        addTestAttributes()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute1\",\n" +
                "      \"status\" : 0\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute2\",\n" +
                "      \"status\" : 0\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute3\",\n" +
                "      \"status\" : 0\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        genericResponse.getMessage().contains("Metric group attributes updated successfully")

        cleanup:
        deleteTestAttributes("testAttribute1")
        deleteTestAttributes("testAttribute2")
        deleteTestAttributes("testAttribute3")
    }

    def "updateMetricGroupAttributes : Edit attribute unavailable"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"testAttribute1-update\",\n" +
                "      \"oldValue\" : \"testAttribute1\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute2\",\n" +
                "      \"status\" : 0\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute3\",\n" +
                "      \"status\" : 0\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("attributes specified are not available to be updated or deleted.")

    }

    def "updateMetricGroupAttributes : Edit attribute duplicate"() {

        given:
        addTestAttributes()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"UPDATE\",\n" +
                "      \"value\": \"testAttribute1\",\n" +
                "      \"oldValue\" : \"testAttribute2\",\n" +
                "      \"status\" : 0\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("attributes specified are already present for the group hence it can't be added.")


        cleanup:
        deleteTestAttributes("testAttribute1")
        deleteTestAttributes("testAttribute2")
        deleteTestAttributes("testAttribute3")
    }

    def "updateMetricGroupAttributes : Delete Attributes"() {

        given:
        addTestAttributes()
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute1\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute2\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute3\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == SUCCESS_STATUS_CODE
        genericResponse.getMessage().contains("Metric group attributes updated successfully")

    }

    def "updateMetricGroupAttributes : Delete unavailable Attributes"() {

        given:
        header.add(AUTHORIZATION)
        request.headers() >> header.toSet()
        request.queryMap() >> queryMap
        parameters.put(ACCOUNT_IDENTIFIER, identifier)
        parameters.put(GROUP_ID, 20 as String)
        request.params() >> parameters
        request.body() >> "{\n" +
                "  \"instanceIds\": [\n" +
                "    31,\n" +
                "    30\n" +
                "  ],\n" +
                "  \"groupAttributes\": [\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute1\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute2\",\n" +
                "      \"status\" : 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"action\" : \"DELETE\",\n" +
                "      \"value\": null,\n" +
                "      \"oldValue\" : \"testAttribute3\",\n" +
                "      \"status\" : 1\n" +
                "    }\n" +
                "  ]\n" +
                "}"
        response.status(200)

        when:
        GenericResponse genericResponse = service.updateMetricGroupAttributes(request, response)

        then:
        response.status == VALIDATION_FAILED_STATUS_CODE
        genericResponse.getMessage().contains("attributes specified are not available to be updated or deleted.")
    }
}
