package com.appnomic.appsone.controlcenter.service

import com.appnomic.appsone.controlcenter.beans.KeycloakUserBean
import com.appnomic.appsone.controlcenter.beans.UserAttributesBean
import com.appnomic.appsone.controlcenter.beans.UserBean
import com.appnomic.appsone.controlcenter.common.Constants
import com.appnomic.appsone.controlcenter.dao.mysql.UserDataService
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager
import com.appnomic.appsone.controlcenter.util.CommonUtils
import com.appnomic.appsone.keycloak.KeycloakConnectionManager
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.reflect.TypeToken
import spock.lang.Specification

import java.time.LocalDateTime
import java.time.ZoneId

class UserDormantServiceIT extends Specification {
    private static final ObjectMapper OBJECT_MAPPER = CommonUtils.getObjectMapperWithHtmlEncoder()


    def setupSpec() {
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(false)
        MySQLConnectionManager.getInstance().setIntegrationTestStatus(true)
        KeyCloakAuthService.init()
    }

    def addUserKeycloakIT(KeycloakUserBean userBean) {
        KeycloakConnectionManager.addUser(OBJECT_MAPPER.writeValueAsString(userBean))

        List<UserBean> users = CommonUtils.jsonToObject(KeycloakConnectionManager.getUsers(),
                new TypeToken<List<UserBean>>() {
                }.getType())

        return users.parallelStream().filter({ u -> u.getUsername() == userBean.getUsername() })
                .findAny().get()
    }

    void deleteUserKeycloakIT(String userId) {
        KeycloakConnectionManager.deleteKeycloakUser(userId)
    }

    def "User dormant if user never login to the system"() {
        given:
        KeycloakUserBean keycloakUserBean = KeycloakUserBean.builder()
                .firstName("dormant-user")
                .lastName("heal")
                .email("www.appnomic.com")
                .enabled("true")
                .username("dormant-app-user")
                .build()
        def user = addUserKeycloakIT(keycloakUserBean)
        def now = LocalDateTime.now(ZoneId.of("UTC")).minusDays(Constants.USER_DORMANT_AFTER_CREATION_DATE_WITHOUT_LOGIN_DEFAULT as long).minusDays(1L).toString()
        UserAttributesBean userAttributesBean = UserAttributesBean.builder()
                .accessProfileId(4)
                .roleId(3)
                .contactNumber("9876598765")
                .emailAddress("www.appnomic.com")
                .userName("dormant-app-user")
                .status(1)
                .timeZoneMyChoice(0)
                .userIdentifier(user.getId())
                .userDetailsId(user.getId())
                .createdTime(now)
                .updatedTime(now)
                .build()
        def userDataService = new UserDataService()
        userDataService.addUserAttributes(userAttributesBean, null)
        when:
        new UserDormantService().runOneIteration()
        then:
        userDataService.getUserAttributes(user.getId()).getStatus() == 0
        cleanup:
        userDataService.deleteUserAttributesAndAccessDetails(user.getId(), null)
        deleteUserKeycloakIT(user.getId())
    }

    def "User dormant if userid/password expires to the system"() {
        given:
        KeycloakUserBean keycloakUserBean = KeycloakUserBean.builder()
                .firstName("dormant-user-last-login-expires")
                .lastName("heal")
                .email("www.appnomic-systems.com")
                .enabled("true")
                .username("dormant-user-last-login-expires")
                .build()
        def user = addUserKeycloakIT(keycloakUserBean)
        def now = LocalDateTime.now(ZoneId.of("UTC")).minusDays(Constants.USER_DORMANT_AFTER_CREATION_DATE_WITHOUT_LOGIN_DEFAULT as long).minusDays(1L).toString()
        def lastLoginDate = LocalDateTime.now(ZoneId.of("UTC")).minusDays(Constants.USER_DORMANT_AFTER_LAST_LOGIN_DATE_DEFAULT as long).minusDays(1L).toString()
        UserAttributesBean userAttributesBean = UserAttributesBean.builder()
                .accessProfileId(4)
                .roleId(3)
                .contactNumber("9876598765")
                .emailAddress("www.appnomic-systems.com")
                .userName("dormant-user-last-login-expires")
                .status(1)
                .timeZoneMyChoice(0)
                .userIdentifier(user.getId())
                .userDetailsId(user.getId())
                .createdTime(now)
                .updatedTime(now)
                .lastLoginTime(lastLoginDate)
                .build()
        def userDataService = new UserDataService()
        userDataService.addUserAttributes(userAttributesBean, null)
        when:
        new UserDormantService().runOneIteration()
        then:
        userDataService.getUserAttributes(user.getId()).getStatus() == 0
        cleanup:
        userDataService.deleteUserAttributesAndAccessDetails(user.getId(), null)
        deleteUserKeycloakIT(user.getId())
    }

    def "User dormant if user recently created in the system"() {
        given:
        KeycloakUserBean keycloakUserBean = KeycloakUserBean.builder()
                .firstName("active-user")
                .lastName("heal")
                .email("www.appnomic.com")
                .enabled("true")
                .username("active-user")
                .build()
        def user = addUserKeycloakIT(keycloakUserBean)
        def now = LocalDateTime.now(ZoneId.of("UTC")).minusDays(Constants.USER_DORMANT_AFTER_CREATION_DATE_WITHOUT_LOGIN_DEFAULT - 10L as long).toString()
        UserAttributesBean userAttributesBean = UserAttributesBean.builder()
                .accessProfileId(4)
                .roleId(3)
                .contactNumber("9876598765")
                .emailAddress("www.appnomic.com")
                .userName("active-user")
                .status(1)
                .timeZoneMyChoice(0)
                .userIdentifier(user.getId())
                .userDetailsId(user.getId())
                .createdTime(now)
                .updatedTime(now)
                .build()
        def userDataService = new UserDataService()
        userDataService.addUserAttributes(userAttributesBean, null)
        when:
        new UserDormantService().runOneIteration()
        then:
        userDataService.getUserAttributes(user.getId()).getStatus() == 1
        cleanup:
        userDataService.deleteUserAttributesAndAccessDetails(user.getId(), null)
        deleteUserKeycloakIT(user.getId())
    }

//    def "User dormant if recently login to the system"() {
//        given:
//        KeycloakUserBean keycloakUserBean = KeycloakUserBean.builder()
//                .firstName("active-user-last-login-recent")
//                .lastName("heal")
//                .email("www.appnomic-systems.com")
//                .enabled("true")
//                .username("active-user-last-login-recent")
//                .build()
//        def user = addUserKeycloakIT(keycloakUserBean)
//        def now = LocalDateTime.now(ZoneId.of("UTC")).minusDays(Constants.USER_DORMANT_AFTER_CREATION_DATE_WITHOUT_LOGIN_DEFAULT as long).minusDays(1L).toString()
//        def lastLoginDate = LocalDateTime.now(ZoneId.of("UTC")).minusDays(Constants.USER_DORMANT_AFTER_LAST_LOGIN_DATE_DEFAULT - 10L as long).toString()
//        UserAttributesBean userAttributesBean = UserAttributesBean.builder()
//                .accessProfileId(4)
//                .roleId(3)
//                .contactNumber("9876598765")
//                .emailAddress("www.appnomic-systems.com")
//                .userName("active-user-last-login-recent")
//                .status(1)
//                .timeZoneMyChoice(0)
//                .userIdentifier(user.getId())
//                .userDetailsId(user.getId())
//                .createdTime(now)
//                .updatedTime(now)
//                .lastLoginTime(lastLoginDate)
//                .build()
//        def userDataService = new UserDataService()
//        userDataService.addUserAttributes(userAttributesBean, null)
//        when:
//        new UserDormantService().runOneIteration()
//        then:
//        userDataService.getUserAttributes(user.getId()).getStatus() == 1
//        cleanup:
//        userDataService.deleteUserAttributesAndAccessDetails(user.getId(), null)
//        deleteUserKeycloakIT(user.getId())
//    }
}
