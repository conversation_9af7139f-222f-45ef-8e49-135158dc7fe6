package com.appnomic.appsone.controlcenter.dao;

import com.appnomic.appsone.controlcenter.dao.mysql.entity.DefaultNotificationPreferences;
import com.appnomic.appsone.controlcenter.dao.mysql.interfaces.ApplicationNotificationDao;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import com.appnomic.appsone.controlcenter.pojo.NotificationPreferencesPojo;
import com.appnomic.appsone.controlcenter.util.ApplicationNotificationTest;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class ApplicationNotificationDaoTest {

    static ApplicationNotificationDao applicationNotificationDao = openConnection();

    @Test
    public void getApplicationNotificationMappingDetails(){
        assertTrue(applicationNotificationDao instanceof ApplicationNotificationDao);
        List<NotificationPreferencesPojo> notificationPreferences = applicationNotificationDao.getApplicationNotificationMappingDetails(ApplicationNotificationTest.appId);
        assertEquals(4, notificationPreferences.size());
    }

    @Test
    public void updateNotificationConfiguration(){
        assertTrue(applicationNotificationDao instanceof ApplicationNotificationDao);
        List<DefaultNotificationPreferences> entityList  = ApplicationNotificationTest.generateNotificationPreferences();
        int[] ids =   applicationNotificationDao.updateDefaultPreferences(entityList);
        assertEquals(4, ids.length);
    }

    private static ApplicationNotificationDao openConnection() {
        String H2URL = "jdbc:h2:mem:apmcommon;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/test/resources/create.sql'\\;" +
                "RUNSCRIPT FROM './src/test/resources/populate.sql'";
        MySQLConnectionManager.INSTANCE.setH2URL(H2URL);
        MySQLConnectionManager.INSTANCE.setDbi(null);
        MySQLConnectionManager.INSTANCE.setHaveToRunTestCases(true);
        MySQLConnectionManager.INSTANCE.getHandle();
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            System.out.println(e);
        }
        return MySQLConnectionManager.getInstance().getHandle().open(ApplicationNotificationDao.class);
    }
}
