-- -----------------------------------------------------
-- Schema appsone_integration_test
-- -----------------------------------------------------
DROP SCHEMA IF EXISTS `appsone_integration_test` ;

-- -----------------------------------------------------
-- Schema appsone_integration_test
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `appsone_integration_test` ;
USE `appsone_integration_test` ;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`a1_installation_attributes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`a1_installation_attributes` ;

CREATE TABLE `a1_installation_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(256) NOT NULL,
  `value` VARCHAR(256) NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_type` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_type_account1_idx` ON `appsone_integration_test`.`mst_type` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_sub_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_sub_type` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_sub_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_sub_type_mst_type`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone_integration_test`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_sub_type_mst_type_idx` ON `appsone_integration_test`.`mst_sub_type` (`mst_type_id` ASC);

CREATE INDEX `mst_sub_type_account1_idx` ON `appsone_integration_test`.`mst_sub_type` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`transaction`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`transaction` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`transaction` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `audit_enabled` TINYINT(1) NOT NULL DEFAULT 0,
  `is_autoconfigured` TINYINT(1) NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `transaction_type_id` INT NOT NULL,
  `pattern_hashcode` VARCHAR(256) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `rule_id` INT NULL,
  `is_business_txn` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_mst_sub_type1`
    FOREIGN KEY (`transaction_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_transaction_mst_sub_type1_idx` ON `appsone_integration_test`.`transaction` (`transaction_type_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE_transaction` ON `appsone_integration_test`.`transaction` (`identifier` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`sub_transactions`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`sub_transactions` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`sub_transactions` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `http_method` VARCHAR(8) NULL,
  `http_url` VARCHAR(128) NULL,
  `transaction_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_configured_transactions_transaction1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone_integration_test`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_sub_transactions_transaction1_idx` ON `appsone_integration_test`.`sub_transactions` (`transaction_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_component`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_component` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_component` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `is_custom` TINYINT(1) NULL DEFAULT 0,
  `status` TINYINT NULL,
  `created_time` DATETIME NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL DEFAULT 0,
  `description` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_component_account1_idx` ON `appsone_integration_test`.`mst_component` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_component_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_component_type` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_component_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `description` VARCHAR(256) NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_component_type_account1_idx` ON `appsone_integration_test`.`mst_component_type` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`comp_instance`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`comp_instance` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`comp_instance` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `host_id` INT NULL,
  `is_DR` TINYINT NULL,
  `is_cluster` TINYINT NULL,
  `mst_component_version_id` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  `discovery` TINYINT NULL,
  `host_address` VARCHAR(256) NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `mst_common_version_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone_integration_test`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_mst_component_type1`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone_integration_test`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `comp_instance_account1_idx` ON `appsone_integration_test`.`comp_instance` (`account_id` ASC);

CREATE INDEX `fk_comp_instance_mst_component1_idx` ON `appsone_integration_test`.`comp_instance` (`mst_component_id` ASC);

CREATE INDEX `fk_comp_instance_mst_component_type1_idx` ON `appsone_integration_test`.`comp_instance` (`mst_component_type_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE_comp_inst` ON `appsone_integration_test`.`comp_instance` (`identifier` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_common_attributes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_common_attributes` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_common_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `attribute_name` VARCHAR(64) NOT NULL,
  `attribute_type` VARCHAR(64) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_common_attributes_account1_details` ON `appsone_integration_test`.`mst_common_attributes` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_common_version`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_common_version` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_common_version` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_component_id` INT NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_common_version_mst_component`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone_integration_test`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_common_version_mst_component_idx` ON `appsone_integration_test`.`mst_common_version` (`mst_component_id` ASC);

CREATE INDEX `mst_common_version_account1_idx` ON `appsone_integration_test`.`mst_common_version` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_component_attribute_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_component_attribute_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_component_attribute_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_common_attributes_id` INT NOT NULL,
  `is_custom` TINYINT(1) NULL DEFAULT 0,
  `is_mandatory` TINYINT(1) NOT NULL,
  `default_value` VARCHAR(64) NULL,
  `mst_common_version_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `min_length` SMALLINT NULL,
  `max_length` INT NULL,
  `regex` VARCHAR(512) NULL,
  `error_message` VARCHAR(256) NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  `is_ui_visible` TINYINT(1) NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_component_attribute_mapping_mst_common_attributes1`
    FOREIGN KEY (`mst_common_attributes_id`)
    REFERENCES `appsone_integration_test`.`mst_common_attributes` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_attribute_mapping_mst_common_version1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone_integration_test`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_attribute_mapping_component_id`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone_integration_test`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_attribute_mapping_component_type_id`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone_integration_test`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_component_attribute_mapping_mst_common_attributes1_idx` ON `appsone_integration_test`.`mst_component_attribute_mapping` (`mst_common_attributes_id` ASC);

CREATE INDEX `fk_mst_component_attribute_mapping_mst_common_version1_idx` ON `appsone_integration_test`.`mst_component_attribute_mapping` (`mst_common_version_id` ASC);

CREATE INDEX `fk_mst_component_attribute_mapping_component_id_idx` ON `appsone_integration_test`.`mst_component_attribute_mapping` (`mst_component_id` ASC);

CREATE INDEX `fk_mst_component_attribute_mapping_component_type_id_idx` ON `appsone_integration_test`.`mst_component_attribute_mapping` (`mst_component_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`comp_instance_attribute_values`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`comp_instance_attribute_values` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`comp_instance_attribute_values` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `attribute_value` VARCHAR(256) NULL,
  `comp_instance_id` INT NOT NULL,
  `mst_component_attribute_mapping_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_common_attributes_id` INT NOT NULL,
  `attribute_name` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_config_details_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_attribute_values_mst_component_attribute_map1`
    FOREIGN KEY (`mst_component_attribute_mapping_id`)
    REFERENCES `appsone_integration_test`.`mst_component_attribute_mapping` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_attribute_values_mst_common_attributes1`
    FOREIGN KEY (`mst_common_attributes_id`)
    REFERENCES `appsone_integration_test`.`mst_common_attributes` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_comp_instance_config_details_comp_instance1_idx` ON `appsone_integration_test`.`comp_instance_attribute_values` (`comp_instance_id` ASC);

CREATE INDEX `fk_comp_instance_attribute_values_mst_component_attribute_m_idx` ON `appsone_integration_test`.`comp_instance_attribute_values` (`mst_component_attribute_mapping_id` ASC);

CREATE INDEX `fk_comp_instance_attribute_values_mst_common_attributes1_idx` ON `appsone_integration_test`.`comp_instance_attribute_values` (`mst_common_attributes_id` ASC);

CREATE TABLE `appsone_integration_test`.`physical_agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `identifier` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `last_job_id` VARCHAR(128) NULL,
  `last_status_id` INT NULL,
  `is_command_executed` TINYINT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_physical_agent_1_idx` (`last_status_id` ASC),
  CONSTRAINT `fk_physical_agent_1`
    FOREIGN KEY (`last_status_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

CREATE UNIQUE INDEX `physical_agent_UNIQUE` ON `appsone_integration_test`.`physical_agent` (`identifier` ASC);

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`agent`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`agent` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `unique_token` VARCHAR(128) NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `agent_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `host_address` VARCHAR(128) NOT NULL,
  `mode` VARCHAR(45) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `agent_data_type_id` INT NOT NULL DEFAULT 251,
  `physical_agent_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_agent_mst_sub_type1`
    FOREIGN KEY (`agent_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_physical_agent1`
    FOREIGN KEY (`physical_agent_id`)
    REFERENCES `appsone_integration_test`.`physical_agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_agent_mst_sub_type1_idx` ON `appsone_integration_test`.`agent` (`agent_type_id` ASC);

CREATE UNIQUE INDEX `unique_token_UNIQUE` ON `appsone_integration_test`.`agent` (`unique_token` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`server_type_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`server_type_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`server_type_details` (
  `id` INT NOT NULL,
  `vendor` VARCHAR(45) NULL,
  `server_type_id` INT NULL,
  `description` VARCHAR(45) NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_kpi_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_kpi_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_kpi_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `description` MEDIUMTEXT NOT NULL,
  `data_type` VARCHAR(32) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  `kpi_type_id` INT NOT NULL,
  `measure_units` VARCHAR(16) NULL,
  `cluster_operation` VARCHAR(16) NULL DEFAULT 'NONE',
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `kpi_group_id` INT NOT NULL DEFAULT 0,
  `alias_name` VARCHAR(128) NOT NULL,
  `value_type` VARCHAR(45) NULL DEFAULT 'SNAPSHOT',
  `rollup_operation` VARCHAR(16) NOT NULL,
  `cluster_aggregation_type` INT NULL,
  `instance_aggregation_type` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_kpi_details_mst_sub_type1`
    FOREIGN KEY (`kpi_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_kpi_details_mst_sub_type1_idx` ON `appsone_integration_test`.`mst_kpi_details` (`kpi_type_id` ASC);

CREATE INDEX `mst_kpi_details_account1_idx` ON `appsone_integration_test`.`mst_kpi_details` (`account_id` ASC);

CREATE INDEX `indx_mst_kpi_details_account_id` ON `appsone_integration_test`.`mst_kpi_details` (`kpi_group_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_timezone`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_timezone` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_timezone` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `time_zone_id` VARCHAR(128) NOT NULL,
  `timeoffset` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_timezone_account1_idx` ON `appsone_integration_test`.`mst_timezone` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`agent_comp_instance_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`agent_comp_instance_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`agent_comp_instance_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `comp_instance_id` INT NOT NULL,
  `agent_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_server_agent_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_server_agent_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone_integration_test`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_server_agent_comp_instance1_idx` ON `appsone_integration_test`.`agent_comp_instance_mapping` (`comp_instance_id` ASC);

CREATE INDEX `fk_server_agent_agent1_idx` ON `appsone_integration_test`.`agent_comp_instance_mapping` (`agent_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_kpi_group`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_kpi_group` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_kpi_group` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(32) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `kpi_type_id` INT NOT NULL,
  `discovery` TINYINT NULL,
  `regex` VARCHAR(64) NULL,
  `status` TINYINT NOT NULL,
  `is_custom` TINYINT NOT NULL,
  `alias_name` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_kpi_group_mst_sub_type1`
    FOREIGN KEY (`kpi_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `indx_mst_kpi_group_account_id` ON `appsone_integration_test`.`mst_kpi_group` (`account_id` ASC);

CREATE INDEX `fk_mst_kpi_group_mst_sub_type1_idx` ON `appsone_integration_test`.`mst_kpi_group` (`kpi_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_component_version_kpi_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_component_version_kpi_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_component_version_kpi_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_kpi_details_id` INT NOT NULL,
  `is_custom` TINYINT(1) NULL DEFAULT 0,
  `do_analytics` TINYINT(1) NULL DEFAULT 0,
  `mst_common_version_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `default_collection_interval` INT NOT NULL DEFAULT 60,
  `status` TINYINT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  `default_operation_id` INT NULL,
  `default_threshold` DOUBLE NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_version_kpi_mapping_mst_kpi_details1`
    FOREIGN KEY (`mst_kpi_details_id`)
    REFERENCES `appsone_integration_test`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_kpi_mapping_mst_common_version1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone_integration_test`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_kpi_mapping_component_id`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone_integration_test`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_kpi_mapping_component_type_id`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone_integration_test`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_component_version_kpi_mapping_mst_kpi_details1_idx` ON `appsone_integration_test`.`mst_component_version_kpi_mapping` (`mst_kpi_details_id` ASC);

CREATE INDEX `fk_mst_component_version_kpi_mapping_mst_common_version1_idx` ON `appsone_integration_test`.`mst_component_version_kpi_mapping` (`mst_common_version_id` ASC);

CREATE INDEX `fk_mst_component_version_kpi_mapping_component_id_idx` ON `appsone_integration_test`.`mst_component_version_kpi_mapping` (`mst_component_id` ASC);

CREATE INDEX `fk_mst_component_version_kpi_mapping_component_type_id_idx` ON `appsone_integration_test`.`mst_component_version_kpi_mapping` (`mst_component_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_producer_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_producer_type` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_producer_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `classname` VARCHAR(128) NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `parameter_type_id` int(11) DEFAULT NULL,
  `producer_table_name` VARCHAR(64) NULL,
  `account_id` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_producer_type_mst_type`
  FOREIGN KEY (`parameter_type_id`)
  REFERENCES `mst_type` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 1,
  `status` TINYINT NOT NULL DEFAULT 1,
  `version` VARCHAR(45) NULL,
  `is_deprecated` TINYINT(1) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `mst_producer_type_id` INT NOT NULL,
  `mst_sub_type_id` INT NOT NULL,
  `is_kpi_group` TINYINT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_producer_mst_producer_type1`
    FOREIGN KEY (`mst_producer_type_id`)
    REFERENCES `appsone_integration_test`.`mst_producer_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_type`
    FOREIGN KEY (`mst_sub_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `producer_account1_idx` ON `appsone_integration_test`.`mst_producer` (`account_id` ASC);

CREATE INDEX `fk_mst_producer_mst_producer_type1_idx` ON `appsone_integration_test`.`mst_producer` (`mst_producer_type_id` ASC);

CREATE INDEX `fk_mst_producer_mst_sub_type1_idx` ON `appsone_integration_test`.`mst_producer` (`mst_sub_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_component_version`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_component_version` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_component_version` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL,
  `status` TINYINT NOT NULL,
  `version_from` SMALLINT NULL,
  `version_to` SMALLINT NULL,
  `mst_common_version_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_component_version_mst_common_version1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone_integration_test`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone_integration_test`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_component_version_mst_common_version1_idx` ON `appsone_integration_test`.`mst_component_version` (`mst_common_version_id` ASC);

CREATE INDEX `fk_mst_component_version_mst_component1_idx` ON `appsone_integration_test`.`mst_component_version` (`mst_component_id` ASC);

CREATE INDEX `mst_component_version_account1_idx` ON `appsone_integration_test`.`mst_component_version` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_producer_kpi_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_producer_kpi_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_producer_kpi_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `producer_id` INT NOT NULL,
  `is_default` TINYINT(1) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `mst_kpi_details_id` INT NOT NULL,
  `mst_component_version_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_producer_component_kpi_mapping_producer1`
    FOREIGN KEY (`producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_mapping_mst_kpi_details1`
    FOREIGN KEY (`mst_kpi_details_id`)
    REFERENCES `appsone_integration_test`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_mapping_mst_component_version1`
    FOREIGN KEY (`mst_component_version_id`)
    REFERENCES `appsone_integration_test`.`mst_component_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_mapping_component_id`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone_integration_test`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_producer_kpi_mapping_component_type_id`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone_integration_test`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_producer_component_kpi_mapping_producer1_idx` ON `appsone_integration_test`.`mst_producer_kpi_mapping` (`producer_id` ASC);

CREATE INDEX `mst_producer_kpi_mapping_account1_idx` ON `appsone_integration_test`.`mst_producer_kpi_mapping` (`account_id` ASC);

CREATE INDEX `fk_mst_producer_kpi_mapping_mst_kpi_details1_idx` ON `appsone_integration_test`.`mst_producer_kpi_mapping` (`mst_kpi_details_id` ASC);

CREATE INDEX `fk_mst_producer_kpi_mapping_mst_component_version1_idx` ON `appsone_integration_test`.`mst_producer_kpi_mapping` (`mst_component_version_id` ASC);

CREATE INDEX `fk_mst_producer_kpi_mapping_component_id_idx` ON `appsone_integration_test`.`mst_producer_kpi_mapping` (`mst_component_id` ASC);

CREATE INDEX `fk_mst_producer_kpi_mapping_component_type_id_idx` ON `appsone_integration_test`.`mst_producer_kpi_mapping` (`mst_component_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`comp_instance_kpi_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`comp_instance_kpi_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`comp_instance_kpi_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `comp_instance_id` INT NOT NULL,
  `mst_producer_kpi_mapping_id` INT NOT NULL,
  `collection_interval` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_kpi_details_id` INT NOT NULL,
  `mst_producer_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_kpi_details_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_details_mst_producer_kpi_mapping1`
    FOREIGN KEY (`mst_producer_kpi_mapping_id`)
    REFERENCES `appsone_integration_test`.`mst_producer_kpi_mapping` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_details_mst_kpi_details1`
    FOREIGN KEY (`mst_kpi_details_id`)
    REFERENCES `appsone_integration_test`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_details_producer_id`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_comp_instance_kpi_details_comp_instance1_idx` ON `appsone_integration_test`.`comp_instance_kpi_details` (`comp_instance_id` ASC);

CREATE INDEX `fk_comp_instance_kpi_details_mst_producer_kpi_mapping1_idx` ON `appsone_integration_test`.`comp_instance_kpi_details` (`mst_producer_kpi_mapping_id` ASC);

CREATE INDEX `fk_comp_instance_kpi_details_mst_kpi_details1_idx` ON `appsone_integration_test`.`comp_instance_kpi_details` (`mst_kpi_details_id` ASC);

CREATE INDEX `fk_comp_instance_kpi_details_producer_id_idx` ON `appsone_integration_test`.`comp_instance_kpi_details` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`server_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`server_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`server_details` (
  `Id` INT NOT NULL AUTO_INCREMENT,
  `interface` VARCHAR(45) NOT NULL,
  `physical_address` VARCHAR(256) NOT NULL,
  `lowerbound_port` INT NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `upperbound_port` INT NULL,
  `virtual_ip` TINYINT(1) NOT NULL,
  `virtual_host` VARCHAR(256) NULL,
  `protocol_id` INT NOT NULL,
  `key_file_path` VARCHAR(516) NULL,
  `key_file_password` VARCHAR(516) NULL,
  `response_header` TINYINT NOT NULL DEFAULT 0,
  `offset` INT NULL DEFAULT 0,
  `size` INT NULL DEFAULT 100,
  `response_body` TINYINT NOT NULL DEFAULT 0,
  `response_time_type_id` INT NOT NULL,
  PRIMARY KEY (`Id`),
  CONSTRAINT `fk_server_details_1`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_server_details_1_idx` ON `appsone_integration_test`.`server_details` (`response_time_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`agent_server_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`agent_server_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`agent_server_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `agent_id` INT NOT NULL,
  `server_details_Id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_agent_server_mapping_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone_integration_test`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_server_mapping_server_details1`
    FOREIGN KEY (`server_details_Id`)
    REFERENCES `appsone_integration_test`.`server_details` (`Id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_agent_server_mapping_agent1_idx` ON `appsone_integration_test`.`agent_server_mapping` (`agent_id` ASC);

CREATE INDEX `fk_agent_server_mapping_server_details1_idx` ON `appsone_integration_test`.`agent_server_mapping` (`server_details_Id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`audit_table`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`audit_table` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`audit_table` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `time` DATETIME NOT NULL,
  `table_name` VARCHAR(128) NOT NULL,
  `operation` VARCHAR(64) NOT NULL,
  `object_id` INT NOT NULL,
  `db_user` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`component_cluster_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`component_cluster_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`component_cluster_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `comp_instance_id` INT NOT NULL,
  `cluster_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_cluster_mapping_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_cluster_mapping_comp_instance2`
    FOREIGN KEY (`cluster_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_component_cluster_mapping_comp_instance1_idx` ON `appsone_integration_test`.`component_cluster_mapping` (`comp_instance_id` ASC);

CREATE INDEX `fk_component_cluster_mapping_comp_instance2_idx` ON `appsone_integration_test`.`component_cluster_mapping` (`cluster_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`data_communication_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`data_communication_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`data_communication_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `protocol_id` INT NOT NULL,
  `host` VARCHAR(512) NOT NULL,
  `port` INT NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_data_communication_details_2`
    FOREIGN KEY (`protocol_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_data_communication_details_2_idx` ON `appsone_integration_test`.`data_communication_details` (`protocol_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`component_agent`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`component_agent` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`component_agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `agent_id` INT NOT NULL,
  `timeout_multiplier` TINYINT NULL DEFAULT 2,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `config_operation_mode_id` INT NOT NULL,
  `data_operation_mode_id` INT NOT NULL,
  `data_communication_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_agent_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone_integration_test`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_agent_2`
    FOREIGN KEY (`config_operation_mode_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_agent_3`
    FOREIGN KEY (`data_operation_mode_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_agent_4`
    FOREIGN KEY (`data_communication_id`)
    REFERENCES `appsone_integration_test`.`data_communication_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_component_agent_agent1_idx` ON `appsone_integration_test`.`component_agent` (`agent_id` ASC);

CREATE UNIQUE INDEX `component_agent_id_UNIQUE` ON `appsone_integration_test`.`component_agent` (`agent_id` ASC);

CREATE INDEX `fk_component_agent_2_idx` ON `appsone_integration_test`.`component_agent` (`config_operation_mode_id` ASC);

CREATE INDEX `fk_component_agent_3_idx` ON `appsone_integration_test`.`component_agent` (`data_operation_mode_id` ASC);

CREATE INDEX `fk_component_agent_4_idx` ON `appsone_integration_test`.`component_agent` (`data_communication_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`jim_agent`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`jim_agent` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`jim_agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `jvm_id` INT NOT NULL,
  `agent_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jim_agent_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone_integration_test`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_agent_2`
    FOREIGN KEY (`jvm_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_jim_agent_agent1_idx` ON `appsone_integration_test`.`jim_agent` (`agent_id` ASC);

CREATE UNIQUE INDEX `jim_agent_id_UNIQUE` ON `appsone_integration_test`.`jim_agent` (`agent_id` ASC);

CREATE INDEX `fk_jim_agent_2_idx` ON `appsone_integration_test`.`jim_agent` (`jvm_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`comp_instance_kpi_group_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`comp_instance_kpi_group_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`comp_instance_kpi_group_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `attribute_value` VARCHAR(255) NOT NULL,
  `alias_name` VARCHAR(64),
  `status` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `comp_instance_id` INT NOT NULL,
  `mst_producer_kpi_mapping_id` INT NOT NULL,
  `collection_interval` INT NOT NULL DEFAULT 60,
  `mst_kpi_details_id` INT NOT NULL,
  `is_discovery` TINYINT NOT NULL,
  `kpi_group_name` VARCHAR(512) NOT NULL,
  `mst_kpi_group_id` INT NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `alias_name` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_kpi_group_details_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_group_details_mst_producer_kpi_mapping1`
    FOREIGN KEY (`mst_producer_kpi_mapping_id`)
    REFERENCES `appsone_integration_test`.`mst_producer_kpi_mapping` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_group_details_mst_kpi_details1`
    FOREIGN KEY (`mst_kpi_details_id`)
    REFERENCES `appsone_integration_test`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_group_details_group_id`
    FOREIGN KEY (`mst_kpi_group_id`)
    REFERENCES `appsone_integration_test`.`mst_kpi_group` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_group_details_producer_id`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_comp_instance_kpi_group_details_comp_instance1_idx` ON `appsone_integration_test`.`comp_instance_kpi_group_details` (`comp_instance_id` ASC);

CREATE INDEX `fk_comp_instance_kpi_group_details_mst_producer_kpi_mapping_idx` ON `appsone_integration_test`.`comp_instance_kpi_group_details` (`mst_producer_kpi_mapping_id` ASC);

CREATE UNIQUE INDEX `comp_inst_grp_name_UNIQUE` ON `appsone_integration_test`.`comp_instance_kpi_group_details` (`attribute_value` ASC, `comp_instance_id` ASC, `mst_kpi_details_id` ASC);

CREATE INDEX `fk_comp_instance_kpi_group_details_mst_kpi_details1_idx` ON `appsone_integration_test`.`comp_instance_kpi_group_details` (`mst_kpi_details_id` ASC);

CREATE INDEX `fk_comp_instance_kpi_group_details_group_id_idx` ON `appsone_integration_test`.`comp_instance_kpi_group_details` (`mst_kpi_group_id` ASC);

CREATE INDEX `fk_comp_instance_kpi_group_details_producer_id_idx` ON `appsone_integration_test`.`comp_instance_kpi_group_details` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`matcher_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`matcher_type` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`matcher_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`matcher_type_attributes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`matcher_type_attributes` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`matcher_type_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `is_mandatory` TINYINT NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `matcher_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_matcher_type_attributes_matcher_type1`
    FOREIGN KEY (`matcher_type_id`)
    REFERENCES `appsone_integration_test`.`matcher_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_matcher_type_attributes_matcher_type1_idx` ON `appsone_integration_test`.`matcher_type_attributes` (`matcher_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`transaction_matcher_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`transaction_matcher_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`transaction_matcher_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `sub_transaction_id` INT NOT NULL,
  `transaction_attribute_id` INT NOT NULL,
  `attribute_1` VARCHAR(256) NOT NULL,
  `attribute_2` VARCHAR(256) NULL,
  `attribute_3` INT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_matcher_details_mst_sub_type1`
    FOREIGN KEY (`transaction_attribute_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_matcher_details_sub_transaction1`
    FOREIGN KEY (`sub_transaction_id`)
    REFERENCES `appsone_integration_test`.`sub_transactions` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_transaction_matcher_details_mst_sub_type1_idx` ON `appsone_integration_test`.`transaction_matcher_details` (`transaction_attribute_id` ASC);

CREATE INDEX `fk_transaction_matcher_details_sub_transaction1_idx` ON `appsone_integration_test`.`transaction_matcher_details` (`sub_transaction_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`account`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`account` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`account` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE UNIQUE INDEX `identifier_UNIQUE_account` ON `appsone_integration_test`.`account` (`identifier` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`transaction_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`transaction_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`transaction_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `transaction_id` INT NOT NULL,
  `transaction_kpi_type_id` INT NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` DOUBLE NOT NULL,
  `max_threshold` DOUBLE NOT NULL,
  `response_time_type_id` INT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_threshold_details_3`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone_integration_test`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_threshold_details_4`
    FOREIGN KEY (`transaction_kpi_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_threshold_details_5`
    FOREIGN KEY (`operation_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_threshold_details_7`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_threshold_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_transaction_threshold_details_3_idx` ON `appsone_integration_test`.`transaction_threshold_details` (`transaction_id` ASC);

CREATE INDEX `fk_transaction_threshold_details_4_idx` ON `appsone_integration_test`.`transaction_threshold_details` (`transaction_kpi_type_id` ASC);

CREATE INDEX `fk_transaction_threshold_details_5_idx` ON `appsone_integration_test`.`transaction_threshold_details` (`operation_id` ASC);

CREATE INDEX `fk_transaction_threshold_details_7_idx` ON `appsone_integration_test`.`transaction_threshold_details` (`response_time_type_id` ASC);

CREATE INDEX `fk_transaction_threshold_details_1_idx` ON `appsone_integration_test`.`transaction_threshold_details` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`ssh_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`ssh_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`ssh_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_id` INT NOT NULL,
  `script_name` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `signature` TEXT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_ssh_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_ssh_producer_mst_producer1_idx` ON `appsone_integration_test`.`ssh_producer` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`jdbc_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`jdbc_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`jdbc_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_id` INT NOT NULL,
  `query` TEXT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `driver` VARCHAR(64) NOT NULL,
  `url` VARCHAR(256) NOT NULL,
  `query_result` VARCHAR(45) NOT NULL,
  `is_query_encrypted` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jdbc_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE UNIQUE INDEX `mst_producer_id_UNIQUE` ON `appsone_integration_test`.`jdbc_producer` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`producer_parameters`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`producer_parameters` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`producer_parameters` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_id` INT NOT NULL,
  `parameter_type` VARCHAR(45) NULL,
  `parameter_name` VARCHAR(45) NOT NULL,
  `parameter_value` VARCHAR(512) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `parameter_order` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_producer_parameters_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_producer_parameters_mst_producer1_idx` ON `appsone_integration_test`.`producer_parameters` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_common_attribute_type_values`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_common_attribute_type_values` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_common_attribute_type_values` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_common_attributes_id` INT NOT NULL,
  `mst_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_common_attribute_type_values_mst_common_attributes1`
    FOREIGN KEY (`mst_common_attributes_id`)
    REFERENCES `appsone_integration_test`.`mst_common_attributes` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_common_attribute_type_values_mst_type1`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone_integration_test`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_common_attribute_type_values_mst_common_attributes1_idx` ON `appsone_integration_test`.`mst_common_attribute_type_values` (`mst_common_attributes_id` ASC);

CREATE INDEX `fk_mst_common_attribute_type_values_mst_type1_idx` ON `appsone_integration_test`.`mst_common_attribute_type_values` (`mst_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`jmx_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`jmx_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`jmx_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `target_object_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `url` VARCHAR(256) NOT NULL,
  `attribute_data_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jmx_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jmx_producer_attribute_data`
    FOREIGN KEY (`attribute_data_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_jmx_producer_mst_producer1_idx` ON `appsone_integration_test`.`jmx_producer` (`mst_producer_id` ASC);

CREATE INDEX `fk_jmx_producer_attribute_data_idx` ON `appsone_integration_test`.`jmx_producer` (`attribute_data_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`httpd_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`httpd_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`httpd_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `status_url` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_http_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_http_producer_mst_producer1_idx` ON `appsone_integration_test`.`httpd_producer` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`shell_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`shell_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`shell_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `script_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `signature` TEXT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_shell_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_shell_producer_mst_producer1_idx` ON `appsone_integration_test`.`shell_producer` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`was_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`was_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`was_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `target_object_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `module` VARCHAR(45) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_was_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_was_producer_mst_producer1_idx` ON `appsone_integration_test`.`was_producer` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_component_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_component_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_component_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_component_type_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_component_mapping_mst_component_type1`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone_integration_test`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_mapping_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone_integration_test`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_component_mapping_mst_component_type1_idx` ON `appsone_integration_test`.`mst_component_mapping` (`mst_component_type_id` ASC);

CREATE INDEX `fk_mst_component_mapping_mst_component1_idx` ON `appsone_integration_test`.`mst_component_mapping` (`mst_component_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`agent_account_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`agent_account_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`agent_account_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `agent_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_agent_account_mapping_agent1`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone_integration_test`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_account_mapping_account1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_agent_account_mapping_agent1_idx` ON `appsone_integration_test`.`agent_account_mapping` (`agent_id` ASC);

CREATE INDEX `fk_agent_account_mapping_account1_idx` ON `appsone_integration_test`.`agent_account_mapping` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`notification_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`notification_profile` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`notification_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `email_flag` TINYINT(1) NOT NULL,
  `email_subject` TINYTEXT NOT NULL,
  `email_body` TEXT NOT NULL,
  `sms_flag` TINYINT(1) NOT NULL,
  `sms_content` VARCHAR(512) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `name` VARCHAR(64) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `notification_type_id` INT NOT NULL,
  `status` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_notification_profile_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_notification_profile_4`
    FOREIGN KEY (`notification_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_notification_profile_1_idx` ON `appsone_integration_test`.`notification_profile` (`account_id` ASC);

CREATE INDEX `fk_notification_profile_4_idx` ON `appsone_integration_test`.`notification_profile` (`notification_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`alert_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`alert_profile` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`alert_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `profile_type_id` INT NOT NULL,
  `notification_profile_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `is_maintenance` TINYINT(1) NULL,
  `description` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_alert_profile_1`
    FOREIGN KEY (`profile_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_2`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_4`
    FOREIGN KEY (`notification_profile_id`)
    REFERENCES `appsone_integration_test`.`notification_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_alert_profile_1_idx` ON `appsone_integration_test`.`alert_profile` (`profile_type_id` ASC);

CREATE INDEX `fk_alert_profile_2_idx` ON `appsone_integration_test`.`alert_profile` (`account_id` ASC);

CREATE INDEX `fk_alert_profile_4_idx` ON `appsone_integration_test`.`alert_profile` (`notification_profile_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`coverage_window_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`coverage_window_profile` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`coverage_window_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `day_option_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT(1) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_coverage_window_profile_1`
    FOREIGN KEY (`day_option_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_coverage_window_profile_2`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_coverage_window_profile_1_idx` ON `appsone_integration_test`.`coverage_window_profile` (`day_option_id` ASC);

CREATE INDEX `fk_coverage_window_profile_2_idx` ON `appsone_integration_test`.`coverage_window_profile` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`coverage_window`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`coverage_window` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`coverage_window` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `day` VARCHAR(16) NOT NULL,
  `start_hour` SMALLINT NOT NULL,
  `start_minute` SMALLINT NOT NULL,
  `end_hour` SMALLINT NOT NULL,
  `end_minute` SMALLINT NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `is_business_hour` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_coverage_window_1`
    FOREIGN KEY (`coverage_window_profile_id`)
    REFERENCES `appsone_integration_test`.`coverage_window_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_coverage_window_1_idx` ON `appsone_integration_test`.`coverage_window` (`coverage_window_profile_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`severity_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`severity_profile` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`severity_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `high_enable` TINYINT(1) NOT NULL,
  `high_persistence` INT NULL,
  `medium_enable` TINYINT(1) NOT NULL,
  `medium_persistence` INT NULL,
  `low_enable` TINYINT(1) NOT NULL,
  `low_persistence` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `profile_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_severity_profile_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_severity_profile_4`
    FOREIGN KEY (`profile_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_severity_profile_1_idx` ON `appsone_integration_test`.`severity_profile` (`account_id` ASC);

CREATE INDEX `fk_severity_profile_4_idx` ON `appsone_integration_test`.`severity_profile` (`profile_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`escalation_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`escalation_profile` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`escalation_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `escalation_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_escalation_profile_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_escalation_profile_3`
    FOREIGN KEY (`escalation_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_escalation_profile_1_idx` ON `appsone_integration_test`.`escalation_profile` (`account_id` ASC);

CREATE INDEX `fk_escalation_profile_3_idx` ON `appsone_integration_test`.`escalation_profile` (`escalation_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`escalation_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`escalation_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`escalation_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `level_name` VARCHAR(64) NOT NULL,
  `level_number` SMALLINT NOT NULL,
  `email_to` VARCHAR(512) NULL,
  `email_cc` VARCHAR(512) NULL,
  `email_bcc` VARCHAR(512) NULL,
  `sms_numbers` VARCHAR(512) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `escalation_profile_id` INT NOT NULL,
  `high_suppression` INT NULL,
  `medium_suppression` INT NULL,
  `low_suppression` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_escalation_details_1`
    FOREIGN KEY (`escalation_profile_id`)
    REFERENCES `appsone_integration_test`.`escalation_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_escalation_details_1_idx` ON `appsone_integration_test`.`escalation_details` (`escalation_profile_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`comp_instance_kpi_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`comp_instance_kpi_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`comp_instance_kpi_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `comp_instance_id` INT NOT NULL,
  `kpi_id` INT NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `kpi_group_id` INT NULL,
  `kpi_group_value` VARCHAR(512) NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_kpi_threshold_3`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_kpi_threshold_4`
    FOREIGN KEY (`kpi_id`)
    REFERENCES `appsone_integration_test`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_comp_instance_kpi_threshold_3_idx` ON `appsone_integration_test`.`comp_instance_kpi_threshold_details` (`comp_instance_id` ASC);

CREATE INDEX `fk_comp_instance_kpi_threshold_4_idx` ON `appsone_integration_test`.`comp_instance_kpi_threshold_details` (`kpi_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`holiday_profile`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`holiday_profile` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`holiday_profile` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_holiday_profile_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_holiday_profile_1_idx` ON `appsone_integration_test`.`holiday_profile` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`alert_profile_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`alert_profile_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`alert_profile_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `coverage_window_profile_id` INT NOT NULL,
  `severity_profile_id` INT NULL,
  `escalation_profile_id` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `alert_profile_id` INT NOT NULL,
  `name` VARCHAR(64) NOT NULL,
  `holiday_profile_id` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_alert_profile_mapping_1`
    FOREIGN KEY (`escalation_profile_id`)
    REFERENCES `appsone_integration_test`.`escalation_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_mapping_2`
    FOREIGN KEY (`severity_profile_id`)
    REFERENCES `appsone_integration_test`.`severity_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_mapping_3`
    FOREIGN KEY (`coverage_window_profile_id`)
    REFERENCES `appsone_integration_test`.`coverage_window_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_mapping_6`
    FOREIGN KEY (`alert_profile_id`)
    REFERENCES `appsone_integration_test`.`alert_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_alert_profile_mapping_5`
    FOREIGN KEY (`holiday_profile_id`)
    REFERENCES `appsone_integration_test`.`holiday_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_alert_profile_mapping_1_idx` ON `appsone_integration_test`.`alert_profile_mapping` (`escalation_profile_id` ASC);

CREATE INDEX `fk_alert_profile_mapping_2_idx` ON `appsone_integration_test`.`alert_profile_mapping` (`severity_profile_id` ASC);

CREATE INDEX `fk_alert_profile_mapping_3_idx` ON `appsone_integration_test`.`alert_profile_mapping` (`coverage_window_profile_id` ASC);

CREATE INDEX `fk_alert_profile_mapping_6_idx` ON `appsone_integration_test`.`alert_profile_mapping` (`alert_profile_id` ASC);

CREATE INDEX `fk_alert_profile_mapping_5_idx` ON `appsone_integration_test`.`alert_profile_mapping` (`holiday_profile_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`protocol_stack_agent`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`protocol_stack_agent` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`protocol_stack_agent` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `config_operation_mode_id` INT NOT NULL,
  `data_operation_mode_id` INT NOT NULL,
  `created_time` DATETIME NULL,
  `updated_time` DATETIME NULL,
  `dbts` INT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `agent_id` INT NOT NULL,
  `data_flag_id` INT NOT NULL,
  `response_time_type_id` INT NOT NULL,
  `data_communication_id` INT NOT NULL,
  `http_proxy_id` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_protocol_stack_agent_3`
    FOREIGN KEY (`config_operation_mode_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_4`
    FOREIGN KEY (`data_operation_mode_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_6`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone_integration_test`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_7`
    FOREIGN KEY (`data_flag_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_8`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_2`
    FOREIGN KEY (`data_communication_id`)
    REFERENCES `appsone_integration_test`.`data_communication_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_protocol_stack_agent_5`
    FOREIGN KEY (`http_proxy_id`)
    REFERENCES `appsone_integration_test`.`data_communication_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_protocol_stack_agent_3_idx` ON `appsone_integration_test`.`protocol_stack_agent` (`config_operation_mode_id` ASC);

CREATE INDEX `fk_protocol_stack_agent_4_idx` ON `appsone_integration_test`.`protocol_stack_agent` (`data_operation_mode_id` ASC);

CREATE INDEX `fk_protocol_stack_agent_6_idx` ON `appsone_integration_test`.`protocol_stack_agent` (`agent_id` ASC);

CREATE INDEX `fk_protocol_stack_agent_7_idx` ON `appsone_integration_test`.`protocol_stack_agent` (`data_flag_id` ASC);

CREATE INDEX `fk_protocol_stack_agent_8_idx` ON `appsone_integration_test`.`protocol_stack_agent` (`response_time_type_id` ASC);

CREATE INDEX `fk_protocol_stack_agent_2_idx` ON `appsone_integration_test`.`protocol_stack_agent` (`data_communication_id` ASC);

CREATE INDEX `fk_protocol_stack_agent_5_idx` ON `appsone_integration_test`.`protocol_stack_agent` (`http_proxy_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`http_json_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`http_json_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`http_json_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_id` INT NOT NULL,
  `json_url` VARCHAR(512) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_http_json_producer_2`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_http_json_producer_2_idx` ON `appsone_integration_test`.`http_json_producer` (`mst_producer_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`biz_value_extractor`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`biz_value_extractor` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`biz_value_extractor` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `regex` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `transaction_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_biz_value_extractor_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone_integration_test`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_biz_value_extractor_1_idx` ON `appsone_integration_test`.`biz_value_extractor` (`transaction_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`transaction_audit_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`transaction_audit_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`transaction_audit_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `data_type_id` INT NOT NULL,
  `extractor_name` VARCHAR(128) NOT NULL,
  `extractor_type_id` INT NOT NULL,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `attribute_1` VARCHAR(512) NOT NULL,
  `attribute_2` VARCHAR(512) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_audit_details_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone_integration_test`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_audit_details_2`
    FOREIGN KEY (`data_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_audit_details_4`
    FOREIGN KEY (`extractor_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_transaction_audit_details_1_idx` ON `appsone_integration_test`.`transaction_audit_details` (`transaction_id` ASC);

CREATE INDEX `fk_transaction_audit_details_2_idx` ON `appsone_integration_test`.`transaction_audit_details` (`data_type_id` ASC);

CREATE INDEX `fk_transaction_audit_details_4_idx` ON `appsone_integration_test`.`transaction_audit_details` (`extractor_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`jim_transactions`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`jim_transactions` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`jim_transactions` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `method_name` VARCHAR(256) NOT NULL,
  `method_long_name` VARCHAR(750) NOT NULL,
  `method_type_id` INT NOT NULL,
  `transformation_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jim_transactions_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_transactions_3`
    FOREIGN KEY (`method_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_transactions_4`
    FOREIGN KEY (`transformation_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_jim_transactions_1_idx` ON `appsone_integration_test`.`jim_transactions` (`account_id` ASC);

CREATE INDEX `fk_jim_transactions_3_idx` ON `appsone_integration_test`.`jim_transactions` (`method_type_id` ASC);

CREATE INDEX `fk_jim_transactions_4_idx` ON `appsone_integration_test`.`jim_transactions` (`transformation_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`jim_transaction_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`jim_transaction_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`jim_transaction_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `jim_transaction_id` INT NOT NULL,
  `agent_id` INT NOT NULL,
  `comp_instance_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jim_transaction_mapping_1`
    FOREIGN KEY (`jim_transaction_id`)
    REFERENCES `appsone_integration_test`.`jim_transactions` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_transaction_mapping_2`
    FOREIGN KEY (`agent_id`)
    REFERENCES `appsone_integration_test`.`agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jim_transaction_mapping_3`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone_integration_test`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_jim_transaction_mapping_1_idx` ON `appsone_integration_test`.`jim_transaction_mapping` (`jim_transaction_id` ASC);

CREATE INDEX `fk_jim_transaction_mapping_2_idx` ON `appsone_integration_test`.`jim_transaction_mapping` (`agent_id` ASC);

CREATE INDEX `fk_jim_transaction_mapping_3_idx` ON `appsone_integration_test`.`jim_transaction_mapping` (`comp_instance_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`transaction_response_threshold`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`transaction_response_threshold` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`transaction_response_threshold` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `slow_threshold_value` DOUBLE NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `response_time_type_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `account_id` INT NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_response_threshold_1`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_response_threshold_3`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `appsone_integration_test`.`transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_transaction_response_threshold_1_idx` ON `appsone_integration_test`.`transaction_response_threshold` (`response_time_type_id` ASC);

CREATE INDEX `fk_transaction_response_threshold_3_idx` ON `appsone_integration_test`.`transaction_response_threshold` (`transaction_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`holiday_dates`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`holiday_dates` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`holiday_dates` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `holiday_date` DATE NOT NULL,
  `holiday_profile_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `update_time` DATETIME NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_holiday_dates_1`
    FOREIGN KEY (`holiday_profile_id`)
    REFERENCES `appsone_integration_test`.`holiday_profile` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_holiday_dates_1_idx` ON `appsone_integration_test`.`holiday_dates` (`holiday_profile_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`jppf_producer`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`jppf_producer` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`jppf_producer` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `mst_producer_id` INT NOT NULL,
  `jppf_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_jppf_producer_mst_producer1`
    FOREIGN KEY (`mst_producer_id`)
    REFERENCES `appsone_integration_test`.`mst_producer` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_jppf_producer_jppf_type1`
    FOREIGN KEY (`jppf_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_jppf_producer_mst_producer1_idx` ON `appsone_integration_test`.`jppf_producer` (`mst_producer_id` ASC);

CREATE INDEX `fk_jppf_producer_jppf_type1_idx` ON `appsone_integration_test`.`jppf_producer` (`jppf_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`controller`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`controller` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`controller` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `alias_name` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `controller_type_id` INT NOT NULL,
  `monitor_enabled` TINYINT NOT NULL DEFAULT 1,
  `status` TINYINT(4) NOT NULL DEFAULT 1,
  `plugin_supr_interval` INT NOT NULL DEFAULT '0',
  `plugin_whitelist_status` TINYINT NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_application_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_application_account_id_idx` ON `appsone_integration_test`.`controller` (`account_id` ASC);

CREATE UNIQUE INDEX `name_UNIQUE` ON `appsone_integration_test`.`controller` (`alias_name` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`tag_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`tag_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`tag_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `tag_type_id` INT NOT NULL,
  `is_predefined` TINYINT NOT NULL,
  `ref_table` VARCHAR(64) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `ref_where_column_name` VARCHAR(128) NULL,
  `ref_select_column_name` VARCHAR(126) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id`
    FOREIGN KEY (`tag_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_details_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_tag_details_tag_type_id_idx` ON `appsone_integration_test`.`tag_details` (`tag_type_id` ASC);

CREATE INDEX `fk_tag_details_account_id_idx` ON `appsone_integration_test`.`tag_details` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`tag_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`tag_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`tag_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `tag_id` INT NOT NULL,
  `object_id` INT NOT NULL,
  `object_ref_table` VARCHAR(256) NOT NULL,
  `tag_key` VARCHAR(256) NOT NULL,
  `tag_value` VARCHAR(256) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id`
    FOREIGN KEY (`tag_id`)
    REFERENCES `appsone_integration_test`.`tag_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_mapping_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_tag_mapping_account_id_idx` ON `appsone_integration_test`.`tag_mapping` (`account_id` ASC);

CREATE INDEX `fk_tag_mapping_tag_id_idx` ON `appsone_integration_test`.`tag_mapping` (`tag_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`connection_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`connection_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`connection_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `source_id` INT NULL,
  `source_ref_object` VARCHAR(64) NULL,
  `destination_id` INT NULL,
  `destination_ref_object` VARCHAR(64) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `is_discovery` TINYINT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`application_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`application_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`application_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `application_id` INT NOT NULL,
  `transaction_kpi_type_id` INT NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` DOUBLE NOT NULL,
  `max_threshold` DOUBLE NOT NULL,
  `response_time_type_id` INT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_application_threshold_details_3`
    FOREIGN KEY (`application_id`)
    REFERENCES `appsone_integration_test`.`controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_application_threshold_details_4`
    FOREIGN KEY (`transaction_kpi_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_application_threshold_details_5`
    FOREIGN KEY (`operation_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_application_threshold_details_7`
    FOREIGN KEY (`response_time_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_application_threshold_details_4_idx` ON `appsone_integration_test`.`application_threshold_details` (`transaction_kpi_type_id` ASC);

CREATE INDEX `fk_application_threshold_details_5_idx` ON `appsone_integration_test`.`application_threshold_details` (`operation_id` ASC);

CREATE INDEX `fk_application_threshold_details_7_idx` ON `appsone_integration_test`.`application_threshold_details` (`response_time_type_id` ASC);

CREATE INDEX `fk_application_threshold_details_3_idx` ON `appsone_integration_test`.`application_threshold_details` (`application_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`component_kpi_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`component_kpi_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`component_kpi_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_component_id` INT NOT NULL,
  `kpi_id` INT NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `kpi_group_id` INT NULL,
  `kpi_group_value` VARCHAR(512) NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `mst_common_version_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_kpi_threshold_3`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone_integration_test`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_kpi_threshold_4`
    FOREIGN KEY (`kpi_id`)
    REFERENCES `appsone_integration_test`.`mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_kpi_threshold_details_1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone_integration_test`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_comp_instance_kpi_threshold_4_idx_1` ON `appsone_integration_test`.`component_kpi_threshold_details` (`kpi_id` ASC);

CREATE INDEX `fk_component_kpi_threshold_3_idx` ON `appsone_integration_test`.`component_kpi_threshold_details` (`mst_component_id` ASC);

CREATE INDEX `fk_component_kpi_threshold_details_1_idx` ON `appsone_integration_test`.`component_kpi_threshold_details` (`mst_common_version_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`file_upload_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`file_upload_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`file_upload_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `file_name` VARCHAR(64) NOT NULL,
  `file_size` MEDIUMTEXT NOT NULL,
  `checksum` VARCHAR(64) NOT NULL,
  `file_location` VARCHAR(256) NOT NULL,
  `upload_by` VARCHAR(64) NOT NULL,
  `upload_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `is_processing` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_file_upload_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_file_upload_details_1_idx` ON `appsone_integration_test`.`file_upload_details` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`file_processed_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`file_processed_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`file_processed_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `file_name` VARCHAR(64) NOT NULL,
  `file_size` MEDIUMTEXT NOT NULL,
  `checksum` VARCHAR(64) NOT NULL,
  `file_location` VARCHAR(256) NOT NULL,
  `upload_by` VARCHAR(64) NOT NULL,
  `upload_time` DATETIME NOT NULL,
  `processed_by` VARCHAR(64) NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `status` VARCHAR(32) NOT NULL,
  `account_id` INT NOT NULL,
  `progress` VARCHAR(32) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_file_processed_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_file_processed_details_1_idx` ON `appsone_integration_test`.`file_processed_details` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`file_summary_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`file_summary_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`file_summary_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `file_processed_id` INT NOT NULL,
  `key` VARCHAR(64) NOT NULL,
  `value` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NULL,
  `account_id` INT NOT NULL,
  `is_debug_logs` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_file_summary_details_1`
    FOREIGN KEY (`file_processed_id`)
    REFERENCES `appsone_integration_test`.`file_processed_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_file_summary_details_1_idx` ON `appsone_integration_test`.`file_summary_details` (`file_processed_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`flow_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`flow_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`flow_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `flow_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_flow_details_1_idx` ON `appsone_integration_test`.`flow_details` (`account_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE_flow_details` ON `appsone_integration_test`.`flow_details` (`identifier` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`step_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`step_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`step_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `sequence_number` INT NOT NULL,
  `account_id` INT NOT NULL,
  `flow_id` INT NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `identifier` VARCHAR(64) NOT NULL,
  `monitor_enabled` TINYINT NOT NULL,
  `trace_enabled` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_step_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone_integration_test`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_step_details_1_idx` ON `appsone_integration_test`.`step_details` (`account_id` ASC);

CREATE INDEX `fk_step_details_2_idx` ON `appsone_integration_test`.`step_details` (`flow_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE_step_details` ON `appsone_integration_test`.`step_details` (`identifier` ASC);

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`step_connection_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`step_connection_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`step_connection_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `source_id` INT NULL,
  `destination_id` INT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `is_discovery` TINYINT NOT NULL DEFAULT 0,
  `flow_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_connection_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_step_connection_details_1_idx` ON `appsone_integration_test`.`step_connection_details` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`step_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`step_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`step_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `kpi_name` VARCHAR(64) NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `step_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_threshold_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone_integration_test`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_step_threshold_details_1`
    FOREIGN KEY (`step_id`)
    REFERENCES `appsone_integration_test`.`step_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_step_threshold_details_1_idx` ON `appsone_integration_test`.`step_threshold_details` (`step_id` ASC);

CREATE INDEX `fk_step_threshold_details_2_idx` ON `appsone_integration_test`.`step_threshold_details` (`step_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`flow_segments`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`flow_segments` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`flow_segments` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `segment_name` VARCHAR(64) NOT NULL,
  `attribute_lookup_name` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `is_default_enabled` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_segments_1`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone_integration_test`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_flow_segments_1_idx` ON `appsone_integration_test`.`flow_segments` (`flow_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`flow_segment_lookup`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`flow_segment_lookup` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`flow_segment_lookup` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `attribute_lookup_name` VARCHAR(64) NOT NULL,
  `data_value` VARCHAR(128) NOT NULL,
  `display_value` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_segment_lookup_1`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone_integration_test`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_flow_segment_lookup_1_idx` ON `appsone_integration_test`.`flow_segment_lookup` (`flow_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`step_stitch_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`step_stitch_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`step_stitch_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `step_id` INT NOT NULL,
  `stitch_from` VARCHAR(64) NOT NULL,
  `stitch_field` VARCHAR(64) NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `flow_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_stitch_details_1`
    FOREIGN KEY (`step_id`)
    REFERENCES `appsone_integration_test`.`step_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_step_stitch_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone_integration_test`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_step_stitch_details_1_idx` ON `appsone_integration_test`.`step_stitch_details` (`step_id` ASC);

CREATE INDEX `fk_step_stitch_details_2_idx` ON `appsone_integration_test`.`step_stitch_details` (`flow_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`flow_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`flow_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`flow_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `kpi_name` VARCHAR(64) NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_threshold_details_1`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone_integration_test`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_flow_threshold_details_1_idx_3` ON `appsone_integration_test`.`flow_threshold_details` (`flow_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`step_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`step_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`step_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `kpi_name` VARCHAR(64) NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `step_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_step_threshold_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone_integration_test`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_step_threshold_details_1`
    FOREIGN KEY (`step_id`)
    REFERENCES `appsone_integration_test`.`step_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_flow_threshold_details_1_idx_1` ON `appsone_integration_test`.`step_threshold_details` (`flow_id` ASC);

CREATE INDEX `fk_step_threshold_details_1_idx` ON `appsone_integration_test`.`step_threshold_details` (`step_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`flow_segment_threshold_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`flow_segment_threshold_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`flow_segment_threshold_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `flow_id` INT NOT NULL,
  `kpi_name` VARCHAR(64) NOT NULL,
  `operation_id` INT NOT NULL,
  `min_threshold` FLOAT NOT NULL,
  `max_threshold` FLOAT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `coverage_window_profile_id` INT NOT NULL,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `segment_name` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_flow_segment_threshold_details_2`
    FOREIGN KEY (`flow_id`)
    REFERENCES `appsone_integration_test`.`flow_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_flow_threshold_details_1_idx_2` ON `appsone_integration_test`.`flow_segment_threshold_details` (`flow_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`kiaros_data_store_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`kiaros_data_store_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`kiaros_data_store_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `field` VARCHAR(64) NOT NULL,
  `store` VARCHAR(64) NOT NULL,
  `type` VARCHAR(64) NOT NULL,
  `display_name` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `is_spend_keystore` TINYINT NOT NULL,
  `flow_id` INT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`rules`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`rules` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`rules` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `is_enabled` TINYINT NOT NULL,
  `order` INT NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `rule_type_id` INT NOT NULL,
  `is_default` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_rules_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_rules_2`
    FOREIGN KEY (`rule_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_rules_1_idx` ON `appsone_integration_test`.`rules` (`account_id` ASC);

CREATE INDEX `fk_rules_2_idx` ON `appsone_integration_test`.`rules` (`rule_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`tcp_patterns`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`tcp_patterns` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`tcp_patterns` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `rule_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `initial_pattern` VARCHAR(256) NOT NULL,
  `length` INT NULL,
  `last_pattern` VARCHAR(256) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tcp_patterns_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tcp_patterns_2`
    FOREIGN KEY (`rule_id`)
    REFERENCES `appsone_integration_test`.`rules` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_tcp_patterns_1_idx` ON `appsone_integration_test`.`tcp_patterns` (`account_id` ASC);

CREATE INDEX `fk_tcp_patterns_2_idx` ON `appsone_integration_test`.`tcp_patterns` (`rule_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`http_patterns`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`http_patterns` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`http_patterns` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `rule_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `http_method_type_id` INT NULL,
  `first_uri_segments` INT NULL,
  `last_uri_segments` INT NULL,
  `complete_uri` TINYINT NULL,
  `payload_type_id` INT NOT NULL,
  `complete_pattern` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `custom_segments` VARCHAR(256) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_http_patterns_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_patterns_2`
    FOREIGN KEY (`rule_id`)
    REFERENCES `appsone_integration_test`.`rules` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_patterns_4`
    FOREIGN KEY (`payload_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_http_patterns_1_idx` ON `appsone_integration_test`.`http_patterns` (`account_id` ASC);

CREATE INDEX `fk_http_patterns_2_idx` ON `appsone_integration_test`.`http_patterns` (`rule_id` ASC);

CREATE INDEX `fk_http_patterns_4_idx` ON `appsone_integration_test`.`http_patterns` (`payload_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`http_pair_data`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`http_pair_data` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`http_pair_data` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `rule_id` INT NOT NULL,
  `http_pattern_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `pair_type_id` INT NOT NULL,
  `pair_key` VARCHAR(256) NOT NULL,
  `pair_value` VARCHAR(256) NOT NULL DEFAULT '*',
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_http_pair_data_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_pair_data_2`
    FOREIGN KEY (`http_pattern_id`)
    REFERENCES `appsone_integration_test`.`http_patterns` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_pair_data_3`
    FOREIGN KEY (`rule_id`)
    REFERENCES `appsone_integration_test`.`rules` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_http_pair_data_4`
    FOREIGN KEY (`pair_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_http_pair_data_1_idx` ON `appsone_integration_test`.`http_pair_data` (`account_id` ASC);

CREATE INDEX `fk_http_pair_data_2_idx` ON `appsone_integration_test`.`http_pair_data` (`http_pattern_id` ASC);

CREATE INDEX `fk_http_pair_data_3_idx` ON `appsone_integration_test`.`http_pair_data` (`rule_id` ASC);

CREATE INDEX `fk_http_pair_data_4_idx` ON `appsone_integration_test`.`http_pair_data` (`pair_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_date_component_data`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_date_component_data` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_date_component_data` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `type` VARCHAR(64) NOT NULL,
  `value` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `collation_values` INT NOT NULL,
  `data_points` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_features`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_features` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_features` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `display_name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `is_enabled` TINYINT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_refresh_conf_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_refresh_conf_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_refresh_conf_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `page_name` VARCHAR(256) NOT NULL,
  `refresh_enabled` TINYINT NOT NULL,
  `refresh_in_secs` INT NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_jim_exit_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_jim_exit_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_jim_exit_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `jim_exit_type_id` INT NOT NULL,
  `param_display_name` VARCHAR(128) NOT NULL,
  `param_name` VARCHAR(128) NOT NULL,
  `param_data_type` VARCHAR(32) NOT NULL,
  `is_mandatory` TINYINT NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_jim_exit_details_1`
    FOREIGN KEY (`jim_exit_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_jim_exit_details_1_idx` ON `appsone_integration_test`.`mst_jim_exit_details` (`jim_exit_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_category_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_category_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_category_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_category_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_category_details_1_idx` ON `appsone_integration_test`.`mst_category_details` (`account_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE_mst_category_details` ON `appsone_integration_test`.`mst_category_details` (`identifier` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`forensics`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`forensics` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`forensics` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `standard_type_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `agent_type_id` INT NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_forensics_1`
    FOREIGN KEY (`standard_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_forensics_1_idx` ON `appsone_integration_test`.`forensics` (`standard_type_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE_forensics` ON `appsone_integration_test`.`forensics` (`identifier` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`forensic_category_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`forensic_category_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`forensic_category_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `category_id` INT NOT NULL,
  `forensic_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `time_window_in_secs` INT NOT NULL DEFAULT 30,
  `forensic_exec_type_id` INT NOT NULL,
  `download_type_id` INT NOT NULL,
  `retries` INT NOT NULL,
  `ttl_in_secs` INT NOT NULL,
  `object_id` INT NOT NULL,
  `object_ref_table` VARCHAR(64) NOT NULL,
  `command_exec_type_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_forensic_category_mapping_1`
    FOREIGN KEY (`forensic_id`)
    REFERENCES `appsone_integration_test`.`forensics` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_forensic_category_mapping_2`
    FOREIGN KEY (`category_id`)
    REFERENCES `appsone_integration_test`.`mst_category_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_forensic_category_mapping_3`
    FOREIGN KEY (`forensic_exec_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_forensic_category_mapping_4`
    FOREIGN KEY (`download_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_forensic_category_mapping_1_idx` ON `appsone_integration_test`.`forensic_category_mapping` (`forensic_id` ASC);

CREATE INDEX `fk_forensic_category_mapping_2_idx` ON `appsone_integration_test`.`forensic_category_mapping` (`category_id` ASC);

CREATE INDEX `fk_forensic_category_mapping_3_idx` ON `appsone_integration_test`.`forensic_category_mapping` (`forensic_exec_type_id` ASC);

CREATE INDEX `fk_forensic_category_mapping_4_idx` ON `appsone_integration_test`.`forensic_category_mapping` (`download_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`forensic_http_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`forensic_http_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`forensic_http_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `forensic_category_id` INT NOT NULL,
  `url` VARCHAR(128) NOT NULL,
  `http_method` VARCHAR(128) NOT NULL,
  `protocol` VARCHAR(64) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `time_out_in_mins` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_forensic_http_details_1`
    FOREIGN KEY (`forensic_category_id`)
    REFERENCES `appsone_integration_test`.`forensic_category_mapping` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_forensic_http_details_1_idx` ON `appsone_integration_test`.`forensic_http_details` (`forensic_category_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`command_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`command_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`command_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `command_type_id` INT NOT NULL,
  `command_name` VARCHAR(128) NOT NULL,
  `timeout_in_secs` INT NOT NULL,
  `output_type_id` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `action_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_command_details_1`
  FOREIGN KEY (`action_id`)
  REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
  CONSTRAINT `fk_command_details_2`
  FOREIGN KEY (`command_type_id`)
  REFERENCES `appsone_integration_test`.`mst_type` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION)
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`command_arguments`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`command_arguments` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`command_arguments` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `command_id` INT NOT NULL,
  `argument_key` VARCHAR(64) NOT NULL,
  `argument_value` VARCHAR(128) NOT NULL,
  `default_value` VARCHAR(128) NULL,
  `argument_value_type_id` INT NULL,
  `argument_type_id` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `is_placeholder` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_command_arguments_1`
    FOREIGN KEY (`command_id`)
    REFERENCES `appsone_integration_test`.`command_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_command_arguments_1_idx` ON `appsone_integration_test`.`command_arguments` (`command_id` ASC);

CREATE INDEX `fk_command_arguments_2_idx` ON `appsone_integration_test`.`command_arguments` (`argument_value_type_id` ASC);

CREATE INDEX `fk_command_arguments_3_idx` ON `appsone_integration_test`.`command_arguments` (`argument_type_id` ASC);

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`agent_commands_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`agent_commands_mapping` ;

CREATE TABLE `appsone_integration_test`.`agent_commands_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `agent_type_id` INT NOT NULL,
  `command_id` INT NOT NULL,
  `is_ui_visible` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `is_default` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_agent_commands_mapping_1_idx` (`agent_type_id` ASC),
  INDEX `fk_agent_commands_mapping_2_idx` (`command_id` ASC),
  CONSTRAINT `fk_agent_commands_mapping_1`
    FOREIGN KEY (`agent_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_commands_mapping_2`
    FOREIGN KEY (`command_id`)
    REFERENCES `appsone_integration_test`.`command_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`agent_commands_triggered`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`agent_commands_triggered` ;

CREATE TABLE `appsone_integration_test`.`agent_commands_triggered` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `physical_agent_id` INT NOT NULL,
  `command_id` INT NOT NULL,
  `trigger_time` DATETIME NOT NULL,
  `command_job_id` VARCHAR(128) NOT NULL,
  `command_status` TINYINT NOT NULL DEFAULT 0,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_agent_commands_triggered_1_idx` (`physical_agent_id` ASC),
  INDEX `fk_agent_commands_triggered_2_idx` (`command_id` ASC),
  CONSTRAINT `fk_agent_commands_triggered_1`
    FOREIGN KEY (`physical_agent_id`)
    REFERENCES `appsone_integration_test`.`physical_agent` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_commands_triggered_2`
    FOREIGN KEY (`command_id`)
    REFERENCES `appsone_integration_test`.`command_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone_integration_test`.`supervisor_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`supervisor_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`supervisor_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `host_box_name` VARCHAR(64) NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `supervisor_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `version` VARCHAR(32) NOT NULL,
  `host_address` VARCHAR(64) NOT NULL,
  `status` VARCHAR(32) NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_supervisor_details_1`
    FOREIGN KEY (`supervisor_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_supervisor_details_2`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_supervisor_details_1_idx` ON `appsone_integration_test`.`supervisor_details` (`supervisor_type_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE_supervisor_details` ON `appsone_integration_test`.`supervisor_details` (`identifier` ASC);

CREATE INDEX `fk_supervisor_details_2_idx` ON `appsone_integration_test`.`supervisor_details` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`command_audit_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`command_audit_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`command_audit_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `supervisor_identifier` VARCHAR(128) NOT NULL,
  `agent_identifier` VARCHAR(128) NOT NULL,
  `command_timeout_in_secs` INT NOT NULL,
  `retries` INT NOT NULL,
  `trigger_time` DATETIME NOT NULL,
  `command_complete_time` DATETIME NOT NULL,
  `exit_status` VARCHAR(64) NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `trigger_source` VARCHAR(256) NOT NULL,
  `supr_ctrl_ttl_in_secs` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `violation_time` DATETIME NOT NULL,
  `command_job_id` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`command_metadata_audit_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`command_metadata_audit_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`command_metadata_audit_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `command_audit_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `key` VARCHAR(256) NOT NULL,
  `value` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_command_metadata_audit_details_1`
    FOREIGN KEY (`command_audit_id`)
    REFERENCES `appsone_integration_test`.`command_audit_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_command_metadata_audit_details_1_idx` ON `appsone_integration_test`.`command_metadata_audit_details` (`command_audit_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_jim_exit_type_mapping`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_jim_exit_type_mapping` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_jim_exit_type_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_jim_exit_type_id` INT NOT NULL,
  `framework_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`mst_error_codes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`mst_error_codes` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`mst_error_codes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `a1_component_type_id` INT NOT NULL,
  `error_code` VARCHAR(64) NOT NULL,
  `error_message` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `error_status` VARCHAR(64) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_error_codes_1`
    FOREIGN KEY (`a1_component_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_error_codes_1_idx` ON `appsone_integration_test`.`mst_error_codes` (`a1_component_type_id` ASC);

-- -----------------------------------------------------
-- Table `service_kpi_thresholds`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `service_kpi_thresholds` ;

CREATE TABLE IF NOT EXISTS `service_kpi_thresholds` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `service_id` INT NOT NULL,
  `kpi_id` INT NOT NULL,
  `applicable_to` VARCHAR(64) NOT NULL,
  `operation_type_id` INT ,
  `min_threshold` VARCHAR(128) NULL,
  `max_threshold` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `kpi_attribute` VARCHAR(256) NOT NULL DEFAULT 'ALL',
  `status` TINYINT NOT NULL DEFAULT 1,
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NULL,
  `defined_by` VARCHAR(64) NOT NULL DEFAULT 'USER',
  `sor_operation_type_id` INT NULL,
  `sor_min_threshold` VARCHAR(128) NULL,
  `sor_max_threshold` VARCHAR(128) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_service_kpi_thresholds_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_kpi_thresholds_2`
    FOREIGN KEY (`service_id`)
    REFERENCES `controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_kpi_thresholds_3`
    FOREIGN KEY (`kpi_id`)
    REFERENCES `mst_kpi_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_kpi_thresholds_4`
    FOREIGN KEY (`operation_type_id`)
    REFERENCES `mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_service_kpi_thresholds_1_idx` ON `service_kpi_thresholds` (`account_id` ASC);

CREATE INDEX `fk_service_kpi_thresholds_2_idx` ON `service_kpi_thresholds` (`service_id` ASC);

CREATE INDEX `fk_service_kpi_thresholds_3_idx` ON `service_kpi_thresholds` (`kpi_id` ASC);

CREATE INDEX `fk_service_kpi_thresholds_4_idx` ON `service_kpi_thresholds` (`operation_type_id` ASC);

-- -----------------------------------------------------
-- Table `event_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `event_details` ;

CREATE TABLE IF NOT EXISTS `event_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `event_type` VARCHAR(64) NOT NULL,
  `stitch_field` VARCHAR(128) NULL,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(256) NOT NULL,
  `status` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_event_details_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_event_details_1_idx` ON `event_details` (`transaction_id` ASC);

CREATE UNIQUE INDEX `identifier_UNIQUE_event_details` ON `event_details` (`identifier` ASC);


-- -----------------------------------------------------
-- Table `transaction_attributes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `transaction_attributes` ;

CREATE TABLE IF NOT EXISTS `transaction_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(256) NOT NULL,
  `extraction_field` VARCHAR(128) NOT NULL,
  `is_segment` TINYINT NOT NULL,
  `is_business_status` TINYINT NOT NULL,
  `is_technical_status` TINYINT NOT NULL,
  `is_business_value` TINYINT NOT NULL,
  `is_search_index` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `status` TINYINT NOT NULL,
  `descriptor` VARCHAR(128) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_attributes_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE UNIQUE INDEX `identifier_UNIQUE_transaction_attributes` ON `transaction_attributes` (`identifier` ASC);

CREATE INDEX `fk_transaction_attributes_1_idx` ON `transaction_attributes` (`transaction_id` ASC);


-- -----------------------------------------------------
-- Table `transaction_attribute_data`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `transaction_attribute_data` ;

CREATE TABLE IF NOT EXISTS `transaction_attribute_data` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `transaction_id` INT NOT NULL,
  `transaction_attribute_id` INT NOT NULL,
  `descriptor` VARCHAR(128) NOT NULL,
  `value` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_attribute_data_1`
    FOREIGN KEY (`transaction_id`)
    REFERENCES `transaction` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_transaction_attribute_data_2`
    FOREIGN KEY (`transaction_attribute_id`)
    REFERENCES `transaction_attributes` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_transaction_attribute_data_1_idx` ON `transaction_attribute_data` (`transaction_id` ASC);

CREATE INDEX `fk_transaction_attribute_data_2_idx` ON `transaction_attribute_data` (`transaction_attribute_id` ASC);


-- -----------------------------------------------------
-- Table `email_templates`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `email_templates` ;

CREATE TABLE IF NOT EXISTS `email_templates` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `update_time` DATETIME NOT NULL,
  `subject` VARCHAR(1024) NOT NULL,
  `body` VARCHAR(4096) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `name` VARCHAR(64) NOT NULL,
  `template_type_id` INT NOT NULL,
  `to_address` VARCHAR(516) NULL,
  `cc_address` VARCHAR(516) NULL,
  `bcc_address` VARCHAR(516) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_email_templates_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_email_templates_2`
    FOREIGN KEY (`template_type_id`)
    REFERENCES `mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_email_templates_1_idx` ON `email_templates` (`account_id` ASC);

CREATE INDEX `fk_email_templates_2_idx` ON `email_templates` (`template_type_id` ASC);


-- -----------------------------------------------------
-- Table `sms_templates`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `sms_templates` ;

CREATE TABLE IF NOT EXISTS `sms_templates` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `sms_content` VARCHAR(512) NOT NULL,
  `status` TINYINT NOT NULL,
  `name` VARCHAR(64) NOT NULL,
  `template_type_id` INT NOT NULL,
  `mobile_numbers` VARCHAR(512) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_sms_templates_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_sms_templates_2`
    FOREIGN KEY (`template_type_id`)
    REFERENCES `mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_sms_templates_1_idx` ON `sms_templates` (`account_id` ASC);

CREATE INDEX `fk_sms_templates_2_idx` ON `sms_templates` (`template_type_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`service_configurations`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`service_configurations` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`service_configurations` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `service_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `start_collection_interval` INT NOT NULL,
  `end_collection_interval` INT NOT NULL,
  `sor_persistence` INT NOT NULL,
  `sor_suppression` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_service_configurations_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_configurations_2`
    FOREIGN KEY (`service_id`)
    REFERENCES `appsone_integration_test`.`controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_service_configurations_1_idx` ON `appsone_integration_test`.`service_configurations` (`account_id` ASC);

CREATE INDEX `fk_service_configurations_2_idx` ON `appsone_integration_test`.`service_configurations` (`service_id` ASC);



-- -----------------------------------------------------
-- Table `appsone_integration_test`.`smtp_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`smtp_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`smtp_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `address` VARCHAR(256) NOT NULL,
  `port` INT NOT NULL,
  `username` VARCHAR(256),
  `password` VARCHAR(256),
  `security_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `from_recipient` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_smtp_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_smtp_details_2`
    FOREIGN KEY (`security_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_smtp_details_1_idx` ON `appsone_integration_test`.`smtp_details` (`account_id` ASC);

CREATE INDEX `fk_smtp_details_2_idx` ON `appsone_integration_test`.`smtp_details` (`security_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`sms_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`sms_details` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`sms_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `address` VARCHAR(128) NOT NULL,
  `port` INT NOT NULL,
  `country_code` VARCHAR(32) NULL,
  `protocol_id` INT NOT NULL,
  `http_method` VARCHAR(16) NULL,
  `http_relative_url` VARCHAR(512) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `post_data` MEDIUMTEXT NULL,
  `post_data_flag` TINYINT(1) NULL,
  `status` TINYINT(1) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_sms_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone_integration_test`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_sms_details_2`
    FOREIGN KEY (`protocol_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_sms_details_1_idx` ON `appsone_integration_test`.`sms_details` (`account_id` ASC);

CREATE INDEX `fk_sms_details_2_idx` ON `appsone_integration_test`.`sms_details` (`protocol_id` ASC);


-- -----------------------------------------------------
-- Table `appsone_integration_test`.`sms_parameters`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone_integration_test`.`sms_parameters` ;

CREATE TABLE IF NOT EXISTS `appsone_integration_test`.`sms_parameters` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `parameter_name` VARCHAR(128) NOT NULL,
  `parameter_value` VARCHAR(128) NOT NULL,
  `sms_details_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `parameter_type_id` INT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `default_value` VARCHAR(128) NULL,
  `is_placeholder` TINYINT NOT NULL,
  `status` TINYINT(1) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_sms_parameters_1`
    FOREIGN KEY (`sms_details_id`)
    REFERENCES `appsone_integration_test`.`sms_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_sms_parameters_1_idx` ON `appsone_integration_test`.`sms_parameters` (`sms_details_id` ASC);

DROP TABLE IF EXISTS `appsone_integration_test`.`service_command_arguments`;
CREATE TABLE `appsone_integration_test`.`service_command_arguments` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `service_id` INT NOT NULL,
  `agent_type_id` INT NOT NULL,
  `command_id` INT NOT NULL,
  `command_argument_id` INT NOT NULL,
  `argument_value` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_service_command_arguments_1_idx` (`service_id` ASC),
  INDEX `fk_service_command_arguments_2_idx` (`command_id` ASC),
  INDEX `fk_service_command_arguments_3_idx` (`agent_type_id` ASC),
  CONSTRAINT `fk_service_command_arguments_1`
    FOREIGN KEY (`service_id`)
    REFERENCES `appsone_integration_test`.`controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_command_arguments_2`
    FOREIGN KEY (`command_id`)
    REFERENCES `appsone_integration_test`.`command_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_service_command_arguments_3`
    FOREIGN KEY (`agent_type_id`)
    REFERENCES `appsone_integration_test`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);


DROP TABLE IF EXISTS `appsone_integration_test`.`agent_mode_configuration`;
CREATE TABLE `appsone_integration_test`.`agent_mode_configuration` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `service_id` INT NOT NULL,
  `agent_type_id` INT NOT NULL,
  `command_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_agent_mode_configuration_1_idx` (`account_id` ASC),
  INDEX `fk_agent_mode_configuration_2_idx` (`service_id` ASC),
  INDEX `fk_agent_mode_configuration_3_idx` (`agent_type_id` ASC),
  INDEX `fk_agent_mode_configuration_4_idx` (`command_id` ASC),
  CONSTRAINT `fk_agent_mode_configuration_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_mode_configuration_2`
    FOREIGN KEY (`service_id`)
    REFERENCES `controller` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_mode_configuration_3`
    FOREIGN KEY (`agent_type_id`)
    REFERENCES `mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_agent_mode_configuration_4`
    FOREIGN KEY (`command_id`)
    REFERENCES `command_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

DROP TABLE IF EXISTS `appsone_integration_test`.`snapshot_levels`;
CREATE TABLE `appsone_integration_test`.`snapshot_levels` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `identifier` VARCHAR(128) NOT NULL,
  `command_mode` VARCHAR(64) NOT NULL,
  `duration_in_mins` INT NOT NULL,
  `no_of_snapshots` INT NOT NULL,
  `silent_window_in_minis` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`));

DROP TABLE IF EXISTS `appsone_integration_test`.`mst_producer_attributes`;
CREATE TABLE `appsone_integration_test`.`mst_producer_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_producer_type_id` INT NOT NULL,
  `attribute_name` VARCHAR(64) NOT NULL,
  `is_custom` VARCHAR(64) NOT NULL,
  `status` VARCHAR(64) NOT NULL,
  `is_mandatory` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `default_value` VARCHAR(256) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_producer_attributes_mst_producer_type`
    FOREIGN KEY (`mst_producer_type_id`)
    REFERENCES `appsone_integration_test`.`mst_producer_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

