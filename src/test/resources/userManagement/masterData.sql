
-- master tables
DROP SCHEMA IF EXISTS `appsone` ;

-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `appsone` ;
USE `appsone` ;

DROP  TABLE IF EXISTS `appsone`.`mst_roles` ;
CREATE TABLE `appsone`.`mst_roles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(32) NOT NULL,
  `description` VARCHAR(256) NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIM<PERSON><PERSON>EY (`id`),
  UNIQUE INDEX `name_UNIQUE` (`name` ASC));

DROP  TABLE IF EXISTS `appsone`.`mst_big_features` ;
CREATE TABLE `appsone`.`mst_big_features` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `description` VARCHAR(256) NULL,
  `status` TINYINT NOT NULL,
  `dashboard_name` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `identifier_UNIQUE` (`identifier` ASC));

DROP  TABLE IF EXISTS `appsone`.`mst_role_feature_mapping` ;
CREATE TABLE `appsone`.`mst_role_feature_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_role_id` INT NOT NULL,
  `mst_big_feature_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_role_feature_mapping_1_idx` (`mst_big_feature_id` ASC),
  INDEX `fk_mst_role_feature_mapping_2_idx` (`mst_role_id` ASC),
  CONSTRAINT `fk_mst_role_feature_mapping_1`
    FOREIGN KEY (`mst_big_feature_id`)
    REFERENCES `mst_big_features` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_role_feature_mapping_2`
    FOREIGN KEY (`mst_role_id`)
    REFERENCES `mst_roles` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

DROP  TABLE IF EXISTS `appsone`.`mst_access_profiles` ;
CREATE TABLE `appsone`.`mst_access_profiles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `mst_role_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `is_custom` TINYINT NOT NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_access_profiles_1_idx` (`mst_role_id` ASC),
  CONSTRAINT `fk_mst_access_profiles_1`
    FOREIGN KEY (`mst_role_id`)
    REFERENCES `mst_roles` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

DROP  TABLE IF EXISTS `appsone`.`mst_access_profile_mapping` ;
CREATE TABLE `appsone`.`mst_access_profile_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_access_profile_id` INT NOT NULL,
  `mst_big_feature_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_access_profile_mapping_1_idx` (`mst_big_feature_id` ASC),
  INDEX `fk_mst_access_profile_mapping_2_idx` (`mst_access_profile_id` ASC),
  CONSTRAINT `fk_mst_access_profile_mapping_1`
    FOREIGN KEY (`mst_big_feature_id`)
    REFERENCES `mst_big_features` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_access_profile_mapping_2`
    FOREIGN KEY (`mst_access_profile_id`)
    REFERENCES `mst_access_profiles` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

create view view_access_details as select a.name access_profile_name, a.id access_profile_id, r.name role_name, r.id role_id, b.name big_feature_name, b.id big_feature_id
from mst_roles r, mst_big_features b, mst_access_profiles a, mst_access_profile_mapping am
where r.id = a.mst_role_id and a.id = am.mst_access_profile_id and am.mst_big_feature_id = b.id
and r.status=1 and a.status = 1 and b.status=1;


DROP  TABLE IF EXISTS `appsone`.`user_attributes` ;
CREATE TABLE `appsone`.`user_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_identifier` VARCHAR(256) NOT NULL,
  `username` VARCHAR(256) NOT NULL,
  `contact_number` VARCHAR(64) NULL,
  `email_address` VARCHAR(64) NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` tinyint NOT NULL,
  `is_timezone_mychoice` tinyint NOT NULL DEFAULT 0,
  `mst_access_profile_id` INT NOT NULL,
  `mst_role_id` INT NOT NULL,
  `is_notifications_timezone_mychoice` tinyint NOT NULL DEFAULT 0,
  `last_login_time` DATETIME,
  `opt_in_status` int DEFAULT '0',
  `opt_in_last_request_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_user_attributes_1_idx` (`mst_access_profile_id` ASC),
  CONSTRAINT `fk_user_attributes_1`
    FOREIGN KEY (`mst_access_profile_id`)
    REFERENCES `mst_access_profiles` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
);

CREATE TABLE `appsone`.`user_access_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_identifier` VARCHAR(256) NOT NULL,
  `access_details` text NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`));

DROP VIEW if exists view_types;
DROP TABLE IF EXISTS `appsone`.`account` ;
DROP TABLE IF EXISTS `appsone`.`mst_type` ;
DROP TABLE IF EXISTS `appsone`.`tag_details` ;
DROP TABLE IF EXISTS `appsone`.`tag_mapping` ;
DROP TABLE IF EXISTS `appsone`.`controller` ;
DROP TABLE IF EXISTS `appsone`.`mst_timezone` ;
DROP TABLE IF EXISTS `appsone`.`service_configurations` ;

-- -----------------------------------------------------
-- Table `appsone`.`account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`account` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_type`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_type_account1_idx` ON `appsone`.`mst_type` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`mst_sub_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_sub_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_sub_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_sub_type_mst_type`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_sub_type_mst_type_idx` ON `appsone`.`mst_sub_type` (`mst_type_id` ASC);

CREATE INDEX `mst_sub_type_account1_idx` ON `appsone`.`mst_sub_type` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`tag_details`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`tag_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `tag_type_id` INT NOT NULL,
  `is_predefined` TINYINT NOT NULL,
  `ref_table` VARCHAR(64) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `ref_where_column_name` VARCHAR(128) NULL,
  `ref_select_column_name` VARCHAR(126) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id`
    FOREIGN KEY (`tag_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_details_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`tag_mapping`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`tag_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `tag_id` INT NOT NULL,
  `object_id` INT NOT NULL,
  `object_ref_table` VARCHAR(256) NOT NULL,
  `tag_key` VARCHAR(256) NOT NULL,
  `tag_value` VARCHAR(256) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id`
    FOREIGN KEY (`tag_id`)
    REFERENCES `appsone`.`tag_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_mapping_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`controller`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`controller` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `controller_type_id` INT NOT NULL,
  `monitor_enabled` TINYINT NOT NULL DEFAULT 1,
  `status` TINYINT(4) NOT NULL DEFAULT 1,
  `plugin_supr_interval` INT NOT NULL DEFAULT '0',
  `plugin_whitelist_status` TINYINT NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_application_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_timezone`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_timezone` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `time_zone_id` VARCHAR(128) NOT NULL,
  `timeoffset` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_timezone_account1_idx` ON `appsone`.`mst_timezone` (`account_id` ASC);

CREATE TABLE `appsone`.`a1_installation_attributes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL,
  `value` varchar(256) DEFAULT NULL,
  `user_details_id` varchar(128) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO `a1_installation_attributes` (`id`, `name`, `value`, `user_details_id`, `created_time`, `updated_time`) VALUES ('3', 'SetupType', 'AD Integrated', '1e0ed2a6-c260-45ae-8f50-5f06a67b84c6', '2020-02-05 00:00:00', '2020-02-05 00:00:00');
INSERT INTO `a1_installation_attributes` (`id`, `name`, `value`, `user_details_id`, `created_time`, `updated_time`) VALUES ('4', 'DefaultCredentials', 'LN0W7lTZIO9846EQcz3Vrg==', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-13 00:00:00', '2020-07-13 00:00:00');
