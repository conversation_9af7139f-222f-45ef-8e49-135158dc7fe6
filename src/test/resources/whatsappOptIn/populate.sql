INSERT INTO account (id,name,created_time,updated_time,status,user_details_id,private_key,public_key,identifier) VALUES (1,'Global','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAWoEJHwA9hOkSIOEjRfjhLg+JM5F43Hy8TfQ0YXUIyV/3dMS+JnNf+mBS4RfTsfEb1i2uL6hn7cXU491iuLt54f1BLshQjfoAcGBSuBBAAnoYGVA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQBPxDDWJdKBdAjgQoC62jaS9YGt4ZIQNjswquEtORgAScGqobCarv5D6UTQuxae/GY4EFR+jXulYFNG96+TF6hNe2oSV/5K6AFzg4uGgoCwXD7PJS1E48Peh85aP7fNiKJaqbjw7LlmkFEAPZlhLBddQVxkj8zBU1aRDKodyviw8v4TCLjhqNRPnm1mIq9qSM=','e573f852-5057-11e9-8fd2-b37b61e52317');
INSERT INTO account (id,name,created_time,updated_time,status,private_key,public_key,user_details_id,identifier) VALUES (2,'QA-TEST','2019-09-24 09:37:20','2019-09-24 09:37:20',1,'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAG+fxMvtzrscbU1Ugo3LNatZyQcgYF4X9aFwGsDj0n1wVoLPN9mRNwXOxqN4xyxjYllga+QpXsuNes5bqZY13aor8yIpeOWaoAcGBSuBBAAnoYGVA4GSAAQDyjSK6FNbguvBXpnCdqOy2QYAs5H3bYxKZV/2TbBdAleEQsotc0FrbFQeD+BpJWavlAef4sD8YmvcFMrZw3mcyjZmRv3Qj80GsAQ2rNODLPwfQgrkse6bZsJctpVloRzr/w/UXlnHrfTpZ1MZIPFzpEBOA++HOmxIbR0E9xyRuQSwxfq8RdgXfvJyKwhhIaA=','MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQDyjSK6FNbguvBXpnCdqOy2QYAs5H3bYxKZV/2TbBdAleEQsotc0FrbFQeD+BpJWavlAef4sD8YmvcFMrZw3mcyjZmRv3Qj80GsAQ2rNODLPwfQgrkse6bZsJctpVloRzr/w/UXlnHrfTpZ1MZIPFzpEBOA++HOmxIbR0E9xyRuQSwxfq8RdgXfvJyKwhhIaA=','7640123a-fbde-4fe5-9812-581cd1e3a9c1','d681ef13-d690-4917-jkhg-6c79b-12');

INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (1,'(GMT-12:00) International Date Line West','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (2,'(GMT-11:00) Midway Island, Samoa','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (3,'(GMT-10:00) Hawaii','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (4,'(GMT-09:00) Alaska','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (5,'(GMT-08:00) Pacific Time (US and Canada); Tijuana','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (6,'(GMT-07:00) Mountain Time (US and Canada)','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (7,'(GMT-07:00) Chihuahua, La Paz, Mazatlan','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (8,'(GMT-07:00) Arizona','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (9,'(GMT-06:00) Central Time (US and Canada','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (10,'(GMT-06:00) Saskatchewan','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (11,'(GMT-06:00) Guadalajara, Mexico City, Monterrey','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (12,'(GMT-06:00) Central America','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (13,'(GMT-05:00) Eastern Time (US and Canada)','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (14,'(GMT-05:00) Indiana (East)','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (15,'(GMT-05:00) Bogota, Lima, Quito','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (16,'(GMT-04:00) Atlantic Time (Canada)','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (17,'(GMT-04:00) Caracas, La Paz','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (18,'(GMT-04:00) Santiago','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (19,'(GMT-03:30) Newfoundland and Labrador','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (20,'(GMT-03:00) Brasilia','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (21,'(GMT-03:00) Buenos Aires, Georgetown','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (22,'(GMT-03:00) Greenland','-********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (23,'(GMT-02:00) Mid-Atlantic','-7200000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (24,'(GMT-01:00) Azores','-3600000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (25,'(GMT-01:00) Cape Verde Islands','-3600000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (26,'(GMT) Greenwich Mean Time: Dublin, Edinburgh, Lisbon, London','0','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (27,'(GMT) Casablanca, Monrovia','0','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (28,'(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague','3600000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (29,'(GMT+01:00) Sarajevo, Skopje, Warsaw, Zagreb','3600000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (30,'(GMT+01:00) Brussels, Copenhagen, Madrid, Paris','3600000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (31,'(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna','3600000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (32,'(GMT+01:00) West Central Africa','3600000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (33,'(GMT+02:00) Bucharest','7200000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (34,'(GMT+02:00) Cairo','7200000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (35,'(GMT+02:00) Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius','7200000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (36,'(GMT+02:00) Athens, Istanbul, Minsk','7200000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (37,'(GMT+02:00) Jerusalem','7200000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (38,'(GMT+02:00) Harare, Pretoria','7200000','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (39,'(GMT+03:00) Moscow, St. Petersburg, Volgograd','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (40,'(GMT+03:00) Kuwait, Riyadh','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (41,'(GMT+03:00) Nairobi','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (42,'(GMT+03:00) Baghdad','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (43,'(GMT+03:30) Tehran','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (44,'(GMT+04:00) Abu Dhabi, Muscat','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (45,'(GMT+04:00) Baku, Tbilisi, Yerevan','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (46,'(GMT+04:30) Kabul','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (47,'(GMT+05:00) Ekaterinburg','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (48,'(GMT+05:00) Islamabad, Karachi, Tashkent','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (49,'(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (50,'(GMT+05:45) Kathmandu','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (51,'(GMT+06:00) Astana, Dhaka','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (52,'(GMT+06:00) Sri Jayawardenepura','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (53,'(GMT+06:00) Almaty, Novosibirsk','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (54,'(GMT+06:30) Yangon Rangoon','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (55,'(GMT+07:00) Bangkok, Hanoi, Jakarta','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (56,'(GMT+07:00) Krasnoyarsk','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (57,'(GMT+08:00) Beijing, Chongqing, Hong Kong SAR, Urumqi','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (58,'(GMT+08:00) Kuala Lumpur, Singapore','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (59,'(GMT+08:00) Taipei','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (60,'(GMT+08:00) Perth','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (61,'(GMT+08:00) Irkutsk, Ulaanbaatar','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (62,'(GMT+09:00) Seoul','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (63,'(GMT+09:00) Osaka, Sapporo, Tokyo','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (64,'(GMT+09:00) Yakutsk','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (65,'(GMT+09:30) Darwin','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (66,'(GMT+09:30) Adelaide','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (67,'(GMT+10:00) Canberra, Melbourne, Sydney','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (68,'(GMT+10:00) Brisbane','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (69,'(GMT+10:00) Hobart','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (70,'(GMT+10:00) Vladivostok','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (71,'(GMT+10:00) Guam, Port Moresby','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (72,'(GMT+11:00) Magadan, Solomon Islands, New Caledonia','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (73,'(GMT+12:00) Fiji Islands, Kamchatka, Marshall Islands','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (74,'(GMT+12:00) Auckland, Wellington','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (75,'(GMT+13:00) Nuku alofa','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_timezone (id,time_zone_id,timeoffset,created_time,updated_time,user_details_id,account_id) VALUES (76,'(GMT+14:00) Kiritimati Island','********','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (1,'Agent','Agent type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (2,'Application','Application type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (931, 'ForensicCmds', 'Type of forenisc commands.', '2019-12-12 00:00:00', '2019-12-12 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (3,'Attribute_Type','Attributes field type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (4,'Availability_DataType','Availability KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (5,'Cluster_Operation','Cluster operations','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (6,'Communication_Endpoint','Communication endpoints of the data server','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (7,'Core_DataType','Core KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (8,'Forensic_DataType','Forensic KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (9,'HTTPMethod','HTTP Method types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (10,'KPI','KPI type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (11,'IntegerKpiUnits','Integer type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (12,'FloatKpiUnits','Float type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (13,'TextKpiUnits','Text type KPI units','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (14,'Transaction','Transaction types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (15,'Transaction_Attributes','Transaction attribute types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (16,'Transaction_Respone_Type','Transaction response time types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (17,'Object_Type','Object types for tags','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (18,'Apache Protocol','Types of protocol for Apache HTTPD','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (19,'Connect With','Oracle connectivity type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (20,'JDBC_Parameter_Type','JDBC Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (21,'SCRIPT_Parameter_Type','SSH Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (22,'WMI_Parameter_Type','Shell Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (23,'JMX_Parameter_Type','JMX Parameter Type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (24,'ConfigWatch_DataType','Config watch KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (25,'FileWatch_DataType','File watch KPI datatype','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (26,'QueryResult','Result type of JDBC queries','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (27,'PSAgentOperationMode','Operation modes for PSAgent','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (28,'ComponentAgentOperationMode','Operation modes for Component Agent','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (29,'ResponseOptions','Options for PSAgent response data','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (30,'ServerDetailProtocols','PSAgent supported protocols for data capture','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (31,'HTTPProxyProtocol','PSAgent proxy protocols','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (32,'HTTPDataParts','Transaction data parts for HTTP','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (33,'TCPDataParts','Transaction data parts for TCP','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (34,'QueryParam','Extractors for query parameters','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (35,'Header','Extractors for headers','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (36,'HTTPRequestBody','Extractors for httprequest body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (37,'HTTPResponseBody','Extractors for httpresponse body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (38,'TCPRequestBody','Extractors for tcprequest body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (39,'TCPResponseBody','Extractors for tcpresponse body','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (40,'UIDashboardTypes','Various dashboard types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (41,'UIPodOperation','Operation for UI Pods','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (42,'MethodType','Method type for JIM transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (43,'TransformationType','Transformation type for JIM transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (44,'ResponseTimeType','Transaction response time type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (45,'CommonPlaceholders','Placeholders for sms/email notification','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (46,'DayOptions','Option for all days in a week','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (47,'Days','All days in a week','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (48,'Operations','Operations type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (49,'SMTP Security','SMTP Security','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (50,'TransactionStatus','Status of a transaction','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (51,'TransactionKPITypes','Various thresholds type for a transaction','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (52,'SMSGatewayProtocols','SMS Gateway Protocols','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (53,'HTTPSMSRequestMethods','HTTP SMS Request Parameter Types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (54,'SMSParameterTypes','SMS Parameter Types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (57,'SMSPlaceHolders','SMS Placeholders','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (58,'CorePlaceHolders','Placeholder for core kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (59,'AvailabilityPlaceHolders','Placeholder for availability kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (60,'FileWatchPlaceHolders','Placeholder for file watch kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (61,'ConfigWatchPlaceHolders','Placeholder for config watch kpi type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (62,'TransactionPlaceHolders','Placeholder for transactions','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (63,'UserRegistrationType','User registration type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (64,'UserRegistrationTypePlaceHolders','Placeholder for user registration type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (65,'UserLockStatus','User lock status type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (66,'JMXAttributeDataType','JMX attribute type for different set of attribute values','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (67,'SecuredType','Security type for JPPF, value should be either TRUE or FALSE','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (68,'JPPFType','JPPF type for server and node','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (69,'TagType','Data details of tag types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (70,'ControllerType','Controller types based on installation','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (71,'AggregationType','Group KPI collation based on types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (72,'XPTFlowDropDown','KPIs for XPT flow level','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (73,'XPTStepDropDown','KPIs for XPT step level','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (74,'RuleType','Rule pattern types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (75,'PayloadType','HTTP Payload types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (76,'PairType','HTTP key value types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (77,'JIMExitType','JIM exit types','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (78,'SupervisorType','Type of supervisors','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (79,'CommandExecType','Execution method to run commands','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (80,'CommandType','Command type for execution','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (81,'StandardType','Type for distinction between custom or OOB','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (82,'CommandOutputType','Type of output of the command','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (83,'DownloadType','Type of download files','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (84,'JIMExitFramework','Type of JIM exit frameworks','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (85,'ForensicExecType','Type of execution by forensics','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (86,'AppsoneComponents','List of Appsone components','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (87,'AgentDataType','Type of agents based on load/behaviour KPI type','2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (88,'ThresholdType', 'Type of thresholds for data points', '2019-08-09 00:00:00','2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (89,'AvailabilityOperations', 'Operations type for availability', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (90,'JIMAgentMode', 'Type of modes for JIM Agent.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (91,'TemplateType', 'Type of notification templates.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (92,'MaintenanceCmds', 'Type of maintenance commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (93,'ForensicCmds', 'Type of forenisc commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (94,'DiscoveryCmds', 'Type of discovery commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (95,'CannedCmds', 'Type of canned commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (96,'ConfigurationCmds', 'Type of configuration commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (97,'HealthCmds', 'Type of health commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (98,'AgentOperationCmds', 'Type of agent operation commands.', '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 1);
INSERT INTO mst_type (id,type,description,created_time,updated_time,user_details_id,account_id,status) VALUES (99,'RollUpOperation','RollUp operations','2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,1);
INSERT INTO mst_type(id, type, description, created_time, updated_time, user_details_id, account_id, status) VALUES ('100', 'HealActionCmds', 'Type of HEAL action commands.', '2020-03-18 00:00:00', '2020-03-18 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', '1');
INSERT INTO mst_type(id, type, description, created_time, updated_time, user_details_id, account_id, status) VALUES ('101', 'Actions', 'Action Scripts', '2020-03-18 00:00:00', '2020-03-18 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', '1');
INSERT INTO mst_type(id, type, description, created_time, updated_time, user_details_id, account_id, status) VALUES ('102', 'NotificationType', 'Type of Notification', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', '1');
INSERT INTO mst_type(id, type, description, created_time, updated_time, user_details_id, account_id, status) VALUES ('103', 'SignalSeverityType', 'Signal Severity Type', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', '1');
INSERT INTO mst_type(id, type, description, created_time, updated_time, user_details_id, account_id, status) VALUES ('104', 'SignalType', 'Signal Type', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', '1');

INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (1,'ComponentAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Component Agent is the agent to collect the KPI data for Components',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (2,'JIMAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Java Intrusive Monitoring Agent is the agent to collect the KPI data for the Java Applications',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (3,'PSAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol Stack Agent is the agent to collect the KPI data for the Transactions',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (4,'NoOpAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dummy agent for no use',0,0);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (5,'SyntheticAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for synthetic monitoring',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (6,'DotNet',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dotnet based Application',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (7,'Finacle10',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Finacle_10 based Applications',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (8,'Finacle7',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Finacle_7 based Application',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (9,'Flexcube',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Flexcube based Application',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (10,'Java',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Java based Application',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (11,'WebServices',2,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Web services based Application',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (12,'TextBox',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (13,'DropDown',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (14,'CheckBox',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (15,'Password',3,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Attribute field type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (16,'Integer',4,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Availability datatype',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (17,'Sum',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Sum the KPI values for Cluster Operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (18,'Average',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Average the KPI values for the Cluster Operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (19,'None',5,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Do nothing on the KPI values for the Cluster Operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (20,'GRPC',6,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'GRPC will be used for the Communication Endpoint',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (21,'Float',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Float datatype for Core KPIs with Decimal values',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (22,'Integer',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Integer datatype for Core KPIs with Numeric values',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (23,'Text',7,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Core KPIs with Alphanumeric values',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (24,'Text',8,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Forensic KPIs with Alphanumeric values',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (25,'GET',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP GET Method type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (26,'POST',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP POST Method type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (27,'UPDATE',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP UPDATE Method type',0,0);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (28,'DELETE',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP DELETE Method type',0,0);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (29,'Availability',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the availability data',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (30,'Core',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the core data',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (31,'Forensic',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the forensic data',0,0);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (32,'ConfigWatch',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the configuration data',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (33,'FileWatch',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Type to collect the data for file changes',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (34,'Bytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (35,'Count',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (36,'Gigabytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (37,'Kilobytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (38,'Megabytes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (39,'Microseconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (40,'Milliseconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (41,'Minutes',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (42,'Seconds',11,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (43,'Percentage',12,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (44,'Text',13,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI unit',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (45,'TCP',14,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP Transaction type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (46,'HTTP',14,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Transaction type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (47,'Body',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Body Patterns',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (48,'Header',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Header Patterns',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (49,'QueryParams',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Query Parameters',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (50,'TCPData',15,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP Data Patterns',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (51,'DC',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DC Response Type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (52,'EUE',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EUE Response Type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (53,'RENDER',16,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'RENDER Response Type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (54,'Application',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Application tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (55,'Component',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Components tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (56,'ComponentInstance',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Component Instances tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (57,'KPI',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPIs tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (58,'Cluster',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Clusters tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (59,'Producer',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Procuers tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (60,'Transaction',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transactions tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (61,'Agents',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (62,'http',18,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP will be used for collecting Apache HTTP KPIs',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (63,'https',18,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTPS will be used for collecting Apache HTTP KPIs',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (64,'ServiceName',19,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'ServiceName will be used for collecting Oracle KPIs',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (65,'SID',19,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SID will be used for collecting Oracle KPIs',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (66,'KEY_VALUE',20,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Key value pair argument for JDBC',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (67,'COMMANDLINE',21,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for SSH',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (68,'STANDARDINPUT',21,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for SSH',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (69,'COMMANDLINE',22,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for Shell',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (70,'KEY_VALUE',23,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Key value pair argument for JMX',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (71,'Float',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Float datatype for Config KPIs with Decimal values',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (72,'Integer',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Integer datatype for Config KPIs with Numeric values',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (73,'Text',24,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for Config watch KPIs with Alphanumeric values',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (74,'Text',25,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Text datatype for File watch KPIs with Alphanumeric values',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (75,'NAMEVALUE',26,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query result type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (76,'RESULTSET',26,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query result type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (77,'Remote',27,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Online operation mode for PSAgent',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (78,'Local',27,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Offline operation mode for PSAgent',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (79,'Remote',28,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Online operation mode for Component Agent',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (80,'Local',28,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Offline operation mode for Component Agent',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (81,'ResponseHeader',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response header option',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (82,'ResponseBody',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response body option',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (83,'StatusLine',29,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'No response data',0,0);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (84,'HTTP',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for HTTP data',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (85,'HTTPS',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for HTTPS data',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (86,'TCP-DIR',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for TCP data',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (87,'FINICORE',30,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Protocol for Finacle data',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (88,'HTTP',31,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'PSAgent proxy protocol',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (89,'QueryParam',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Query parameters',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (90,'Header',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP headers',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (91,'RequestBody',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (92,'ResponseBody',32,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP response body',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (93,'RequestBody',33,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (94,'ResponseBody',33,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (95,'Query Param Extractor',34,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query param extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (96,'Grouped Regex Extractor',35,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Header extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (97,'Grouped Regex Extractor',36,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body regex extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (98,'Form Data Extractor',36,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP request body form extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (99,'Grouped Regex Extractor',37,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP response body regex extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (100,'Grouped Regex Extractor',38,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body regex extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (101,'Position And Length Extractor',38,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP request body position length extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (102,'Grouped Regex Extractor',39,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body regex extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (103,'Position And Length Extractor',39,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP response body position length extractor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (104,'Application Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for applications',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (105,'NOC Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for NOC',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (106,'JIM Dashboard',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for JIM',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (107,'Synthetic Monitoring',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for Synthetic Monitoring',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (108,'BVE',40,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Dashboard for BVE',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (109,'Default',41,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Default pod operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (110,'Custom',41,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Custom pod operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (111,'ENTRY',42,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Entry type method',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (112,'EXIT',42,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Exit type method',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (113,'GENERIC',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Generic transformation type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (114,'SERVLET',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Servlet transformation type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (115,'SQL',43,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query transformation type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (116,'Transaction',10,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transaction KPI Type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (117,'SeverityProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Severity alert profile type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (118,'NotificationContentProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Notification alert profile type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (119,'EscalationProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Esacalation alert profile type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (120,'AlertProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Alert profile',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (121,'DC',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DC transaction time type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (122,'EUM',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'EUM transaction time type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (123,'BOTH',44,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Both transaction time type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (124,'{Account}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Account',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (125,'{Application}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Application',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (126,'{Severity}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Severity',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (127,'{ComponentInstance}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component Instance',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (128,'{Component}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (129,'{ComponentType}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Component Type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (130,'{KPI}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (131,'{KPIType}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI Type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (132,'{ActualValue}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Actual Value',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (133,'{ThresholdValue}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Threshold Value',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (134,'{Time}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Time',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (135,'{AlertID}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Alert Id',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (136,'{EscalationLevel}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Escalation Level',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (137,'{ViolationDetails}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for Violation Details',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (138,'{KPI Group}',45,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for KPI Group',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (139,'Daily',46,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'All days in a week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (140,'Days',46,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Day in a week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (141,'Monday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'1st day of the week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (142,'Tuesday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'2nd day of the week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (143,'Wednesday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'3rd day of the week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (144,'Thursday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'4th day of the week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (145,'Friday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'5th day of the week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (146,'Saturday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'6th day of the week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (147,'Sunday',47,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'7th day of the week',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (148,'TimeProfile',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Tags for coverage window',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (149,'greater than',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Greater than operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (150,'lesser than',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Less than operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (151,'NONE',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as NONE',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (152,'SSL',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as SSL',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (153,'TLS',49,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMTP security as TLS',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (154,'Good',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Good status of a transaction',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (155,'Slow',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Slow status of a transaction',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (156,'Fail',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Fail status of a transaction',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (157,'Unknown',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Unknown status of a transaction',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (158,'Timedout',50,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Timedout status of a transaction',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (159,'Slow Percentage',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Total volume threshold for transactions',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (160,'Fail Percentage',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transaction status threshold for transactions',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (161,'HTTP',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP SMS Gateway Protocol',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (162,'TCP',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP SMS Gateway Protocol',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (163,'GET',53,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP GET SMS Request Method',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (164,'POST',53,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP POST SMS Request Method',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (165,'QueryParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query parameter for SMS Request',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (166,'RequestParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Request parameter for SMS Request',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (167,'TcpParameter',54,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'TCP parameter for SMS Request',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (168,'{MobileNumber}',57,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Mobile number placeholder for SMS',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (169,'{SMSContent}',57,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SMS content placeholder for SMS',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (170,'UserRegistration',63,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User registration',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (171,'UserResetPassword',63,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Password reset for users',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (172,'{Username}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for username',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (173,'{Password}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for user password',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (174,'{Account}',64,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Placeholder for user account',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (175,'Locked',65,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User account is locked',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (176,'Unlocked',65,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'User account is unlocked',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (177,'GrpcSettings',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Grpc Settings',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (178,'KPIGroup',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KPI Group',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (179,'HolidayProfiles',17,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Holiday Profiles',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (180,'HTTPS',52,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTPS SMS Gateway Protocol',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (181,'DirectValue',66,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'DirectValue type for JMX attributes',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (182,'CompositeValue',66,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'CompositeValue type for JMX attributes',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (183,'Yes',67,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Security type TRUE for JPPF security enable',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (184,'No',67,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Security type FALSE for JPPF security enable',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (185,'Server',68,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JPPF type is server',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (186,'Node',68,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JPPF type is node',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (187,'ENV_PARAM',22,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for Shell',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (188,'LogForwarder',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Log forwarder agent',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (189,'KeyValue',69,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'KeyValue tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (190,'String',69,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Plain text tag type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (191,'Application',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Application controller type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (192,'Services',70,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Service controller type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (193,'not between',48,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Less than minimum value and greater than maximum value',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (194,'Total Volume',51,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Total volume of transactions',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (195,'SingleValue',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Single value aggregation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (196,'MultiValue',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Multi value aggregation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (197,'None',71,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'No aggregation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (198,'Transacted Value',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Transacted KPI',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (199,'Conversion',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Conversion KPI',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (200,'Business Error',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Business error KPI',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (201,'Arrival Rate',72,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Arrival rate KPI',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (202,'Response Time (ms)',73,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Response time KPI',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (203,'Pass throughs (%)',73,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Passthrough KPI',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (204,'OPTIONS',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP OPTIONS Method type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (205,'PUT',9,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP PUT Method type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (206,'Regex',74,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Regex rule type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (207,'Request Data',74,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Request data rule type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (208,'Form Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Form data http payload type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (209,'XML Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'XML data http payload type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (210,'JSON Data',75,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JSON data http payload type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (211,'Cookie',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Cookie key value pair type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (212,'Query Parameters',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Query parameter key value pair type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (213,'HTTP Header',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP Header key value pair type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (214,'PayloadType',76,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Payload content type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (215,'JDBC',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JDBC exit type for JIM',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (216,'HTTP',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'HTTP exit type for JIM',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (217,'Queue',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Queue exit type for JIM',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (218,'ForensicAgent',1,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for foresic actions',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (219,'WindowsSupervisor',78,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Windows based supervisor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (220,'UnixSupervisor',78,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Unix based supervisor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (221,'RestClient',85,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'REST client based command execution',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (222,'Script',85,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Script based command execution',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (223,'LongPolling',79,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Long polling based command execution',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (224,'Install',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform installation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (225,'Upgrade',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform upgradeation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (226,'UnInstall',92,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform uninstall',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (227,'Running',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform stop',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (228,'Stopped',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform start',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (229,'Restart',98,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform restart',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (230,'Execute',93,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command to perform execute',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (231,'Custom',81,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Type to identify custom component',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (232,'OOB',81,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Type to identify OOB component',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (233,'Blob',82,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Output contents in blob',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (234,'File',82,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Output contents in a file',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (235,'PDF',83,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'PDF download type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (236,'CSV',83,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'CSV download type',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (237,'JDBC',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Oracle JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (240,'JAX_WS_CLIENT',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JAX WS client JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (241,'IBM_MQ',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'IBM MQ JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (242,'APACHE_HTTP_CLIENT1',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Apache HTTP client JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (243,'APACHE_HTTP_CLIENT2',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Apache HTTP client JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (244,'AXIS2_CLIENT',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'AXIS2 client JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (245,'JDBC_CONNECTION',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MSSQL JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (246,'JMS',84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JMS JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (247,'Supervisor',86,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Appsone supervisor',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (248,'SupervisorController',86,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Appsone supervisor controller',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (249,'Workload',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents for load KPIs',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (250,'Behaviour',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agents for behaviour KPIs',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (251,'WorkNBehaviour',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for both load and behaviour KPIs',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (252,'Others',87,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for other than load and behaviour KPIs',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (253,'Baseline', 88, '2019-08-09 00:00:00','2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Baseline threshold to be discovered by MLE', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (254,'Static', 88, '2019-08-09 00:00:00','2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Static threshold to be defined by users', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (255,'in between', 48, '2019-08-09 00:00:00','2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'In Between operation', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (256,'not equals', 89, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Value is not equals.', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (257,'Auto', 96, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running as Auto mode.', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (258,'Verbose', 96, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running in Verbose mode.', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (259,'SwitchOff', 96, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running in SwitchOff mode.', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (260,'Problem', 91, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running as Auto mode.', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (261,'ProblemImapacts', 91, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running in Verbose mode.', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (262,'Anomaly', 91, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Agent will be running in SwitchOff mode.', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (263,'Realtime', 88, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'Realtime threshold to be discovered by MLE', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (264,'EJB_WEBLOGIC_REMOTE', 84, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'EJB Weblogic exit framework', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (265,'Weblogic_EJB', 77, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'EJB Weblogic exit type for JIM', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (266,'EJB Data', 74, '2019-08-09 00:00:00', '2019-08-09 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 1, 'EJB rule type', 0, 1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (267,'MYSQL_STATEMENT', 84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MySQL Statement JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (268,'MYSQL_PREPARED_STATEMENT', 84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MySQL Prepared Statement JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (269,'ORACLE', 84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Oracle JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (270,'MSSQL', 84,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'MSSQL JIM exit framework',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (271,'NEW-JDBC',77,'2019-08-09 00:00:00','2019-08-09 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'JDBC exit type for JIM',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (272,'AgentOperations',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent operations type for command type.',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (273,'Maintenance',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Maintenance type for command type.',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (274,'Forensic',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Forensic type for command type.',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (275,'Discovery',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Discovery type for command type.',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (276,'CannedCommands',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Canned type for command type.',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (277,'Configuration',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Configuration type for command type.',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (278,'Health',80,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Health type for command type.',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (279,'SelfHeal',98,'2019-12-10 00:00:00','2019-12-10 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'SelfHeal for command type.',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (280,'Sum',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Sum the KPI values for rollup Operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (281,'Average',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Average the KPI values for the rollup Operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (282,'None',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Do nothing on the KPI values for the rollup Operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (283,'Max',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Max of KPI values for the rollup Operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (284,'Last',99,'2020-01-06 00:00:00','2020-01-06 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Last fo KPI values for the rollup Operation',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (285,'COMMAND_OPTIONS',21,'2020-01-24 00:00:00', '2020-01-24 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Standard input argument for SSH',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (286,'COMMAND_OPTIONS',22,'2020-01-24 00:00:00', '2020-01-24 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Command line argument for Shell',0,1);
INSERT INTO mst_sub_type (id,name,mst_type_id,created_time,updated_time,user_details_id,account_id,description,is_custom,status) VALUES (287,'Custom',1,'2020-01-06 00:00:00','2020-01-06 00:00:00' ,'7640123a-fbde-4fe5-9812-581cd1e3a9c1',1,'Agent for custom',0,1);
INSERT INTO mst_sub_type (id, name, mst_type_id, created_time, updated_time, user_details_id, account_id, description, is_custom, status) VALUES ('291', 'Immediately', '102', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Notification Type details.', '0', '1');
INSERT INTO mst_sub_type (id, name, mst_type_id, created_time, updated_time, user_details_id, account_id, description, is_custom, status) VALUES ('292', 'Open for long', '102', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Notification Type details.', '0', '1');
INSERT INTO mst_sub_type (id, name, mst_type_id, created_time, updated_time, user_details_id, account_id, description, is_custom, status) VALUES ('293', 'Open for too long', '102', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Notification Type details.', '0', '1');
INSERT INTO mst_sub_type (id, name, mst_type_id, created_time, updated_time, user_details_id, account_id, description, is_custom, status) VALUES ('294', 'Off', '102', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Notification Type details.', '0', '1');
INSERT INTO mst_sub_type(id, name, mst_type_id, created_time, updated_time, user_details_id, account_id, description, is_custom, status) VALUES ('295', 'Severe', '103', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Signal Severity Type details.', '0', '1');
INSERT INTO mst_sub_type(id, name, mst_type_id, created_time, updated_time, user_details_id, account_id, description, is_custom, status) VALUES ('296', 'Default', '103', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Signal Severity Type details.', '0', '1');
INSERT INTO mst_sub_type(id, name, mst_type_id, created_time, updated_time, user_details_id, account_id, description, is_custom, status) VALUES ('297', 'Signal', '104', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Signal Type details.', '0', '1');
INSERT INTO mst_sub_type(id, name, mst_type_id, created_time, updated_time, user_details_id, account_id, description, is_custom, status) VALUES ('298', 'Early Warning', '104', '2020-05-11 00:00:00', '2020-05-11 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '1', 'Signal Type details.', '0', '1');

INSERT INTO tag_details (id,name,tag_type_id,is_predefined,ref_table,ref_select_column_name,ref_where_column_name,created_time,updated_time,account_id,user_details_id) VALUES (1,'Controller',190,1,'controller','id tagKey, identifier tagValue','identifier = :name and account_id = :accountId','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_details (id,name,tag_type_id,is_predefined,ref_table,ref_select_column_name,ref_where_column_name,created_time,updated_time,account_id,user_details_id) VALUES (2,'Timezone',190,1,'mst_timezone','id tagKey, timeoffset tagValue','(time_zone_id = :name or id =:name) and account_id = 1','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_details (id,name,tag_type_id,is_predefined,ref_table,ref_select_column_name,ref_where_column_name,created_time,updated_time,account_id,user_details_id) VALUES (3,'Account',190,1,'account','id tagKey, identifier tagValue','id = :name','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_details (id,name,tag_type_id,is_predefined,ref_table,ref_select_column_name,ref_where_column_name,created_time,updated_time,account_id,user_details_id) VALUES (4,'LayerName',189,0,'NULL','NULL','NULL','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_details (id,name,tag_type_id,is_predefined,ref_table,ref_select_column_name,ref_where_column_name,created_time,updated_time,account_id,user_details_id) VALUES (5,'Category',190,1,'mst_category_details','id tagKey, identifier tagValue','identifier = :name and account_id = :accountId','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_details (id,name,tag_type_id,is_predefined,ref_table,ref_select_column_name,ref_where_column_name,created_time,updated_time,account_id,user_details_id) VALUES(6, 'Agent', 190,1, 'agent','id tagKey, unique_token tagValue','unique_token = :name','2019-08-09 00:00:00', '2019-08-09 00:00:00', 1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (1,5,1,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (2,5,2,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (3,5,3,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (4,5,4,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (5,5,5,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (6,5,6,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (7,5,7,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (8,5,8,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (9,5,9,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (10,5,10,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (11,5,11,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (12,5,12,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (13,5,13,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (14,5,14,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (15,5,15,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (16,5,16,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (17,5,17,'mst_kpi_details',4,'Disk Space','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (18,5,18,'mst_kpi_details',4,'Disk Space','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (19,5,19,'mst_kpi_details',4,'Disk Space','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (20,5,20,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (21,5,21,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (22,5,22,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (23,5,23,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (24,5,24,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (25,5,25,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (26,5,26,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (27,5,27,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (28,5,28,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (29,5,29,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (30,5,30,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (31,5,31,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (32,5,32,'mst_kpi_details',6,'Process','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (33,5,33,'mst_kpi_details',6,'Process','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (34,5,34,'mst_kpi_details',6,'Process','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (35,5,35,'mst_kpi_details',6,'Process','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (36,5,36,'mst_kpi_details',6,'Process','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (37,5,37,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (38,5,38,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (39,5,39,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (40,5,40,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (41,5,41,'mst_kpi_details',6,'Process','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (42,5,42,'mst_kpi_details',6,'Process','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (43,5,43,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (44,5,44,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (45,5,45,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (46,5,46,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (47,5,47,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (48,5,48,'mst_kpi_details',9,'Uptime','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (49,5,49,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (50,5,50,'mst_kpi_details',7,'File Handler','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (51,5,51,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (52,5,52,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (53,5,53,'mst_kpi_details',70,'ZFSStoragePool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (54,5,54,'mst_kpi_details',3,'Disk IO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (55,5,55,'mst_kpi_details',8,'FinacleUniser','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (56,5,56,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (57,5,57,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (58,5,58,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (59,5,59,'mst_kpi_details',16,'WebserverCPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (60,5,60,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (61,5,61,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (62,5,62,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (63,5,63,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (64,5,64,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (65,5,65,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (66,5,66,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (67,5,67,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (68,5,68,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (69,5,69,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (70,5,70,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (71,5,71,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (72,5,72,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (73,5,73,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (74,5,74,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (75,5,75,'mst_kpi_details',11,'WebserverRequests','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (76,5,76,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (77,5,77,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (78,5,78,'mst_kpi_details',12,'WebserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (79,5,79,'mst_kpi_details',13,'WebserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (80,5,80,'mst_kpi_details',14,'WebserverSession','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (81,5,81,'mst_kpi_details',14,'WebserverSession','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (82,5,82,'mst_kpi_details',15,'WebserverJSPs','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (83,5,83,'mst_kpi_details',15,'WebserverJSPs','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (84,5,84,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (85,5,85,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (86,5,86,'mst_kpi_details',18,'AppserverCPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (87,5,87,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (88,5,88,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (89,5,89,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (90,5,90,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (91,5,91,'mst_kpi_details',20,'AppserverConnPool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (92,5,92,'mst_kpi_details',20,'AppserverConnPool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (93,5,93,'mst_kpi_details',20,'AppserverConnPool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (94,5,94,'mst_kpi_details',20,'AppserverConnPool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (95,5,95,'mst_kpi_details',20,'AppserverConnPool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (96,5,96,'mst_kpi_details',20,'AppserverConnPool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (97,5,97,'mst_kpi_details',20,'AppserverConnPool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (98,5,98,'mst_kpi_details',20,'AppserverConnPool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (99,5,99,'mst_kpi_details',21,'AppserverTransaction','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (100,5,100,'mst_kpi_details',21,'AppserverTransaction','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (101,5,101,'mst_kpi_details',21,'AppserverTransaction','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (102,5,102,'mst_kpi_details',22,'AppserverSessions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (103,5,103,'mst_kpi_details',22,'AppserverSessions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (104,5,104,'mst_kpi_details',22,'AppserverSessions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (105,5,105,'mst_kpi_details',23,'AppserverServlets','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (106,5,106,'mst_kpi_details',23,'AppserverServlets','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (107,5,107,'mst_kpi_details',24,'AppserverEJB','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (108,5,108,'mst_kpi_details',24,'AppserverEJB','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (109,5,109,'mst_kpi_details',24,'AppserverEJB','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (110,5,110,'mst_kpi_details',24,'AppserverEJB','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (111,5,111,'mst_kpi_details',24,'AppserverEJB','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (112,5,112,'mst_kpi_details',25,'AppserverNetwork','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (113,5,113,'mst_kpi_details',21,'AppserverTransaction','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (114,5,114,'mst_kpi_details',21,'AppserverTransaction','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (115,5,115,'mst_kpi_details',21,'AppserverTransaction','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (116,5,116,'mst_kpi_details',21,'AppserverTransaction','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (117,5,117,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (118,5,118,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (119,5,119,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (120,5,120,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (121,5,121,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (122,5,122,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (123,5,123,'mst_kpi_details',26,'AppserverRequest','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (124,5,124,'mst_kpi_details',26,'AppserverRequest','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (125,5,125,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (126,5,126,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (127,5,127,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (128,5,128,'mst_kpi_details',26,'AppserverRequest','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (129,5,129,'mst_kpi_details',27,'AppserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (130,5,130,'mst_kpi_details',27,'AppserverIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (131,5,131,'mst_kpi_details',26,'AppserverRequest','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (132,5,132,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (133,5,133,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (134,5,134,'mst_kpi_details',22,'AppserverSessions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (135,5,135,'mst_kpi_details',22,'AppserverSessions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (136,5,136,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (137,5,137,'mst_kpi_details',18,'AppserverCPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (138,5,138,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (139,5,139,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (140,5,140,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (141,5,141,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (142,5,142,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (143,5,143,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (144,5,144,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (145,5,145,'mst_kpi_details',29,'DBThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (146,5,146,'mst_kpi_details',29,'DBThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (147,5,147,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (148,5,148,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (149,5,149,'mst_kpi_details',30,'DBLocks','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (150,5,150,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (151,5,151,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (152,5,152,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (153,5,153,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (154,5,154,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (155,5,155,'mst_kpi_details',41,'DBConnections','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (156,5,156,'mst_kpi_details',32,'DBSpace','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (157,5,157,'mst_kpi_details',32,'DBSpace','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (158,5,158,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (159,5,159,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (160,5,160,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (161,5,161,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (162,5,162,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (163,5,163,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (164,5,164,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (165,5,165,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (166,5,166,'mst_kpi_details',33,'DBSQL','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (167,5,167,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (168,5,168,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (169,5,169,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (170,5,170,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (171,5,171,'mst_kpi_details',34,'DBCache','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (172,5,172,'mst_kpi_details',35,'DBPhysicalReads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (173,5,173,'mst_kpi_details',36,'DBPhysicalWrites','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (174,5,174,'mst_kpi_details',45,'DBLogicalReads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (175,5,175,'mst_kpi_details',37,'DBDiskIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (176,5,176,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (177,5,177,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (178,5,178,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (179,5,179,'mst_kpi_details',38,'DBSessions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (180,5,180,'mst_kpi_details',39,'DBCPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (181,5,181,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (182,5,182,'mst_kpi_details',37,'DBDiskIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (183,5,183,'mst_kpi_details',37,'DBDiskIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (184,5,184,'mst_kpi_details',40,'DBTablescans','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (185,5,185,'mst_kpi_details',40,'DBTablescans','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (186,5,186,'mst_kpi_details',30,'DBLocks','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (187,5,187,'mst_kpi_details',40,'DBConnections','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (188,5,188,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (189,5,189,'mst_kpi_details',40,'DBConnections','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (190,5,190,'mst_kpi_details',42,'DBAsyncWrites','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (191,5,191,'mst_kpi_details',43,'DBSyncReads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (192,5,192,'mst_kpi_details',35,'DBPhysicalReads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (193,5,193,'mst_kpi_details',37,'DBDiskIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (194,5,194,'mst_kpi_details',37,'DBDiskIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (195,5,195,'mst_kpi_details',37,'DBDiskIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (196,5,196,'mst_kpi_details',44,'DBSyncWrites','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (197,5,197,'mst_kpi_details',44,'DBSyncWrites','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (198,5,198,'mst_kpi_details',45,'DBLogicalReads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (199,5,199,'mst_kpi_details',45,'DBLogicalReads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (200,5,200,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (201,5,201,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (202,5,202,'mst_kpi_details',33,'DBSQL','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (203,5,203,'mst_kpi_details',33,'DBSQL','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (204,5,204,'mst_kpi_details',46,'DBTransactions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (205,5,205,'mst_kpi_details',47,'DBExecutionTime','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (206,5,206,'mst_kpi_details',30,'DBLocks','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (207,5,207,'mst_kpi_details',30,'DBLocks','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (208,5,208,'mst_kpi_details',48,'DBWaitTime','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (209,5,209,'mst_kpi_details',30,'DBLocks','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (210,5,210,'mst_kpi_details',30,'DBLocks','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (211,5,211,'mst_kpi_details',40,'DBTablescans','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (212,5,212,'mst_kpi_details',40,'DBTablescans','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (213,5,213,'mst_kpi_details',40,'DBTablescans','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (214,5,214,'mst_kpi_details',40,'DBTablescans','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (215,5,215,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (216,5,216,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (217,5,217,'mst_kpi_details',46,'DBTransactions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (218,5,218,'mst_kpi_details',46,'DBTransactions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (219,5,219,'mst_kpi_details',46,'DBTransactions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (220,5,220,'mst_kpi_details',46,'DBTransactions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (221,5,221,'mst_kpi_details',46,'DBTransactions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (222,5,222,'mst_kpi_details',49,'DBMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (223,5,223,'mst_kpi_details',33,'DBSQL','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (224,5,224,'mst_kpi_details',33,'DBSQL','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (225,5,225,'mst_kpi_details',50,'DBSize','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (226,5,226,'mst_kpi_details',30,'DBLocks','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (227,5,227,'mst_kpi_details',37,'DBDiskIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (228,5,228,'mst_kpi_details',37,'DBDiskIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (229,5,229,'mst_kpi_details',50,'DBSize','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (230,5,230,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (231,5,231,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (232,5,232,'mst_kpi_details',51,'DBWorkload','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (233,5,233,'mst_kpi_details',30,'DBLocks','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (234,5,234,'mst_kpi_details',48,'DBWaitTime','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (235,5,235,'mst_kpi_details',52,'DBPages','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (236,5,236,'mst_kpi_details',52,'DBPages','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (237,5,237,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (238,5,238,'mst_kpi_details',46,'DBTransactions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (239,5,239,'mst_kpi_details',46,'DBTransactions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (240,5,240,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (241,5,241,'mst_kpi_details',31,'DBExecutions','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (242,5,242,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (243,5,243,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (244,5,244,'mst_kpi_details',28,'DBIO','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (245,5,245,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (246,5,246,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (247,5,247,'mst_kpi_details',71,'ModifiedStatus','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (248,5,248,'mst_kpi_details',72,'FileFolderExists','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (249,5,249,'mst_kpi_details',54,'DBLog','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (250,5,250,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (251,5,251,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (252,5,252,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (253,5,253,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (254,5,254,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (255,5,255,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (256,5,256,'mst_kpi_details',53,'DBAvailability','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (257,5,257,'mst_kpi_details',55,'ZFS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (258,5,258,'mst_kpi_details',55,'ZFS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (259,5,259,'mst_kpi_details',56,'Config','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (260,5,260,'mst_kpi_details',1,'CPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (261,5,261,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (262,5,262,'mst_kpi_details',56,'Config','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (263,5,263,'mst_kpi_details',56,'Config','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (264,5,264,'mst_kpi_details',56,'Config','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (265,5,265,'mst_kpi_details',56,'Config','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (266,5,266,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (267,5,267,'mst_kpi_details',2,'Memory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (268,5,268,'mst_kpi_details',57,'Threads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (269,5,272,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (270,5,273,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (271,5,274,'mst_kpi_details',58,'Request','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (272,5,275,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (273,5,276,'mst_kpi_details',59,'AppServerApplications','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (274,5,277,'mst_kpi_details',59,'AppServerApplications','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (275,5,278,'mst_kpi_details',60,'AppServerJDBC','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (276,5,279,'mst_kpi_details',60,'AppServerJDBC','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (277,5,280,'mst_kpi_details',60,'AppServerJDBC','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (278,5,281,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (279,5,282,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (280,5,283,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (281,5,284,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (282,5,285,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (283,5,286,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (284,5,287,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (285,5,288,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (286,5,289,'mst_kpi_details',61,'AppServerJMS','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (287,5,290,'mst_kpi_details',56,'Config','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (288,5,291,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (289,5,292,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (290,5,293,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (291,5,294,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (292,5,295,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (293,5,296,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (294,5,297,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (295,5,298,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (296,5,299,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (297,5,300,'mst_kpi_details',62,'AppServerDataSource','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (298,5,301,'mst_kpi_details',63,'Nodes','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (299,5,302,'mst_kpi_details',63,'Nodes','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (300,5,303,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (301,5,304,'mst_kpi_details',17,'AppserverMemory','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (302,5,305,'mst_kpi_details',18,'AppserverCPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (303,5,306,'mst_kpi_details',19,'AppserverThreads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (304,5,307,'mst_kpi_details',64,'Node Management','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (305,5,308,'mst_kpi_details',64,'Node Management','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (306,5,309,'mst_kpi_details',64,'Node Management','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (307,5,310,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (308,5,311,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (309,5,312,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (310,5,313,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (311,5,314,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (312,5,315,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (313,5,316,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (314,5,317,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (315,5,318,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (316,5,319,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (317,5,320,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (318,5,321,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (319,5,322,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (320,5,323,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (321,5,324,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (322,5,325,'mst_kpi_details',18,'AppserverCPU','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (323,5,326,'mst_kpi_details',64,'Node Management','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (324,5,327,'mst_kpi_details',64,'Node Management','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (325,5,328,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (326,5,329,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (327,5,330,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (328,5,331,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (329,5,332,'mst_kpi_details',65,'Task Monitoring','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (330,5,333,'mst_kpi_details',65,'Task Monitoring','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (331,5,334,'mst_kpi_details',65,'Task Monitoring','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (332,5,335,'mst_kpi_details',65,'Task Monitoring','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (333,5,336,'mst_kpi_details',65,'Task Monitoring','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (334,5,337,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (335,5,338,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (336,5,339,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (337,5,340,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (338,5,341,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (339,5,342,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (340,5,343,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (341,5,344,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (342,5,345,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (343,5,346,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (344,5,347,'mst_kpi_details',66,'Connection Pool','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (345,5,348,'mst_kpi_details',57,'Threads','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (346,5,349,'mst_kpi_details',5,'Network','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (347,5,350,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (348,5,351,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (349,5,352,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (350,5,353,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (351,5,354,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (352,5,355,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (353,5,356,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (354,5,357,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (355,5,358,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (356,5,359,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (357,5,360,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (358,5,361,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (359,5,362,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (360,5,363,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (361,5,364,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (362,5,365,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (363,5,366,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (364,5,367,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (365,5,368,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (366,5,369,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (367,5,370,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (368,5,371,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (369,5,372,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (370,5,373,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (371,5,374,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (372,5,375,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (373,5,376,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (374,5,377,'mst_kpi_details',73,'PatternCount','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (375,5,378,'mst_kpi_details',4,'Disk Space','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (376,5,379,'mst_kpi_details',74,'DiskLatency','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (377,5,380,'mst_kpi_details',74,'DiskLatency','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (378,5,381,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (379,5,382,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (380,5,383,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (381,5,384,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (382,5,385,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (383,5,386,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (384,5,387,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (385,5,388,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (386,5,389,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (387,5,390,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (388,5,391,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (389,5,392,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (390,5,393,'mst_kpi_details',75,'FetchPingStatus','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (391,5,394,'mst_kpi_details',75,'FetchPingStatus','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (392,5,395,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (393,5,396,'mst_kpi_details',10,'Others','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (394,5,397,'mst_kpi_details',67,'ResponseTime','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (395,5,398,'mst_kpi_details',68,'Volume','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (396,5,399,'mst_kpi_details',67,'ResponseTime','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (397,5,400,'mst_kpi_details',69,'Errors','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (398,5,401,'mst_kpi_details',69,'Errors','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (399,5,402,'mst_kpi_details',67,'ResponseTime','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (400,5,403,'mst_kpi_details',69,'Errors','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (401,5,404,'mst_kpi_details',69,'Errors','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (402,5,405,'mst_kpi_details',69,'Errors','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (403,5,406,'mst_kpi_details',76,'Success','2019-08-09 00:00:00','2019-08-09 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (404,5,407,'mst_kpi_details',54,'DBLog','2019-10-01 00:00:00','2019-10-01 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (405,5,408,'mst_kpi_details',77,'WeblogicLogs','2019-10-01 00:00:00','2019-10-01 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (406,5,409,'mst_kpi_details',78,'FinacleServerLogs','2019-10-01 00:00:00','2019-10-01 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (407,5,410,'mst_kpi_details',32,'DBSpace','2019-10-04 00:00:00','2019-10-04 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (408,5,411,'mst_kpi_details',32,'DBSpace','2019-10-04 00:00:00','2019-10-04 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (409,5,412,'mst_kpi_details',32,'DBSpace','2019-10-04 00:00:00','2019-10-04 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (410,5,413,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (411,5,414,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (412,5,415,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (413,5,416,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (414,5,417,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (415,5,418,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (416,5,419,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (417,5,420,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (418,5,421,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (419,5,422,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (420,5,423,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (421,5,424,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (422,5,425,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (423,5,426,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (424,5,427,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (425,5,428,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (426,5,429,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (427,5,430,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (428,5,431,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (429,5,432,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (430,5,433,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (431,5,434,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (432,5,435,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (433,5,436,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO tag_mapping (id,tag_id,object_id,object_ref_table,tag_key,tag_value,created_time,updated_time,account_id,user_details_id) VALUES (434,5,437,'mst_kpi_details',79,'TopNSQLByPhysicalReads','2019-12-12 00:00:00','2019-12-12 00:00:00',1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO `tag_mapping` (`id`, `tag_id`, `object_id`, `object_ref_table`, `tag_key`, `tag_value`, `created_time`, `updated_time`, `account_id`, `user_details_id`) VALUES (435, 2, 2, 'account', 49, '********', '2019-09-24 09:37:20', '2019-09-24 09:37:20', '1', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO `account` (`id`, `name`, `created_time`, `updated_time`, `status`, `private_key`, `public_key`, `user_details_id`, `identifier`) VALUES ('3', 'Singapore', '2020-07-09 11:26:43', '2020-07-09 11:26:43', '1', 'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAAOXWGg6g080nE/6f1q8Baq4cUs0qZllWAdRKMcZwoMNxL9kE/4vuD6TCHqanVWufJN6gBQiOd17IHkRx4L3e7JVgQ+yYeaYoAcGBSuBBAAnoYGVA4GSAAQELcwaztJ34S9cmBP4GtjCcThntOgMi6d1L0xi2TPN6vt1MZAnIt/PM/gVEkoIhR6IFXC1uCF3+nybSB87KkoJ3ARDF0193bMBs7uDXbmybcgz95YaoC20pAj4oZ8DETNIgBPw2F4mhuUcjBn8YJ/6+XSjyANjtcezCMbgbYlq3BvucYuCxldKLohTDQK0I+8=', 'MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQELcwaztJ34S9cmBP4GtjCcThntOgMi6d1L0xi2TPN6vt1MZAnIt/PM/gVEkoIhR6IFXC1uCF3+nybSB87KkoJ3ARDF0193bMBs7uDXbmybcgz95YaoC20pAj4oZ8DETNIgBPw2F4mhuUcjBn8YJ/6+XSjyANjtcezCMbgbYlq3BvucYuCxldKLohTDQK0I+8=', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'd681ef13-d690-4917-jkhg-6c79b-2');
INSERT INTO `account` (`id`, `name`, `created_time`, `updated_time`, `status`, `private_key`, `public_key`, `user_details_id`, `identifier`) VALUES ('4', 'Alaska', '2020-07-09 11:27:43', '2020-07-09 11:27:43', '1', 'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAxlZP4w+td8S2DDvVCQuVKq2Yu2xgYWU6fWKxZOEpxDawdlm/SEBROT2ZoR0uP8qVWez0FkGMWTfFFOJZr34Jmn92TPg2oiAoAcGBSuBBAAnoYGVA4GSAAQESVhTReE9wO8jGD1tRNA+nfOG8PeSW/2pghgGrtQtvTMVhb0Aa1CH0J7Gw4YmdstEB/FyQKdYbkLZX4NtBlwbfznUQAEOEa8BVdL+bjGqsTiNh59Ur+ugjbQG/hVZbGEmUcZNcUcC1a4NXT+/J48Ocn5PxObfp/ZeEH2V1j6yYJpH1a5WWgTjS0+0IHxgZQQ=', 'MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQESVhTReE9wO8jGD1tRNA+nfOG8PeSW/2pghgGrtQtvTMVhb0Aa1CH0J7Gw4YmdstEB/FyQKdYbkLZX4NtBlwbfznUQAEOEa8BVdL+bjGqsTiNh59Ur+ugjbQG/hVZbGEmUcZNcUcC1a4NXT+/J48Ocn5PxObfp/ZeEH2V1j6yYJpH1a5WWgTjS0+0IHxgZQQ=', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'd681ef13-d690-4917-jkhg-6c79b-3');
INSERT INTO `account` (`id`, `name`, `created_time`, `updated_time`, `status`, `private_key`, `public_key`, `user_details_id`, `identifier`) VALUES ('5', 'ACME-EStore', '2020-07-09 11:28:47', '2020-07-09 11:28:47', '1', 'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIATlYpgQ7xM5ltL7TUcEkNPtqXGNBEUUdcutfp+PB38lRzFzc/DMMGZrAO0sugufhV+JtDufvmFE7oJi9TdTKevg5Zw/xZglioAcGBSuBBAAnoYGVA4GSAAQAc3Pzh17i3migQGfXLiS9r/7nNhf33ujF2MLLra8/4moFEZCobACvK4PD2XZs7oeSOrHrA54sKyEQi74JyPMsz8iYTmSC3jgAktkqkksxi7XJQLoi1yi010PIYz6NH7IotULwPLfyyy4RbQb0v0VfCBDxKQPx0Y9sSHqpqmLDrVcOpW3kpnNY48BSB6lhLxg=', 'MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQAc3Pzh17i3migQGfXLiS9r/7nNhf33ujF2MLLra8/4moFEZCobACvK4PD2XZs7oeSOrHrA54sKyEQi74JyPMsz8iYTmSC3jgAktkqkksxi7XJQLoi1yi010PIYz6NH7IotULwPLfyyy4RbQb0v0VfCBDxKQPx0Y9sSHqpqmLDrVcOpW3kpnNY48BSB6lhLxg=', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'c681ef13-d690-4917-jkhg-6c79b-1');
INSERT INTO `account` (`id`, `name`, `created_time`, `updated_time`, `status`, `private_key`, `public_key`, `user_details_id`, `identifier`) VALUES ('6', 'Adelaide', '2020-07-09 11:30:36', '2020-07-09 11:30:36', '1', 'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIANGjnTaDoCqroXz/4bOutg7iqriDrwtLL10Tnu/w+WUZYCYp22D4nX8Fo5tWcnw9jKcJxEGIawvvucZ1/n7F9zyd0tM46Y+MoAcGBSuBBAAnoYGVA4GSAAQA3g39NCMaeLRvicxaZaHHBAIqpkzEQ2xkmMV7V3BXDTZJQ8nR5wXXgl4EZyWWz/DDIHn+059FOxKhbPb/rj0P1Ez6QORijvYG6Sqdvy3v0TZ2MED41/ldzVF9I2QfsjGgEs2MTKTsbddjhNzqxvywTul2JvZvhcWHsFM8L8hEladhqX1nUq3DItBL/ZmA6v8=', 'MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQA3g39NCMaeLRvicxaZaHHBAIqpkzEQ2xkmMV7V3BXDTZJQ8nR5wXXgl4EZyWWz/DDIHn+059FOxKhbPb/rj0P1Ez6QORijvYG6Sqdvy3v0TZ2MED41/ldzVF9I2QfsjGgEs2MTKTsbddjhNzqxvywTul2JvZvhcWHsFM8L8hEladhqX1nUq3DItBL/ZmA6v8=', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'j681ef13-d690-4917-jkhg-6c79b-1');
INSERT INTO `account` (`id`, `name`, `created_time`, `updated_time`, `status`, `private_key`, `public_key`, `user_details_id`, `identifier`) VALUES ('7', 'USA', '2020-07-23 12:25:43', '2020-07-23 12:25:43', '1', 'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIACHLBcacb7KnO/3H1CzXNo8BMUFLMh2ETHaGeYaiAobRmFFUBZwpXOhEzGsdOVB2JhiClDo9GJzXn9hj5x5U1i7n+WMG56CLoAcGBSuBBAAnoYGVA4GSAAQAGKIKj70anAl19V811zdeXQM8sHU2GgH0JCqTmJMYV1ISLMJSgFVh/zzMh0809eAkFIsaiswM1th3zEp+tARTfgKS5mdtOJEGBrj7dw6R/xxH7786LAAVUffLy8r/aOQpwJmCyBQ9qYb3MrHKkW/QmvGcEGhmp8QLXsT+IL+PPuOyixoZHTuCTb+P2fl2t9g=', 'MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQAGKIKj70anAl19V811zdeXQM8sHU2GgH0JCqTmJMYV1ISLMJSgFVh/zzMh0809eAkFIsaiswM1th3zEp+tARTfgKS5mdtOJEGBrj7dw6R/xxH7786LAAVUffLy8r/aOQpwJmCyBQ9qYb3MrHKkW/QmvGcEGhmp8QLXsT+IL+PPuOyixoZHTuCTb+P2fl2t9g=', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'd681ef13-d690-4917-jkhg-6c79b-4');
INSERT INTO `account` (`id`, `name`, `created_time`, `updated_time`, `status`, `private_key`, `public_key`, `user_details_id`, `identifier`) VALUES ('8', 'Test', '2020-07-24 13:51:00', '2020-07-24 13:51:00', '1', 'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAUb5NXQrmetP/8U8HdKIN5w+ggUK5S3R8r6Pb6+bIWYBbGxsCGTB1ItbfMT0PH5Pl+IBmEt23EVIiSPOlb45Zj1SIhgfCxgaoAcGBSuBBAAnoYGVA4GSAAQEE87DQ2dulMk27fND+F44I0Ty0yCKn40GDZEPiUrnRbOb5E16y78Ry5uhzD4a0UktDJHpTHR8CzmHvCtO1hsGaO2E2uxIOFYHfAgY1hUoX3SnZ43O2hLAfNHxP3gjTDALOiCz/A+jrlWDCfZfMkj6sShJb4GWsqWGJ1ebsYbrCW21Num4nC4oto9TnsS8Lm8=', 'MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQEE87DQ2dulMk27fND+F44I0Ty0yCKn40GDZEPiUrnRbOb5E16y78Ry5uhzD4a0UktDJHpTHR8CzmHvCtO1hsGaO2E2uxIOFYHfAgY1hUoX3SnZ43O2hLAfNHxP3gjTDALOiCz/A+jrlWDCfZfMkj6sShJb4GWsqWGJ1ebsYbrCW21Num4nC4oto9TnsS8Lm8=', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'd681ef13-d690-4917-jkhg-6c79b-5');
INSERT INTO `account` (`id`, `name`, `created_time`, `updated_time`, `status`, `private_key`, `public_key`, `user_details_id`, `identifier`) VALUES ('9', 'Testing123', '2020-07-27 19:43:47', '2020-07-27 19:43:47', '1', 'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIATfd32WnwiT73nKSGnnbUoXTqiyHbGtzkpHnBtP3wDPgLTwUUnjhsgQ1Ca286F7y0iJVrS/YQA6/7k9NWHLW4MLabaqXH5qRoAcGBSuBBAAnoYGVA4GSAAQFMc4lP6/e26lvYN0xl1W3DhoOLwittspFo2HRIuaMGBtUSWM7JhAbXLPtFq7m+z9QUY0sTqYVGQVnbqwfgEXQ7ep7PRLjEpMAerUb/XoYV0JYiedacYXmKcggC5FxPNOabBC7ZStC121+pc7uAoLyjO0ogF3PPIp6bDHff9wtNaZOtChizzqH+PnbPk5JrLA=', 'MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQFMc4lP6/e26lvYN0xl1W3DhoOLwittspFo2HRIuaMGBtUSWM7JhAbXLPtFq7m+z9QUY0sTqYVGQVnbqwfgEXQ7ep7PRLjEpMAerUb/XoYV0JYiedacYXmKcggC5FxPNOabBC7ZStC121+pc7uAoLyjO0ogF3PPIp6bDHff9wtNaZOtChizzqH+PnbPk5JrLA=', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', 'bba4d9ae-9539-4a08-83cd-61d02c38b044');
INSERT INTO `account` (`id`, `name`, `created_time`, `updated_time`, `status`, `private_key`, `public_key`, `user_details_id`, `identifier`) VALUES ('10', 'Audit-tester', '2020-08-04 12:29:50', '2020-08-04 12:29:50', '1', 'MIIBCQIBADAQBgcqhkjOPQIBBgUrgQQAJwSB8TCB7gIBAQRIAif8KQdlCkNbpxwcGYF1+Iqfo+AXMWHopEIE7ei4BLQ7pQ/Sq3oASQRsRou/y5GCOG5heWHVQBnmwt8D++D49NdUPzluJGegoAcGBSuBBAAnoYGVA4GSAAQH7q36PCeZCLMetVVpNgPCRKXuKYy69fpVNZ+VAMOXF/G3BG3EkoHBWNFwGZx4yKnYGPnneuVENPxc6uLZC7RU3fflpOQgLpEGTmGh1kXRIWdqDb9JYpOj16DO2iGuFxBZyCwi5TLj/TRGUZO+ZSIWrBKF2baAcdtQNf/AhJIsgq1n0aHAjbG2en/whIEH9dw=', 'MIGnMBAGByqGSM49AgEGBSuBBAAnA4GSAAQH7q36PCeZCLMetVVpNgPCRKXuKYy69fpVNZ+VAMOXF/G3BG3EkoHBWNFwGZx4yKnYGPnneuVENPxc6uLZC7RU3fflpOQgLpEGTmGh1kXRIWdqDb9JYpOj16DO2iGuFxBZyCwi5TLj/TRGUZO+ZSIWrBKF2baAcdtQNf/AhJIsgq1n0aHAjbG2en/whIEH9dw=', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '7262f4d6-756d-4f7d-b876-eea1db74e15a');

INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('1', 'NetBanking', 'netbanking_1', '2', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-09 05:53:43', '2020-07-09 05:53:43', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('9', 'LOS', 'los_2', '2', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-09 05:53:46', '2020-07-09 05:53:46', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('14', 'ENET', 'enet_3', '2', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-09 05:53:47', '2020-08-04 11:20:21', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('19', 'MicroBanking', 'microbanking_1', '2', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-09 05:53:47', '2020-07-09 05:53:47', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('23', 'CoreBanking', 'corebanking_5', '3', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-09 05:56:49', '2020-07-09 05:56:49', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('28', 'ECOREBanking', 'ecorebanking_6', '4', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-09 05:57:49', '2020-07-09 05:57:49', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('33', 'OfBizzzz', 'OfBiz', '5', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-09 05:58:53', '2020-07-23 11:22:48', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('38', 'UPI', 'UPI', '5', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-09 05:58:53', '2020-07-09 05:58:53', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('51', 'OnlineBanking-123', 'Onlinebanking_5', '6', '11f40006-ccb6-4fab-8fc9-c296f317d175', '2020-07-09 06:00:42', '2020-08-04 09:16:42', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('57', 'Audit-Trigger-191-2323', 'Audit-Trigger-191-Test', '6', '11f40006-ccb6-4fab-8fc9-c296f317d175', '2020-07-22 06:00:42', '2020-08-04 08:57:51', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('58', 'Netbanking', 'd0e51469-454f-4531-a51b-8fb0d2189156', '5', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-23 09:14:41', '2020-07-23 09:14:41', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('60', 'HAhafdaf', '3f2d43b8-2676-485b-b401-4dc5c2081619', '2', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-07-27 13:11:32', '2020-07-27 13:11:32', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('69', 'TestApp-123', '474ae122-5ff9-433d-94f1-2a6f0d22debf', '9', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-03 09:02:03', '2020-08-03 09:41:06', '191', '1', '1');
INSERT INTO `controller` (`id`, `name`, `identifier`, `account_id`, `user_details_id`, `created_time`, `updated_time`, `controller_type_id`, `monitor_enabled`, `status`) VALUES ('70', 'Tester', '167c5084-989d-4d2b-b675-cfdca4083baa', '6', '11f40006-ccb6-4fab-8fc9-c296f317d175', '2020-08-04 07:54:09', '2020-08-04 07:54:09', '191', '1', '1');


-- This table have predefined entiries of HEAL roles.

INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (1, 'Super Admin', 'One who initially installs Heal, has complete access to all accounts per installation.', 1, 0, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (2, 'Admin', 'One or more individual, having access to one or multiple Big features/application (read/write/edit access).', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (3, 'User Manager', 'One or more individual, having access to only Admin_Users section in Control Center.', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_roles (id, name, description, status, ui_visible, created_time, updated_time, user_details_id) VALUES (4, 'User', 'One or more individual, who has access to Heal dashboard only. Access will be a combination of Big Feature and Application level.', 1, 1, '2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

-- This table have predefined entiries of all the big features in HEAL.

INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (1, 'Account Management', 'Account_Management', 'Add/Edit/Delete', 1, 'ControlCenter','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (2, 'Account Monitoring', 'Account_Monitoring', 'Edit account level settings', 1, 'ControlCenter','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (3, 'User Management', 'User_Management', 'Add/Edit/Delete', 1, 'ControlCenter','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (4, 'Audit Trail', 'Audit_Trail', 'View all the configuration changes in system.', 1, 'ControlCenter','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (5, 'Setup Monitoring', 'Setup_Monitoring', 'Setup Monitoring.', 1, 'ControlCenter','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (6, 'Setup Topology', 'Setup_Topology', 'Setup Topology.', 1, 'ControlCenter','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (7, 'Health Monitoring', 'Health_Monitoring', 'Data Health.', 1, 'ControlCenter','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (8, 'Control Center External API', 'External_CC_API', 'External APIs.', 1, 'ControlCenter','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (9, 'Service Maps', 'Service_Maps', 'Service Maps.', 1, 'HealUI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (10, 'Service Applications', 'Service_Applications', 'Service Applications.', 1, 'HealUI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (11, 'Experience Transactions', 'Experiences_transactions', 'Experiences transactions.', 1, 'HealUI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (12, 'Experience Search', 'Experiences_search', 'Experiences Search.', 1, 'HealUI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (13, 'Signal Details', 'Signal_Details', 'Signals Details.', 1, 'HealUI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (14, 'HEAL UI External API', 'External_HEALUI_API', 'External_HEALUI_APIs.', 1, 'HealUI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (15, 'Metric Data', 'Metric_Data_APIs', 'Metric data APIs.', 1, 'QueryAPI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (16, 'Audit data', 'Audit_Data_APIs', 'Audit data APIs.', 1, 'QueryAPI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_big_features (id, name, identifier, description, status, dashboard_name, created_time, updated_time, user_details_id) VALUES (17, 'Configuration data', 'Configuration_data_APIs', 'Configuration data through APIs.', 1, 'QueryAPI','2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

-- This is mapping table for role and big features.
-- This table have entries for each role which features mapped.
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (1, 1, 1, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (2, 1, 2, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (3, 1, 3, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (4, 1, 4, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (5, 1, 5, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (6, 1, 6, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (7, 1, 7, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (8, 1, 8, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (9, 1, 9, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (10, 1, 10, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (11, 1, 11, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (12, 1, 12, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (13, 1, 13, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (14, 1, 14, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (15, 1, 15, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (16, 1, 16, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (17, 1, 17, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (18, 2, 2, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (19, 2, 4, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (20, 2, 5, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (21, 2, 6, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (22, 2, 7, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (23, 2, 8, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (24, 2, 9, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (25, 2, 10, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (26, 2, 11, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (27, 2, 12, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (28, 2, 13, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (29, 2, 14, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (30, 2, 15, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (31, 2, 16, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (32, 2, 17, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (33, 3, 3, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (34, 3, 4, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (35, 3, 16, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (36, 4, 9, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (37, 4, 10, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (38, 4, 11, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (39, 4, 12, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (40, 4, 13, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (41, 4, 15, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_role_feature_mapping (id, mst_role_id, mst_big_feature_id, created_time, user_details_id) VALUES (42, 4, 17, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

-- This table will have all prefined access profiles in HEAL. Each role can have one or more access profiles.
-- In future we will have creating custom access profiles.

INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (1, 'Super Admin', 1, 1, 0, 1, 0,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (2, 'Heal Admin', 2, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (3, 'Application Owner', 2, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, ui_visible, created_time, updated_time, user_details_id) VALUES (4, 'User Manager', 3, 1, 0, 1, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profiles(id, name, mst_role_id, account_id, is_custom, status, created_time, updated_time, user_details_id) VALUES (5, 'Application User', 4, 1, 0, 1,'2020-07-05 00:00:00', '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

-- This table will have mapping between access profiles and big features.
-- Each access profile will have one or more big features mapped.

INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (1, 2, 2, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (2, 2, 4, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (3, 2, 5, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (4, 2, 6, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (5, 2, 7, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (6, 2, 8, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (7, 2, 9, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (8, 2, 10, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (9, 2, 11, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (10, 2, 12, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (11, 2, 13, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (12, 2, 14, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (13, 2, 15, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (14, 2, 16, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (15, 2, 17, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (16, 3, 4, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (17, 3, 5, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (18, 3, 7, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (19, 3, 8, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (20, 3, 9, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (21, 3, 10, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (22, 3, 11, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (23, 3, 12, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (24, 3, 13, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (25, 3, 14, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (26, 3, 15, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (27, 3, 16, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (28, 3, 17, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (29, 4, 3, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (30, 4, 4, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (31, 5, 9, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (32, 5, 10, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (33, 5, 11, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (34, 5, 12, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (35, 5, 13, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (36, 1, 1, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (37, 1, 2, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (38, 1, 3, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (39, 1, 4, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (40, 1, 5, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (41, 1, 6, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (42, 1, 7, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (43, 1, 8, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (44, 1, 9, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (45, 1, 10, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (46, 1, 11, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (47, 1, 12, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (48, 1, 13, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (49, 1, 14, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (50, 1, 15, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (51, 1, 16, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (52, 1, 17, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (53, 5, 15, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (54, 5, 17, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');
INSERT INTO mst_access_profile_mapping (id, mst_access_profile_id, mst_big_feature_id, created_time, user_details_id) VALUES (55, 4, 16, '2020-07-05 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

-- This table have contact details and access profile for a user.

INSERT INTO `user_attributes` (`id`,`user_identifier`,`contact_number`,`email_address`,`username`,`user_details_id`,`created_time`,`updated_time`,`status`,`is_timezone_mychoice`,`mst_access_profile_id`,`mst_role_id`,`is_notifications_timezone_mychoice`) VALUES (1,'7640123a-fbde-4fe5-9812-581cd1e3a9c1','************',NULL,'appsoneadmin','7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-07-05 00:00:00','2020-07-05 00:00:00',1,0,1,1,0);

-- This table have account access for each user.
-- This table have application access per account for each user.

INSERT INTO user_access_details (id, user_identifier, access_details,created_time,updated_time,user_details_id) VALUES (1, '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '{"accounts":["*"], "accountMapping": {}}','2020-07-05 00:00:00','2020-07-05 00:00:00','7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO `user_attributes` (`id`,`user_identifier`,`contact_number`,`email_address`,`username`,`user_details_id`,`created_time`,`updated_time`,`status`,`is_timezone_mychoice`,`mst_access_profile_id`,`mst_role_id`,`is_notifications_timezone_mychoice`) VALUES (47,'11f40006-ccb6-4fab-8fc9-c296f317d175','**********','<EMAIL>','healadmin','7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-08-01 17:44:35','2020-08-03 15:13:10',1,0,2,2,0);
INSERT INTO `user_attributes` (`id`,`user_identifier`,`contact_number`,`email_address`,`username`,`user_details_id`,`created_time`,`updated_time`,`status`,`is_timezone_mychoice`,`mst_access_profile_id`,`mst_role_id`,`is_notifications_timezone_mychoice`) VALUES (48,'f67bca59-7a8d-4d20-bc8b-c972fe007915','**********','<EMAIL>','appowner','7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-08-01 17:45:43','2020-08-03 15:17:00',1,0,3,2,0);
INSERT INTO `user_attributes` (`id`,`user_identifier`,`contact_number`,`email_address`,`username`,`user_details_id`,`created_time`,`updated_time`,`status`,`is_timezone_mychoice`,`mst_access_profile_id`,`mst_role_id`,`is_notifications_timezone_mychoice`) VALUES (49,'9feea7db-c6eb-4071-828a-ee9f40016dff','**********','<EMAIL>','usermanager','7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-08-01 17:47:04','2020-08-01 17:47:04',1,0,4,3,0);
INSERT INTO `user_attributes` (`id`,`user_identifier`,`contact_number`,`email_address`,`username`,`user_details_id`,`created_time`,`updated_time`,`status`,`is_timezone_mychoice`,`mst_access_profile_id`,`mst_role_id`,`is_notifications_timezone_mychoice`) VALUES (50,'e8c4c018-4eb4-43da-b5b0-dc8409bacbe1','**********','<EMAIL>','appuser','7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-08-01 17:47:38','2020-08-01 17:47:38',1,0,5,4,0);
INSERT INTO `user_attributes` (`id`,`user_identifier`,`contact_number`,`email_address`,`username`,`user_details_id`,`created_time`,`updated_time`,`status`,`is_timezone_mychoice`,`mst_access_profile_id`,`mst_role_id`,`is_notifications_timezone_mychoice`) VALUES (55,'f635c526-d84c-476d-bd7e-dec7182565ce','**********','<EMAIL>','Testing','7640123a-fbde-4fe5-9812-581cd1e3a9c1','2020-08-03 18:32:28','2020-08-04 11:46:33',1,0,3,2,0);
INSERT INTO `user_attributes` (`id`,`user_identifier`,`contact_number`,`email_address`,`username`,`user_details_id`,`created_time`,`updated_time`,`status`,`is_timezone_mychoice`,`mst_access_profile_id`,`mst_role_id`,`is_notifications_timezone_mychoice`) VALUES (56,'46ff60d2-f072-416a-8631-6069fa1fb6c3','**********','<EMAIL>','Test','9feea7db-c6eb-4071-828a-ee9f40016dff','2020-08-04 13:34:54','2020-08-04 13:36:22',0,0,4,3,0);

INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('46', '11f40006-ccb6-4fab-8fc9-c296f317d175', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1", "j681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["*"]}, "j681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["*"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:14:34', '2020-08-03 09:43:10');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('47', 'f67bca59-7a8d-4d20-bc8b-c972fe007915', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["los_2", "netbanking_1", "microbanking_1"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:15:42', '2020-08-03 09:46:59');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('48', '9feea7db-c6eb-4071-828a-ee9f40016dff', '{"accounts": ["*"], "accountMapping": {}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:17:03', '2020-08-01 12:17:03');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('49', 'e8c4c018-4eb4-43da-b5b0-dc8409bacbe1', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["netbanking_1", "los_2", "microbanking_1", "enet_3", "3f2d43b8-2676-485b-b401-4dc5c2081619"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-01 12:17:38', '2020-08-01 12:17:38');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('54', 'f635c526-d84c-476d-bd7e-dec7182565ce', '{"accounts": ["d681ef13-d690-4917-jkhg-6c79b-1", "c681ef13-d690-4917-jkhg-6c79b-1", "j681ef13-d690-4917-jkhg-6c79b-1"], "accountMapping": {"c681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["UPI", "OfBiz"]}, "d681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["microbanking_1", "los_2"]}, "j681ef13-d690-4917-jkhg-6c79b-1": {"applications": ["Audit-Trigger-191-Test"]}}}', '7640123a-fbde-4fe5-9812-581cd1e3a9c1', '2020-08-03 13:02:27', '2020-08-04 06:16:32');
INSERT INTO `user_access_details` (`id`, `user_identifier`, `access_details`, `user_details_id`, `created_time`, `updated_time`) VALUES ('55', '46ff60d2-f072-416a-8631-6069fa1fb6c3', '{"accounts": ["*"], "accountMapping": {}}', '9feea7db-c6eb-4071-828a-ee9f40016dff', '2020-08-04 08:04:53', '2020-08-04 08:06:21');
